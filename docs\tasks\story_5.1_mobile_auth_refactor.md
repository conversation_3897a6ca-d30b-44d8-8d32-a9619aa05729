### User Story 5.1: Implement Mobile Authentication Flow
*   **User Story:** As a mobile app user, I want to log in using the new secure API and have my session securely stored on my device, so I can have a persistent and safe experience.
*   **Acceptance Criteria:**
    *   The mobile app's login, logout, and registration flows are fully functional using the `dukancard` API.
    *   `accessToken`, `refreshToken`, `deviceId`, and `deviceSecret` are correctly stored in the device's secure storage upon login.
    *   The application correctly uses the stored tokens for subsequent API calls.
    *   The silent token refresh mechanism is implemented and working.

---

### Development Tasks

-   [ ] **1. Adapt Client-Side State Management for Mobile**
    *   **Developer's Task:** Review the existing mobile authentication state management solution (e.g., Zustand, React Context). Adapt the auth store and state to handle the new set of tokens and identifiers: `accessToken`, `refreshToken`, `deviceId`, and `deviceSecret`.
    *   **Action:** Implement platform-specific secure storage for these tokens and identifiers (e.g., using `react-native-keychain` for iOS/Android Keychain, or `expo-secure-store` if using Expo).

-   [ ] **2. Refactor Mobile UI Components for Authentication**
    *   **Developer's Task:** Identify all mobile UI components related to authentication, such as login screens, registration forms, and user profile sections with a "Logout" button. Modify their event handlers to call the new API endpoints (`POST /api/auth/login`, `POST /api/auth/logout`) instead of using the Supabase client SDK directly.

-   [ ] **3. Implement Mobile Client-Side Token Refresh Logic**
    *   **Developer's Task:** Implement the logic for silent token refreshing in the mobile app. This typically involves creating an interceptor for your HTTP client (e.g., Axios) or a custom fetch wrapper. This wrapper should:
        1.  Catch `401 Unauthorized` responses from API calls.
        2.  When a `401` is caught, pause the original request and trigger a call to the `/api/auth/refresh` endpoint.
        3.  Securely update the stored tokens with the new ones from the refresh response.
        4.  Retry the original, failed request with the new `accessToken`.

-   [ ] **4. Update Protected Mobile Screens and Navigation**
    *   **Developer's Task:** Review all mobile screens and navigation flows that require a user to be logged in. Update the logic that protects these screens to rely on the new client-side authentication state instead of the old Supabase session object. Ensure that unauthenticated users are correctly redirected to the login screen.