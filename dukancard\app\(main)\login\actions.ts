"use server";

import { z } from "zod";
import { EmailOTPSchema, VerifyOTPSchema, MobilePasswordLoginSchema } from "@/lib/schemas/authSchemas";

// Validate email format
function validateEmail(email: string): { isValid: boolean; message?: string } {
  if (!email) {
    return { isValid: false, message: 'Email is required' };
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return { isValid: false, message: 'Please enter a valid email address' };
  }
  
  return { isValid: true };
}

// Validate OTP format
function validateOTP(otp: string): { isValid: boolean; message?: string } {
  if (!otp) {
    return { isValid: false, message: 'OTP is required' };
  }
  
  if (otp.length !== 6) {
    return { isValid: false, message: 'OTP must be 6 digits' };
  }
  
  if (!/^\d{6}$/.test(otp)) {
    return { isValid: false, message: 'OTP must contain only numbers' };
  }
  
  return { isValid: true };
}

// Send OTP to email using new secure API
export async function sendOTP(values: z.infer<typeof EmailOTPSchema>) {
  const { email } = values;
  const emailValidation = validateEmail(email);
  if (!emailValidation.isValid) {
    return {
      success: false,
      error: emailValidation.message,
    };
  }

  try {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');
    const response = await fetch(`${baseUrl}/api/auth/send-otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email }),
    });

    const data = await response.json();

    if (!response.ok) {
      // Check if this is a configuration error (email rate limit)
      if ('isConfigurationError' in data && data.isConfigurationError) {
        return {
          success: false,
          error: data.error,
          isConfigurationError: true,
        };
      }

      return {
        success: false,
        error: data.error || 'Failed to send OTP',
      };
    }

    return {
      success: true,
      message: data.message || "OTP sent to your email address. Please check your inbox.",
    };
  } catch (error) {
    console.error('Send OTP error:', error);
    return {
      success: false,
      error: 'An unexpected error occurred. Please try again.',
    };
  }
}

// Verify OTP and sign in using new secure API
export async function verifyOTP(values: z.infer<typeof VerifyOTPSchema>) {
  const { email, otp } = values;
  const otpValidation = validateOTP(otp);
  if (!otpValidation.isValid) {
    return {
      success: false,
      error: otpValidation.message,
    };
  }

  try {
    const deviceInfo = {
      deviceName: `Web Browser - ${new Date().toISOString().slice(0, 10)}`,
      platform: 'web' as const,
    };

    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');
    const response = await fetch(`${baseUrl}/api/auth/verify-otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email,
        otp,
        deviceInfo,
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: data.error || 'OTP verification failed',
      };
    }

    // Return the API response data including tokens
    return {
      success: true,
      data: {
        accessToken: data.accessToken,
        refreshToken: data.refreshToken,
        deviceId: data.deviceId,
        deviceSecret: data.deviceSecret,
        hmacKey: data.hmacKey,
        // Legacy format for compatibility (will be removed later)
        user: { id: 'temp-user-id' },
        session: { access_token: data.accessToken }
      },
      message: data.message || "Successfully signed in!",
    };
  } catch (error) {
    console.error('OTP verification error:', error);
    return {
      success: false,
      error: 'An unexpected error occurred. Please try again.',
    };
  }
}

// Mobile + password login using secure API endpoint
export async function loginWithMobilePassword(values: z.infer<typeof MobilePasswordLoginSchema>) {
  const validatedFields = MobilePasswordLoginSchema.safeParse(values);

  if (!validatedFields.success) {
    return {
      success: false,
      error: "Invalid mobile number or password format",
    };
  }

  const { mobile, password } = validatedFields.data;

  try {
    // Format mobile number with +91 prefix as email for our API
    const phoneNumber = `+91${mobile}`;
    
    const deviceInfo = {
      deviceName: `Web Browser - ${new Date().toISOString().slice(0, 10)}`,
      platform: 'web' as const,
    };

    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');
    const response = await fetch(`${baseUrl}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: phoneNumber, // Use phone as email
        password: password,
        deviceInfo,
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: data.error || 'Login failed',
      };
    }

    // Return the API response data including tokens
    return {
      success: true,
      data: {
        accessToken: data.accessToken,
        refreshToken: data.refreshToken,
        deviceId: data.deviceId,
        deviceSecret: data.deviceSecret,
        hmacKey: data.hmacKey,
        // Legacy format for compatibility (will be removed later)
        user: { id: 'temp-user-id' },
        session: { access_token: data.accessToken }
      },
      message: "Successfully signed in!",
    };
  } catch (error) {
    console.error('Mobile login error:', error);
    return {
      success: false,
      error: 'An unexpected error occurred. Please try again.',
    };
  }
}
