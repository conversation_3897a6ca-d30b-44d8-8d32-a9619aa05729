{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\n/**\r\n * Cleans and formats phone number from Supabase auth.users table format\r\n * Handles various formats: +918458060663, 918458060663, 8458060663\r\n * Returns clean 10-digit phone number or null if invalid\r\n *\r\n * @param phone - Phone number from Supabase auth.users table\r\n * @returns Clean 10-digit phone number or null if invalid\r\n */\r\nexport function cleanPhoneFromAuth(phone: string | null | undefined): string | null {\r\n  if (!phone) return null;\r\n\r\n  let processedPhone = phone.trim();\r\n\r\n  // Remove +91 prefix if present\r\n  if (processedPhone.startsWith('+91')) {\r\n    processedPhone = processedPhone.substring(3);\r\n  }\r\n  // Remove 91 prefix if it's a 12-digit number starting with 91\r\n  else if (processedPhone.length === 12 && processedPhone.startsWith('91')) {\r\n    processedPhone = processedPhone.substring(2);\r\n  }\r\n\r\n  // Validate it's a 10-digit number\r\n  if (/^\\d{10}$/.test(processedPhone)) {\r\n    return processedPhone;\r\n  }\r\n\r\n  return null; // Invalid format\r\n}\r\n\r\n/**\r\n * Masks a phone number, showing first and last two digits.\r\n * Example: 9123456789 -> 91******89\r\n * Handles null/undefined/empty strings.\r\n */\r\nexport function maskPhoneNumber(phone: string | null | undefined): string {\r\n  if (!phone || phone.length < 4) {\r\n    return \"Invalid Phone\"; // Or return empty string or original if preferred\r\n  }\r\n  const firstTwo = phone.substring(0, 2);\r\n  const lastTwo = phone.substring(phone.length - 2);\r\n  const maskedPart = \"*\".repeat(phone.length - 4);\r\n  return `${firstTwo}${maskedPart}${lastTwo}`;\r\n}\r\n\r\n/**\r\n * Masks an email address.\r\n * Example: <EMAIL> -> ex****@do****.com\r\n * Handles null/undefined/empty strings.\r\n */\r\nexport function maskEmail(email: string | null | undefined): string {\r\n  if (!email || !email.includes(\"@\")) {\r\n    return \"Invalid Email\"; // Or return empty string or original\r\n  }\r\n  const parts = email.split(\"@\");\r\n  const username = parts[0];\r\n  const domain = parts[1];\r\n\r\n  if (username.length <= 2 || domain.length <= 2 || !domain.includes(\".\")) {\r\n    return \"Email Hidden\"; // Simple mask for very short/invalid emails\r\n  }\r\n\r\n  const maskedUsername =\r\n    username.substring(0, 2) + \"*\".repeat(username.length - 2);\r\n\r\n  const domainParts = domain.split(\".\");\r\n  const domainName = domainParts[0];\r\n  const domainTld = domainParts.slice(1).join(\".\"); // Handle multiple parts like .co.uk\r\n\r\n  const maskedDomainName =\r\n    domainName.substring(0, 2) + \"*\".repeat(domainName.length - 2);\r\n\r\n  return `${maskedUsername}@${maskedDomainName}.${domainTld}`;\r\n}\r\n\r\n/**\r\n * Formats a number using the Indian numbering system with short notations.\r\n * Supports: K (Thousand), L (Lakh), Cr (Crore), Ar (Arab), Khar (Kharab), Neel, Padma, Shankh, etc.\r\n * Examples:\r\n *   1_200 -> \"1.2K\"\r\n *   1_20_000 -> \"1.2L\"\r\n *   1_20_00_000 -> \"1.2Cr\"\r\n *   1_20_00_00_000 -> \"1.2Ar\"\r\n *   1_20_00_00_00_000 -> \"1.2Khar\"\r\n *   1_20_00_00_00_00_000 -> \"1.2Neel\"\r\n *   1_20_00_00_00_00_00_000 -> \"1.2Padma\"\r\n *   1_20_00_00_00_00_00_00_000 -> \"1.2Shankh\"\r\n */\r\nexport function formatIndianNumberShort(num: number): string {\r\n  if (num === null || num === undefined || isNaN(num)) return \"0\";\r\n  const absNum = Math.abs(num);\r\n\r\n  // Indian units and their values\r\n  const units = [\r\n    { value: 1e5, symbol: \"L\" }, // Lakh\r\n    { value: 1e7, symbol: \"Cr\" }, // Crore\r\n    { value: 1e9, symbol: \"Ar\" }, // Arab\r\n    { value: 1e11, symbol: \"Khar\" }, // Kharab\r\n    { value: 1e13, symbol: \"Neel\" }, // Neel\r\n    { value: 1e15, symbol: \"Padma\" }, // Padma\r\n    { value: 1e17, symbol: \"Shankh\" }, // Shankh\r\n  ];\r\n\r\n  // For thousands (K), use western style for sub-lakh\r\n  if (absNum < 1e5) {\r\n    if (absNum >= 1e3) {\r\n      return (num / 1e3).toFixed(1).replace(/\\.0$/, \"\") + \"K\";\r\n    }\r\n    return num.toString();\r\n  }\r\n\r\n  // Find the largest unit that fits\r\n  for (let i = units.length - 1; i >= 0; i--) {\r\n    if (absNum >= units[i].value) {\r\n      return (\r\n        (num / units[i].value).toFixed(1).replace(/\\.0$/, \"\") + units[i].symbol\r\n      );\r\n    }\r\n  }\r\n\r\n  // Fallback (should not reach here)\r\n  return num.toString();\r\n}\r\n\r\n/**\r\n * Formats an address from BusinessCardData into a single string\r\n */\r\nexport function formatAddress(data: BusinessCardData): string {\r\n  const addressParts = [\r\n    data.address_line,\r\n    data.locality,\r\n    data.city,\r\n    data.state,\r\n    data.pincode,\r\n  ].filter(Boolean);\r\n\r\n  return addressParts.join(\", \") || \"Address not available\";\r\n}\r\n\r\n/**\r\n * Formats a date in a user-friendly format with Indian Standard Time (IST)\r\n * @param date The date to format\r\n * @param includeTime Whether to include time in the formatted string\r\n * @returns Formatted date string in IST\r\n */\r\nexport function formatDate(date: Date, includeTime: boolean = false): string {\r\n  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {\r\n    return \"Invalid date\";\r\n  }\r\n\r\n  const options: Intl.DateTimeFormatOptions = {\r\n    year: \"numeric\",\r\n    month: \"long\",\r\n    day: \"numeric\",\r\n    timeZone: \"Asia/Kolkata\", // Explicitly set timezone to IST\r\n  };\r\n\r\n  if (includeTime) {\r\n    options.hour = \"2-digit\";\r\n    options.minute = \"2-digit\";\r\n    options.hour12 = true;\r\n  }\r\n\r\n  return date.toLocaleString(\"en-IN\", options);\r\n}\r\n\r\n/**\r\n * Formats a currency amount with the appropriate currency symbol\r\n * @param amount The amount to format\r\n * @param currency The currency code (e.g., INR, USD)\r\n * @returns Formatted currency string\r\n */\r\nexport function formatCurrency(\r\n  amount: number,\r\n  currency: string = \"INR\"\r\n): string {\r\n  if (amount === null || amount === undefined || isNaN(amount)) {\r\n    return \"Invalid amount\";\r\n  }\r\n\r\n  try {\r\n    return new Intl.NumberFormat(\"en-IN\", {\r\n      style: \"currency\",\r\n      currency: currency,\r\n      minimumFractionDigits: 0,\r\n      maximumFractionDigits: 2,\r\n    }).format(amount);\r\n  } catch {\r\n    // Catch any error without using the error variable\r\n    // Fallback in case of invalid currency code\r\n    return `${currency} ${amount.toFixed(2)}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Formats a string to title case (first letter of each word capitalized)\r\n * @param text The text to format\r\n * @returns The text in title case\r\n */\r\nexport function toTitleCase(text: string): string {\r\n  if (!text) return \"\";\r\n\r\n  return text\r\n    .toLowerCase()\r\n    .replace(/\\b\\w/g, (char) => char.toUpperCase());\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAUO,SAAS,mBAAmB,KAAgC;IACjE,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI,iBAAiB,MAAM,IAAI;IAE/B,+BAA+B;IAC/B,IAAI,eAAe,UAAU,CAAC,QAAQ;QACpC,iBAAiB,eAAe,SAAS,CAAC;IAC5C,OAEK,IAAI,eAAe,MAAM,KAAK,MAAM,eAAe,UAAU,CAAC,OAAO;QACxE,iBAAiB,eAAe,SAAS,CAAC;IAC5C;IAEA,kCAAkC;IAClC,IAAI,WAAW,IAAI,CAAC,iBAAiB;QACnC,OAAO;IACT;IAEA,OAAO,MAAM,iBAAiB;AAChC;AAOO,SAAS,gBAAgB,KAAgC;IAC9D,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG,GAAG;QAC9B,OAAO,iBAAiB,kDAAkD;IAC5E;IACA,MAAM,WAAW,MAAM,SAAS,CAAC,GAAG;IACpC,MAAM,UAAU,MAAM,SAAS,CAAC,MAAM,MAAM,GAAG;IAC/C,MAAM,aAAa,IAAI,MAAM,CAAC,MAAM,MAAM,GAAG;IAC7C,OAAO,GAAG,WAAW,aAAa,SAAS;AAC7C;AAOO,SAAS,UAAU,KAAgC;IACxD,IAAI,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC,MAAM;QAClC,OAAO,iBAAiB,qCAAqC;IAC/D;IACA,MAAM,QAAQ,MAAM,KAAK,CAAC;IAC1B,MAAM,WAAW,KAAK,CAAC,EAAE;IACzB,MAAM,SAAS,KAAK,CAAC,EAAE;IAEvB,IAAI,SAAS,MAAM,IAAI,KAAK,OAAO,MAAM,IAAI,KAAK,CAAC,OAAO,QAAQ,CAAC,MAAM;QACvE,OAAO,gBAAgB,4CAA4C;IACrE;IAEA,MAAM,iBACJ,SAAS,SAAS,CAAC,GAAG,KAAK,IAAI,MAAM,CAAC,SAAS,MAAM,GAAG;IAE1D,MAAM,cAAc,OAAO,KAAK,CAAC;IACjC,MAAM,aAAa,WAAW,CAAC,EAAE;IACjC,MAAM,YAAY,YAAY,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,oCAAoC;IAEtF,MAAM,mBACJ,WAAW,SAAS,CAAC,GAAG,KAAK,IAAI,MAAM,CAAC,WAAW,MAAM,GAAG;IAE9D,OAAO,GAAG,eAAe,CAAC,EAAE,iBAAiB,CAAC,EAAE,WAAW;AAC7D;AAeO,SAAS,wBAAwB,GAAW;IACjD,IAAI,QAAQ,QAAQ,QAAQ,aAAa,MAAM,MAAM,OAAO;IAC5D,MAAM,SAAS,KAAK,GAAG,CAAC;IAExB,gCAAgC;IAChC,MAAM,QAAQ;QACZ;YAAE,OAAO;YAAK,QAAQ;QAAI;QAC1B;YAAE,OAAO;YAAK,QAAQ;QAAK;QAC3B;YAAE,OAAO;YAAK,QAAQ;QAAK;QAC3B;YAAE,OAAO;YAAM,QAAQ;QAAO;QAC9B;YAAE,OAAO;YAAM,QAAQ;QAAO;QAC9B;YAAE,OAAO;YAAM,QAAQ;QAAQ;QAC/B;YAAE,OAAO;YAAM,QAAQ;QAAS;KACjC;IAED,oDAAoD;IACpD,IAAI,SAAS,KAAK;QAChB,IAAI,UAAU,KAAK;YACjB,OAAO,CAAC,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,QAAQ,MAAM;QACtD;QACA,OAAO,IAAI,QAAQ;IACrB;IAEA,kCAAkC;IAClC,IAAK,IAAI,IAAI,MAAM,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QAC1C,IAAI,UAAU,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE;YAC5B,OACE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,QAAQ,MAAM,KAAK,CAAC,EAAE,CAAC,MAAM;QAE3E;IACF;IAEA,mCAAmC;IACnC,OAAO,IAAI,QAAQ;AACrB;AAKO,SAAS,cAAc,IAAsB;IAClD,MAAM,eAAe;QACnB,KAAK,YAAY;QACjB,KAAK,QAAQ;QACb,KAAK,IAAI;QACT,KAAK,KAAK;QACV,KAAK,OAAO;KACb,CAAC,MAAM,CAAC;IAET,OAAO,aAAa,IAAI,CAAC,SAAS;AACpC;AAQO,SAAS,WAAW,IAAU,EAAE,cAAuB,KAAK;IACjE,IAAI,CAAC,QAAQ,CAAC,CAAC,gBAAgB,IAAI,KAAK,MAAM,KAAK,OAAO,KAAK;QAC7D,OAAO;IACT;IAEA,MAAM,UAAsC;QAC1C,MAAM;QACN,OAAO;QACP,KAAK;QACL,UAAU;IACZ;IAEA,IAAI,aAAa;QACf,QAAQ,IAAI,GAAG;QACf,QAAQ,MAAM,GAAG;QACjB,QAAQ,MAAM,GAAG;IACnB;IAEA,OAAO,KAAK,cAAc,CAAC,SAAS;AACtC;AAQO,SAAS,eACd,MAAc,EACd,WAAmB,KAAK;IAExB,IAAI,WAAW,QAAQ,WAAW,aAAa,MAAM,SAAS;QAC5D,OAAO;IACT;IAEA,IAAI;QACF,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ,EAAE,OAAM;QACN,mDAAmD;QACnD,4CAA4C;QAC5C,OAAO,GAAG,SAAS,CAAC,EAAE,OAAO,OAAO,CAAC,IAAI;IAC3C;AACF;AAOO,SAAS,YAAY,IAAY;IACtC,IAAI,CAAC,MAAM,OAAO;IAElB,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,SAAS,CAAC,OAAS,KAAK,WAAW;AAChD", "debugId": null}}, {"offset": {"line": 194, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/resizable-navbar.tsx"], "sourcesContent": ["\"use client\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Menu, X } from \"lucide-react\";\r\n\r\nimport React, { useRef, useState } from \"react\";\r\n\r\ninterface NavbarProps {\r\n  children: React.ReactNode;\r\n  className?: string;\r\n}\r\n\r\ninterface NavBodyProps {\r\n  children: React.ReactNode;\r\n  className?: string;\r\n  visible?: boolean;\r\n}\r\n\r\ninterface NavItemsProps {\r\n  items: {\r\n    name: string;\r\n    link: string;\r\n    badge?: string;\r\n    icon?: React.ComponentType<{ className?: string }>;\r\n  }[];\r\n  className?: string;\r\n  onItemClick?: () => void;\r\n}\r\n\r\ninterface MobileNavProps {\r\n  children: React.ReactNode;\r\n  className?: string;\r\n  visible?: boolean;\r\n}\r\n\r\ninterface MobileNavHeaderProps {\r\n  children: React.ReactNode;\r\n  className?: string;\r\n}\r\n\r\ninterface MobileNavMenuProps {\r\n  children: React.ReactNode;\r\n  className?: string;\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n}\r\n\r\nexport const Navbar = ({ children, className }: NavbarProps) => {\r\n  const ref = useRef<HTMLDivElement>(null);\r\n  const [visible, setVisible] = useState<boolean>(false);\r\n\r\n  // Use regular scroll event instead of Framer Motion\r\n  React.useEffect(() => {\r\n    const handleScroll = () => {\r\n      if (window.scrollY > 100) {\r\n        setVisible(true);\r\n      } else {\r\n        setVisible(false);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('scroll', handleScroll);\r\n    return () => window.removeEventListener('scroll', handleScroll);\r\n  }, []);\r\n\r\n  return (\r\n    <div\r\n      ref={ref}\r\n      // IMPORTANT: Change this to class of `fixed` if you want the navbar to be fixed\r\n      className={cn(\"sticky inset-x-0 top-20 z-40 w-full\", className)}\r\n    >\r\n      {React.Children.map(children, (child) =>\r\n        React.isValidElement(child)\r\n          ? React.cloneElement(\r\n              child as React.ReactElement<{ visible?: boolean }>,\r\n              { visible }\r\n            )\r\n          : child\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const NavBody = ({ children, className, visible }: NavBodyProps) => {\r\n  return (\r\n    <div\r\n      style={{\r\n        minWidth: \"900px\",\r\n        backdropFilter: visible ? \"blur(10px)\" : \"none\",\r\n        boxShadow: visible\r\n          ? \"0 0 24px rgba(34, 42, 53, 0.06), 0 1px 1px rgba(0, 0, 0, 0.05), 0 0 0 1px rgba(34, 42, 53, 0.04), 0 0 4px rgba(34, 42, 53, 0.08), 0 16px 68px rgba(47, 48, 55, 0.05), 0 1px 0 rgba(255, 255, 255, 0.1) inset\"\r\n          : \"none\",\r\n        width: visible ? \"40%\" : \"100%\",\r\n        transform: visible ? \"translateY(20px)\" : \"translateY(0)\",\r\n        transition: \"all 0.3s ease-out\",\r\n      }}\r\n      className={cn(\r\n        \"relative z-[60] mx-auto hidden w-full max-w-7xl flex-row items-center justify-between self-start rounded-full bg-transparent px-4 py-4 lg:flex dark:bg-transparent\",\r\n        visible && \"bg-white/80 dark:bg-neutral-950/80\",\r\n        className\r\n      )}\r\n    >\r\n      {children}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const NavItems = ({ items, className, onItemClick }: NavItemsProps) => {\r\n  const [hovered, setHovered] = useState<number | null>(null);\r\n\r\n  return (\r\n    <div\r\n      onMouseLeave={() => setHovered(null)}\r\n      className={cn(\r\n        \"absolute inset-0 hidden flex-1 flex-row items-center justify-center space-x-2 text-sm font-medium text-zinc-600 transition duration-200 hover:text-zinc-800 lg:flex lg:space-x-2\",\r\n        className\r\n      )}\r\n    >\r\n      {items.map((item, idx) => (\r\n        <a\r\n          onMouseEnter={() => setHovered(idx)}\r\n          onClick={onItemClick}\r\n          className=\"relative px-4 py-3 text-neutral-600 dark:text-neutral-300\"\r\n          key={`link-${idx}`}\r\n          href={item.link}\r\n        >\r\n          {hovered === idx && (\r\n            <div\r\n              className=\"absolute inset-0 h-full w-full rounded-full bg-gray-100 dark:bg-neutral-800 transition-all duration-200\"\r\n            />\r\n          )}\r\n          <span className=\"relative z-20\">\r\n            {item.badge && (\r\n              <span className=\"absolute -top-5 left-1/2 transform -translate-x-1/2 text-[8px] font-medium text-[var(--brand-gold)] bg-[var(--brand-gold)]/10 px-1 py-0.5 rounded border border-[var(--brand-gold)]/20 whitespace-nowrap\">\r\n                {item.badge}\r\n              </span>\r\n            )}\r\n            {item.name}\r\n          </span>\r\n        </a>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const MobileNav = ({ children, className, visible }: MobileNavProps) => {\r\n  return (\r\n    <div\r\n      style={{\r\n        backdropFilter: visible ? \"blur(10px)\" : \"none\",\r\n        boxShadow: visible\r\n          ? \"0 0 24px rgba(34, 42, 53, 0.06), 0 1px 1px rgba(0, 0, 0, 0.05), 0 0 0 1px rgba(34, 42, 53, 0.04), 0 0 4px rgba(34, 42, 53, 0.08), 0 16px 68px rgba(47, 48, 55, 0.05), 0 1px 0 rgba(255, 255, 255, 0.1) inset\"\r\n          : \"none\",\r\n        width: visible ? \"90%\" : \"100%\",\r\n        paddingRight: visible ? \"12px\" : \"0px\",\r\n        paddingLeft: visible ? \"12px\" : \"0px\",\r\n        borderRadius: visible ? \"4px\" : \"2rem\",\r\n        transform: visible ? \"translateY(20px)\" : \"translateY(0)\",\r\n        transition: \"all 0.3s ease-out\",\r\n      }}\r\n      className={cn(\r\n        \"relative z-50 mx-auto flex w-full max-w-[calc(100vw-2rem)] flex-col items-center justify-between bg-transparent px-0 py-4 lg:hidden\",\r\n        visible && \"bg-white/80 dark:bg-neutral-950/80\",\r\n        className\r\n      )}\r\n    >\r\n      {children}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const MobileNavHeader = ({\r\n  children,\r\n  className,\r\n}: MobileNavHeaderProps) => {\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"flex w-full flex-row items-center justify-between\",\r\n        className\r\n      )}\r\n    >\r\n      {children}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const MobileNavMenu = ({\r\n  children,\r\n  className,\r\n  isOpen,\r\n}: MobileNavMenuProps) => {\r\n  return (\r\n    <>\r\n      {isOpen && (\r\n        <div\r\n          className={cn(\r\n            \"absolute inset-x-0 top-16 z-50 flex w-full flex-col items-start justify-start gap-4 rounded-lg bg-white px-4 py-8 shadow-[0_0_24px_rgba(34,_42,_53,_0.06),_0_1px_1px_rgba(0,_0,_0,_0.05),_0_0_0_1px_rgba(34,_42,_53,_0.04),_0_0_4px_rgba(34,_42,_53,_0.08),_0_16px_68px_rgba(47,_48,_55,_0.05),_0_1px_0_rgba(255,_255,_255,_0.1)_inset] dark:bg-neutral-950 transition-opacity duration-200\",\r\n            className\r\n          )}\r\n        >\r\n          {children}\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport const MobileNavToggle = ({\r\n  isOpen,\r\n  onClick,\r\n}: {\r\n  isOpen: boolean;\r\n  onClick: () => void;\r\n}) => {\r\n  return isOpen ? (\r\n    <X\r\n      className=\"cursor-pointer text-black dark:text-white\"\r\n      onClick={onClick}\r\n    />\r\n  ) : (\r\n    <Menu\r\n      className=\"cursor-pointer text-black dark:text-white\"\r\n      onClick={onClick}\r\n    />\r\n  );\r\n};\r\n\r\nexport const NavbarButton = ({\r\n  href,\r\n  as: Tag = \"a\",\r\n  children,\r\n  className,\r\n  variant = \"primary\",\r\n  ...props\r\n}: {\r\n  href?: string;\r\n  as?: React.ElementType;\r\n  children: React.ReactNode;\r\n  className?: string;\r\n  variant?: \"primary\" | \"secondary\" | \"dark\" | \"gradient\";\r\n} & (\r\n  | React.ComponentPropsWithoutRef<\"a\">\r\n  | React.ComponentPropsWithoutRef<\"button\">\r\n)) => {\r\n  const baseStyles =\r\n    \"px-4 py-2 rounded-md bg-white button bg-white text-black text-sm font-bold relative cursor-pointer hover:-translate-y-0.5 transition duration-200 inline-block text-center\";\r\n\r\n  const variantStyles = {\r\n    primary:\r\n      \"shadow-[0_0_24px_rgba(34,_42,_53,_0.06),_0_1px_1px_rgba(0,_0,_0,_0.05),_0_0_0_1px_rgba(34,_42,_53,_0.04),_0_0_4px_rgba(34,_42,_53,_0.08),_0_16px_68px_rgba(47,_48,_55,_0.05),_0_1px_0_rgba(255,_255,_255,_0.1)_inset]\",\r\n    secondary: \"bg-transparent shadow-none dark:text-white\",\r\n    dark: \"bg-black text-white shadow-[0_0_24px_rgba(34,_42,_53,_0.06),_0_1px_1px_rgba(0,_0,_0,_0.05),_0_0_0_1px_rgba(34,_42,_53,_0.04),_0_0_4px_rgba(34,_42,_53,_0.08),_0_16px_68px_rgba(47,_48,_55,_0.05),_0_1px_0_rgba(255,_255,_255,_0.1)_inset]\",\r\n    gradient:\r\n      \"bg-gradient-to-b from-blue-500 to-blue-700 text-white shadow-[0px_2px_0px_0px_rgba(255,255,255,0.3)_inset]\",\r\n  };\r\n\r\n  return (\r\n    <Tag\r\n      href={href || undefined}\r\n      className={cn(baseStyles, variantStyles[variant], className)}\r\n      {...props}\r\n    >\r\n      {children}\r\n    </Tag>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AACA;AACA;AAAA;AAEA;AAJA;;;;;AA8CO,MAAM,SAAS,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAe;IACzD,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAEhD,oDAAoD;IACpD,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,MAAM,eAAe;YACnB,IAAI,OAAO,OAAO,GAAG,KAAK;gBACxB,WAAW;YACb,OAAO;gBACL,WAAW;YACb;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,KAAK;QACL,gFAAgF;QAChF,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,uCAAuC;kBAEpD,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,sBAC7B,qMAAA,CAAA,UAAK,CAAC,cAAc,CAAC,uBACjB,qMAAA,CAAA,UAAK,CAAC,YAAY,CAChB,OACA;gBAAE;YAAQ,KAEZ;;;;;;AAIZ;AAEO,MAAM,UAAU,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAgB;IACpE,qBACE,8OAAC;QACC,OAAO;YACL,UAAU;YACV,gBAAgB,UAAU,eAAe;YACzC,WAAW,UACP,iNACA;YACJ,OAAO,UAAU,QAAQ;YACzB,WAAW,UAAU,qBAAqB;YAC1C,YAAY;QACd;QACA,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sKACA,WAAW,sCACX;kBAGD;;;;;;AAGP;AAEO,MAAM,WAAW,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAiB;IACvE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtD,qBACE,8OAAC;QACC,cAAc,IAAM,WAAW;QAC/B,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,oLACA;kBAGD,MAAM,GAAG,CAAC,CAAC,MAAM,oBAChB,8OAAC;gBACC,cAAc,IAAM,WAAW;gBAC/B,SAAS;gBACT,WAAU;gBAEV,MAAM,KAAK,IAAI;;oBAEd,YAAY,qBACX,8OAAC;wBACC,WAAU;;;;;;kCAGd,8OAAC;wBAAK,WAAU;;4BACb,KAAK,KAAK,kBACT,8OAAC;gCAAK,WAAU;0CACb,KAAK,KAAK;;;;;;4BAGd,KAAK,IAAI;;;;;;;;eAdP,CAAC,KAAK,EAAE,KAAK;;;;;;;;;;AAoB5B;AAEO,MAAM,YAAY,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAkB;IACxE,qBACE,8OAAC;QACC,OAAO;YACL,gBAAgB,UAAU,eAAe;YACzC,WAAW,UACP,iNACA;YACJ,OAAO,UAAU,QAAQ;YACzB,cAAc,UAAU,SAAS;YACjC,aAAa,UAAU,SAAS;YAChC,cAAc,UAAU,QAAQ;YAChC,WAAW,UAAU,qBAAqB;YAC1C,YAAY;QACd;QACA,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uIACA,WAAW,sCACX;kBAGD;;;;;;AAGP;AAEO,MAAM,kBAAkB,CAAC,EAC9B,QAAQ,EACR,SAAS,EACY;IACrB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qDACA;kBAGD;;;;;;AAGP;AAEO,MAAM,gBAAgB,CAAC,EAC5B,QAAQ,EACR,SAAS,EACT,MAAM,EACa;IACnB,qBACE;kBACG,wBACC,8OAAC;YACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+XACA;sBAGD;;;;;;;AAKX;AAEO,MAAM,kBAAkB,CAAC,EAC9B,MAAM,EACN,OAAO,EAIR;IACC,OAAO,uBACL,8OAAC,4LAAA,CAAA,IAAC;QACA,WAAU;QACV,SAAS;;;;;6BAGX,8OAAC,kMAAA,CAAA,OAAI;QACH,WAAU;QACV,SAAS;;;;;;AAGf;AAEO,MAAM,eAAe,CAAC,EAC3B,IAAI,EACJ,IAAI,MAAM,GAAG,EACb,QAAQ,EACR,SAAS,EACT,UAAU,SAAS,EACnB,GAAG,OAUJ;IACC,MAAM,aACJ;IAEF,MAAM,gBAAgB;QACpB,SACE;QACF,WAAW;QACX,MAAM;QACN,UACE;IACJ;IAEA,qBACE,8OAAC;QACC,MAAM,QAAQ;QACd,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY,aAAa,CAAC,QAAQ,EAAE;QACjD,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 392, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/hooks/use-mobile.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\n\r\nconst MOBILE_BREAKPOINT = 768;\r\n\r\nexport function useIsMobile() {\r\n  // Initialize with undefined to avoid hydration mismatch\r\n  const [isMobile, setIsMobile] = useState<boolean | undefined>(undefined);\r\n\r\n  useEffect(() => {\r\n    // Check if window is available (client-side)\r\n    if (typeof window !== \"undefined\") {\r\n      const checkMobile = () => {\r\n        setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\r\n      };\r\n\r\n      // Initial check\r\n      checkMobile();\r\n\r\n      // Set up media query listener\r\n      const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);\r\n      const onChange = () => {\r\n        checkMobile();\r\n      };\r\n\r\n      mql.addEventListener(\"change\", onChange);\r\n      return () => mql.removeEventListener(\"change\", onChange);\r\n    }\r\n  }, []);\r\n\r\n  // Return false during SSR to avoid hydration issues\r\n  return isMobile ?? false;\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAIA,MAAM,oBAAoB;AAEnB,SAAS;IACd,wDAAwD;IACxD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAE9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,6CAA6C;QAC7C,uCAAmC;;QAgBnC;IACF,GAAG,EAAE;IAEL,oDAAoD;IACpD,OAAO,YAAY;AACrB", "debugId": null}}, {"offset": {"line": 417, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all   disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,gdACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 474, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\r\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Trigger\r\n      data-slot=\"dropdown-menu-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot=\"dropdown-menu-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </DropdownMenuPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = \"default\",\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\r\n  inset?: boolean\r\n  variant?: \"default\" | \"destructive\"\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot=\"dropdown-menu-item\"\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot=\"dropdown-menu-checkbox-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-pointer items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioGroup\r\n      data-slot=\"dropdown-menu-radio-group\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot=\"dropdown-menu-radio-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-pointer items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className=\"size-2 fill-current\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot=\"dropdown-menu-label\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot=\"dropdown-menu-separator\"\r\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"dropdown-menu-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\r\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot=\"dropdown-menu-sub-trigger\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-pointer items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronRightIcon className=\"ml-auto size-4\" />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot=\"dropdown-menu-sub-content\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 736, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/ThemeToggle.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { Moon, Sun, Monitor } from \"lucide-react\";\r\nimport { useTheme } from \"next-themes\";\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\n\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\n\r\ninterface ThemeToggleProps {\r\n  variant?: \"default\" | \"dashboard\";\r\n}\r\n\r\nexport function ThemeToggle({ variant = \"default\" }: ThemeToggleProps = {}) {\r\n  const { setTheme, theme } = useTheme();\r\n  const isMobile = useIsMobile();\r\n\r\n  // Mobile version with modern card design (only for default variant, not dashboard)\r\n  if (isMobile && variant === \"default\") {\r\n    return (\r\n      <div className=\"w-full\">\r\n        <div className=\"flex items-center justify-between p-4 rounded-xl bg-white/50 dark:bg-neutral-800/50 border border-neutral-200/50 dark:border-neutral-700/50 backdrop-blur-sm\">\r\n          <div className=\"flex items-center gap-3\">\r\n            <div className=\"flex items-center justify-center w-10 h-10 rounded-lg bg-muted text-foreground\">\r\n              {theme === \"light\" ? (\r\n                <Sun className=\"h-5 w-5\" />\r\n              ) : theme === \"dark\" ? (\r\n                <Moon className=\"h-5 w-5\" />\r\n              ) : (\r\n                <Monitor className=\"h-5 w-5\" />\r\n              )}\r\n            </div>\r\n            <div className=\"flex flex-col\">\r\n              <span className=\"font-medium text-foreground\">Theme</span>\r\n              <span className=\"text-xs text-muted-foreground capitalize\">\r\n                {theme || \"system\"}\r\n              </span>\r\n            </div>\r\n          </div>\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button variant=\"ghost\" size=\"sm\" className=\"h-8 px-3\">\r\n                Change\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\" className=\"w-40\">\r\n              <DropdownMenuItem onClick={() => setTheme(\"light\")}>\r\n                <Sun className=\"mr-2 h-4 w-4\" />\r\n                <span>Light</span>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\r\n                <Moon className=\"mr-2 h-4 w-4\" />\r\n                <span>Dark</span>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={() => setTheme(\"system\")}>\r\n                <Monitor className=\"mr-2 h-4 w-4\" />\r\n                <span>System</span>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Dashboard variant - simplified icon-only button matching avatar size\r\n  if (variant === \"dashboard\") {\r\n    return (\r\n      <DropdownMenu>\r\n        <DropdownMenuTrigger asChild>\r\n          <Button variant=\"ghost\" className=\"h-9 w-9 rounded-full focus-visible:ring-0 focus-visible:ring-offset-0\">\r\n            <Sun className=\"h-[1.1rem] w-[1.1rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\r\n            <Moon className=\"absolute h-[1.1rem] w-[1.1rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\r\n            <span className=\"sr-only\">Toggle theme</span>\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent align=\"end\">\r\n          <DropdownMenuItem onClick={() => setTheme(\"light\")}>\r\n            <Sun className=\"mr-2 h-4 w-4\" />\r\n            <span>Light</span>\r\n          </DropdownMenuItem>\r\n          <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\r\n            <Moon className=\"mr-2 h-4 w-4\" />\r\n            <span>Dark</span>\r\n          </DropdownMenuItem>\r\n          <DropdownMenuItem onClick={() => setTheme(\"system\")}>\r\n            <Monitor className=\"mr-2 h-4 w-4\" />\r\n            <span>System</span>\r\n          </DropdownMenuItem>\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n    );\r\n  }\r\n\r\n  // Desktop version (original)\r\n  return (\r\n    <DropdownMenu>\r\n      <DropdownMenuTrigger asChild>\r\n        <Button variant=\"outline\" size=\"icon\">\r\n          <Sun className=\"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\r\n          <Moon className=\"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\r\n          <span className=\"sr-only\">Toggle theme</span>\r\n        </Button>\r\n      </DropdownMenuTrigger>\r\n      <DropdownMenuContent align=\"end\">\r\n        <DropdownMenuItem onClick={() => setTheme(\"light\")}>\r\n          <Sun className=\"mr-2 h-4 w-4\" />\r\n          <span>Light</span>\r\n        </DropdownMenuItem>\r\n        <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\r\n          <Moon className=\"mr-2 h-4 w-4\" />\r\n          <span>Dark</span>\r\n        </DropdownMenuItem>\r\n        <DropdownMenuItem onClick={() => setTheme(\"system\")}>\r\n          <Monitor className=\"mr-2 h-4 w-4\" />\r\n          <span>System</span>\r\n        </DropdownMenuItem>\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AARA;;;;;;;AAmBO,SAAS,YAAY,EAAE,UAAU,SAAS,EAAoB,GAAG,CAAC,CAAC;IACxE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;IAE3B,mFAAmF;IACnF,IAAI,YAAY,YAAY,WAAW;QACrC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,UAAU,wBACT,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;2CACb,UAAU,uBACZ,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;yDAEhB,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;0CAGvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAA8B;;;;;;kDAC9C,8OAAC;wCAAK,WAAU;kDACb,SAAS;;;;;;;;;;;;;;;;;;kCAIhB,8OAAC,qIAAA,CAAA,eAAY;;0CACX,8OAAC,qIAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,8OAAC,2HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;8CAAW;;;;;;;;;;;0CAIzD,8OAAC,qIAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAM,WAAU;;kDACzC,8OAAC,qIAAA,CAAA,mBAAgB;wCAAC,SAAS,IAAM,SAAS;;0DACxC,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,qIAAA,CAAA,mBAAgB;wCAAC,SAAS,IAAM,SAAS;;0DACxC,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,qIAAA,CAAA,mBAAgB;wCAAC,SAAS,IAAM,SAAS;;0DACxC,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOpB;IAEA,uEAAuE;IACvE,IAAI,YAAY,aAAa;QAC3B,qBACE,8OAAC,qIAAA,CAAA,eAAY;;8BACX,8OAAC,qIAAA,CAAA,sBAAmB;oBAAC,OAAO;8BAC1B,cAAA,8OAAC,2HAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,WAAU;;0CAChC,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;8BAG9B,8OAAC,qIAAA,CAAA,sBAAmB;oBAAC,OAAM;;sCACzB,8OAAC,qIAAA,CAAA,mBAAgB;4BAAC,SAAS,IAAM,SAAS;;8CACxC,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,8OAAC;8CAAK;;;;;;;;;;;;sCAER,8OAAC,qIAAA,CAAA,mBAAgB;4BAAC,SAAS,IAAM,SAAS;;8CACxC,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;8CAAK;;;;;;;;;;;;sCAER,8OAAC,qIAAA,CAAA,mBAAgB;4BAAC,SAAS,IAAM,SAAS;;8CACxC,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;IAKhB;IAEA,6BAA6B;IAC7B,qBACE,8OAAC,qIAAA,CAAA,eAAY;;0BACX,8OAAC,qIAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,2HAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,8OAAC,qIAAA,CAAA,sBAAmB;gBAAC,OAAM;;kCACzB,8OAAC,qIAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,8OAAC;0CAAK;;;;;;;;;;;;kCAER,8OAAC,qIAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC;0CAAK;;;;;;;;;;;;kCAER,8OAAC,qIAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,8OAAC,wMAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB", "debugId": null}}, {"offset": {"line": 1289, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/utils/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr';\nimport { Database } from '@/types/supabase';\n\nexport function createClient() {\n  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\n  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\n\n  if (!supabaseUrl || !supabaseAnonKey) {\n    console.error(\"Supabase environment variables are not set. Please ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are defined.\");\n    return createBrowserClient<Database>(\"\", \"\");\n  }\n\n  return createBrowserClient<Database>(\n    supabaseUrl,\n    supabaseAnonKey\n  );\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAGO,SAAS;IACd,MAAM;IACN,MAAM;IAEN,uCAAsC;;IAGtC;IAEA,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,EACvB,aACA;AAEJ", "debugId": null}}, {"offset": {"line": 1309, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/hooks/useScrollDirection.ts"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from 'react';\n\ninterface UseScrollDirectionOptions {\n  threshold?: number;\n  initialDirection?: 'up' | 'down';\n}\n\ninterface ScrollState {\n  scrollDirection: 'up' | 'down';\n  isScrolled: boolean;\n  scrollY: number;\n}\n\nexport function useScrollDirection(options: UseScrollDirectionOptions = {}): ScrollState {\n  const { threshold = 10, initialDirection = 'up' } = options;\n  \n  const [scrollState, setScrollState] = useState<ScrollState>({\n    scrollDirection: initialDirection,\n    isScrolled: false,\n    scrollY: 0,\n  });\n\n  useEffect(() => {\n    let lastScrollY = window.scrollY;\n    let ticking = false;\n\n    const updateScrollDirection = () => {\n      const scrollY = window.scrollY;\n      const direction = scrollY > lastScrollY ? 'down' : 'up';\n      const isScrolled = scrollY > threshold;\n\n      // Only update if the scroll direction has changed or crossed the threshold\n      if (\n        direction !== scrollState.scrollDirection ||\n        Math.abs(scrollY - lastScrollY) > threshold ||\n        isScrolled !== scrollState.isScrolled\n      ) {\n        setScrollState({\n          scrollDirection: direction,\n          isScrolled,\n          scrollY,\n        });\n      }\n\n      lastScrollY = scrollY > 0 ? scrollY : 0;\n      ticking = false;\n    };\n\n    const onScroll = () => {\n      if (!ticking) {\n        requestAnimationFrame(updateScrollDirection);\n        ticking = true;\n      }\n    };\n\n    window.addEventListener('scroll', onScroll);\n\n    return () => window.removeEventListener('scroll', onScroll);\n  }, [scrollState.scrollDirection, scrollState.isScrolled, threshold]);\n\n  return scrollState;\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAeO,SAAS,mBAAmB,UAAqC,CAAC,CAAC;IACxE,MAAM,EAAE,YAAY,EAAE,EAAE,mBAAmB,IAAI,EAAE,GAAG;IAEpD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QAC1D,iBAAiB;QACjB,YAAY;QACZ,SAAS;IACX;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,OAAO,OAAO;QAChC,IAAI,UAAU;QAEd,MAAM,wBAAwB;YAC5B,MAAM,UAAU,OAAO,OAAO;YAC9B,MAAM,YAAY,UAAU,cAAc,SAAS;YACnD,MAAM,aAAa,UAAU;YAE7B,2EAA2E;YAC3E,IACE,cAAc,YAAY,eAAe,IACzC,KAAK,GAAG,CAAC,UAAU,eAAe,aAClC,eAAe,YAAY,UAAU,EACrC;gBACA,eAAe;oBACb,iBAAiB;oBACjB;oBACA;gBACF;YACF;YAEA,cAAc,UAAU,IAAI,UAAU;YACtC,UAAU;QACZ;QAEA,MAAM,WAAW;YACf,IAAI,CAAC,SAAS;gBACZ,sBAAsB;gBACtB,UAAU;YACZ;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG;QAAC,YAAY,eAAe;QAAE,YAAY,UAAU;QAAE;KAAU;IAEnE,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1361, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/Header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport {\r\n  Navbar,\r\n  NavBody,\r\n  NavItems,\r\n  MobileNav,\r\n  NavbarButton,\r\n  MobileNavHeader,\r\n  MobileNavToggle,\r\n  MobileNavMenu,\r\n} from \"@/components/ui/resizable-navbar\";\r\nimport { ThemeToggle } from \"@/app/components/ThemeToggle\";\r\nimport { createClient } from \"@/utils/supabase/client\";\r\nimport type { User } from \"@supabase/supabase-js\";\r\nimport { Grid3X3, Search, Store, Users, ArrowRight } from \"lucide-react\";\r\nimport { useScrollDirection } from \"@/hooks/useScrollDirection\";\r\nimport { usePathname } from \"next/navigation\";\r\n// import { Badge } from \"@/components/ui/badge\";\r\n\r\nconst Header = () => {\r\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const [userType, setUserType] = useState<\"customer\" | \"business\" | null>(\r\n    null\r\n  );\r\n  const [loading, setLoading] = useState(true);\r\n  const pathname = usePathname();\r\n  const { scrollDirection, isScrolled } = useScrollDirection({ threshold: 50 });\r\n\r\n  // Determine if header should be hidden based on scroll and page\r\n  const shouldHideHeader = isScrolled && scrollDirection === 'down' &&\r\n    (pathname.startsWith('/dashboard/') || pathname === '/discover' || pathname.startsWith('/post/'));\r\n\r\n  useEffect(() => {\r\n    const fetchUserAndProfile = async () => {\r\n      const supabase = createClient();\r\n      setLoading(true);\r\n\r\n      const {\r\n        data: { user: currentUser },\r\n      } = await supabase.auth.getUser();\r\n      setUser(currentUser);\r\n\r\n      if (currentUser) {\r\n        const [customerRes, businessRes] = await Promise.all([\r\n          supabase\r\n            .from(\"customer_profiles\")\r\n            .select(\"id\")\r\n            .eq(\"id\", currentUser.id)\r\n            .maybeSingle(),\r\n          supabase\r\n            .from(\"business_profiles\")\r\n            .select(\"id\")\r\n            .eq(\"id\", currentUser.id)\r\n            .maybeSingle(),\r\n        ]);\r\n\r\n        if (customerRes.data) {\r\n          setUserType(\"customer\");\r\n        } else if (businessRes.data) {\r\n          setUserType(\"business\");\r\n        } else {\r\n          setUserType(null);\r\n        }\r\n      } else {\r\n        setUserType(null);\r\n      }\r\n      setLoading(false);\r\n    };\r\n\r\n    fetchUserAndProfile();\r\n  }, []);\r\n\r\n  const getDashboardPath = () => {\r\n    if (userType === \"business\") return \"/dashboard/business\";\r\n    if (userType === \"customer\") return \"/dashboard/customer\";\r\n    return \"/dashboard/customer\";\r\n  };\r\n\r\n  const navItems = [\r\n    { name: \"Categories\", link: \"/categories\", icon: Grid3X3 },\r\n    { name: \"Discover\", link: \"/discover\", icon: Search },\r\n    { name: \"Free Listing\", link: \"/login\", badge: \"Business\", icon: Store },\r\n    {\r\n      name: user ? \"Feed\" : \"Community\",\r\n      link: user ? getDashboardPath() : \"/login\",\r\n      icon: Users,\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"relative w-full\">\r\n      <Navbar className={`fixed top-0 left-0 right-0 z-50 bg-background/80 dark:bg-background/90 backdrop-blur-lg border-b border-border/80 dark:border-border transition-transform duration-300 ease-in-out ${\r\n        shouldHideHeader ? '-translate-y-full' : 'translate-y-0'\r\n      }`}>\r\n        {/* Desktop Navigation */}\r\n        <NavBody className=\"max-w-7xl mx-auto px-4 md:px-6 lg:px-8\">\r\n          <Link href={user ? \"/?view=home\" : \"/\"} className=\"flex items-center shrink-0 z-[100]\">\r\n            <div className=\"flex flex-col\">\r\n              <span className=\"font-bold text-xl text-[var(--brand-gold)]\">\r\n                Dukan<span className=\"text-foreground\">card</span>\r\n              </span>\r\n              <span className=\"text-xs text-[var(--brand-gold)]/80 -mt-1\">\r\n                Your Neighborhood, Digitally Connected\r\n              </span>\r\n            </div>\r\n          </Link>\r\n          <NavItems\r\n            items={navItems}\r\n            className=\"flex flex-1 flex-row items-center justify-center space-x-2 text-sm font-medium text-muted-foreground hover:text-[var(--brand-gold)]\"\r\n          />\r\n          <div className=\"flex items-center gap-4 z-[70]\">\r\n            <ThemeToggle />\r\n            {!loading ? (\r\n              user ? (\r\n                <NavbarButton\r\n                  variant=\"primary\"\r\n                  as=\"button\"\r\n                  onClick={() => (window.location.href = getDashboardPath())}\r\n                  className=\"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] rounded-full px-6 font-medium shadow hover:shadow-md\"\r\n                >\r\n                  Dashboard\r\n                </NavbarButton>\r\n              ) : (\r\n                <NavbarButton\r\n                  variant=\"primary\"\r\n                  as=\"button\"\r\n                  onClick={() => (window.location.href = \"/login\")}\r\n                  className=\"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] rounded-full px-6 font-medium shadow hover:shadow-md\"\r\n                >\r\n                  Sign In\r\n                </NavbarButton>\r\n              )\r\n            ) : (\r\n              <div className=\"h-10 w-24 bg-muted rounded-full animate-pulse\" />\r\n            )}\r\n          </div>\r\n        </NavBody>\r\n\r\n        {/* Mobile Navigation */}\r\n        <MobileNav>\r\n          <MobileNavHeader>\r\n            <Link href={user ? \"/?view=home\" : \"/\"} className=\"flex items-center shrink-0 z-[100]\">\r\n              <div className=\"flex flex-col\">\r\n                <span className=\"font-bold text-xl text-[var(--brand-gold)]\">\r\n                  Dukan<span className=\"text-foreground\">card</span>\r\n                </span>\r\n                <span className=\"text-xs text-[var(--brand-gold)]/80 -mt-1\">\r\n                  Your Neighborhood, Digitally Connected\r\n                </span>\r\n              </div>\r\n            </Link>\r\n            <MobileNavToggle\r\n              isOpen={isMobileMenuOpen}\r\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\r\n            />\r\n          </MobileNavHeader>\r\n\r\n          <MobileNavMenu\r\n            isOpen={isMobileMenuOpen}\r\n            onClose={() => setIsMobileMenuOpen(false)}\r\n            className=\"bg-background/95 backdrop-blur-md border-t border-border\"\r\n          >\r\n            <div className=\"w-full space-y-2\">\r\n              {navItems.map((item, idx) => {\r\n                const IconComponent = item.icon;\r\n                return (\r\n                  <Link\r\n                    key={`mobile-link-${idx}`}\r\n                    href={item.link}\r\n                    onClick={() => setIsMobileMenuOpen(false)}\r\n                    className=\"group flex items-center justify-between w-full p-4 rounded-xl bg-white/50 dark:bg-neutral-800/50 hover:bg-white/80 dark:hover:bg-neutral-800/80 border border-neutral-200/50 dark:border-neutral-700/50 transition-all duration-200 hover:shadow-md hover:scale-[1.02] active:scale-[0.98]\"\r\n                  >\r\n                    <div className=\"flex items-center gap-3\">\r\n                      <div className=\"flex items-center justify-center w-10 h-10 rounded-lg bg-[var(--brand-gold)]/10 text-[var(--brand-gold)] group-hover:bg-[var(--brand-gold)]/20 transition-colors\">\r\n                        <IconComponent className=\"h-5 w-5\" />\r\n                      </div>\r\n                      <div className=\"flex flex-col\">\r\n                        <span className=\"font-medium text-foreground group-hover:text-[var(--brand-gold)] transition-colors\">\r\n                          {item.name}\r\n                        </span>\r\n                        {item.badge && (\r\n                          <span className=\"text-xs text-[var(--brand-gold)] font-medium\">\r\n                            {item.badge}\r\n                          </span>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                    <ArrowRight className=\"h-4 w-4 text-muted-foreground group-hover:text-[var(--brand-gold)] group-hover:translate-x-1 transition-all\" />\r\n                  </Link>\r\n                );\r\n              })}\r\n            </div>\r\n            <div className=\"flex w-full flex-col gap-4 px-0\">\r\n              <ThemeToggle />\r\n              {!loading ? (\r\n                user ? (\r\n                  <NavbarButton\r\n                    variant=\"primary\"\r\n                    as=\"button\"\r\n                    className=\"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] rounded-xl w-full mx-4 shadow-md hover:shadow-lg transition-all\"\r\n                    onClick={() => {\r\n                      setIsMobileMenuOpen(false);\r\n                      window.location.href = getDashboardPath();\r\n                    }}\r\n                  >\r\n                    Dashboard\r\n                  </NavbarButton>\r\n                ) : (\r\n                  <NavbarButton\r\n                    variant=\"primary\"\r\n                    as=\"button\"\r\n                    className=\"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] rounded-xl w-full mx-4 shadow-md hover:shadow-lg transition-all\"\r\n                    onClick={() => {\r\n                      setIsMobileMenuOpen(false);\r\n                      window.location.href = \"/login\";\r\n                    }}\r\n                  >\r\n                    Sign In\r\n                  </NavbarButton>\r\n                )\r\n              ) : (\r\n                <div className=\"h-10 w-full bg-muted rounded-xl animate-pulse mx-4\" />\r\n              )}\r\n            </div>\r\n          </MobileNavMenu>\r\n        </MobileNav>\r\n      </Navbar>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAUA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAnBA;;;;;;;;;;AAoBA,iDAAiD;AAEjD,MAAM,SAAS;IACb,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACrC;IAEF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,eAAe,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE;QAAE,WAAW;IAAG;IAE3E,gEAAgE;IAChE,MAAM,mBAAmB,cAAc,oBAAoB,UACzD,CAAC,SAAS,UAAU,CAAC,kBAAkB,aAAa,eAAe,SAAS,UAAU,CAAC,SAAS;IAElG,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,sBAAsB;YAC1B,MAAM,WAAW,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;YAC5B,WAAW;YAEX,MAAM,EACJ,MAAM,EAAE,MAAM,WAAW,EAAE,EAC5B,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YAC/B,QAAQ;YAER,IAAI,aAAa;gBACf,MAAM,CAAC,aAAa,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;oBACnD,SACG,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,YAAY,EAAE,EACvB,WAAW;oBACd,SACG,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,YAAY,EAAE,EACvB,WAAW;iBACf;gBAED,IAAI,YAAY,IAAI,EAAE;oBACpB,YAAY;gBACd,OAAO,IAAI,YAAY,IAAI,EAAE;oBAC3B,YAAY;gBACd,OAAO;oBACL,YAAY;gBACd;YACF,OAAO;gBACL,YAAY;YACd;YACA,WAAW;QACb;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,IAAI,aAAa,YAAY,OAAO;QACpC,IAAI,aAAa,YAAY,OAAO;QACpC,OAAO;IACT;IAEA,MAAM,WAAW;QACf;YAAE,MAAM;YAAc,MAAM;YAAe,MAAM,4MAAA,CAAA,UAAO;QAAC;QACzD;YAAE,MAAM;YAAY,MAAM;YAAa,MAAM,sMAAA,CAAA,SAAM;QAAC;QACpD;YAAE,MAAM;YAAgB,MAAM;YAAU,OAAO;YAAY,MAAM,oMAAA,CAAA,QAAK;QAAC;QACvE;YACE,MAAM,OAAO,SAAS;YACtB,MAAM,OAAO,qBAAqB;YAClC,MAAM,oMAAA,CAAA,QAAK;QACb;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,wIAAA,CAAA,SAAM;YAAC,WAAW,CAAC,mLAAmL,EACrM,mBAAmB,sBAAsB,iBACzC;;8BAEA,8OAAC,wIAAA,CAAA,UAAO;oBAAC,WAAU;;sCACjB,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAM,OAAO,gBAAgB;4BAAK,WAAU;sCAChD,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CAA6C;0DACtD,8OAAC;gDAAK,WAAU;0DAAkB;;;;;;;;;;;;kDAEzC,8OAAC;wCAAK,WAAU;kDAA4C;;;;;;;;;;;;;;;;;sCAKhE,8OAAC,wIAAA,CAAA,WAAQ;4BACP,OAAO;4BACP,WAAU;;;;;;sCAEZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,cAAW;;;;;gCACX,CAAC,UACA,qBACE,8OAAC,wIAAA,CAAA,eAAY;oCACX,SAAQ;oCACR,IAAG;oCACH,SAAS,IAAO,OAAO,QAAQ,CAAC,IAAI,GAAG;oCACvC,WAAU;8CACX;;;;;yDAID,8OAAC,wIAAA,CAAA,eAAY;oCACX,SAAQ;oCACR,IAAG;oCACH,SAAS,IAAO,OAAO,QAAQ,CAAC,IAAI,GAAG;oCACvC,WAAU;8CACX;;;;;yDAKH,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;8BAMrB,8OAAC,wIAAA,CAAA,YAAS;;sCACR,8OAAC,wIAAA,CAAA,kBAAe;;8CACd,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAM,OAAO,gBAAgB;oCAAK,WAAU;8CAChD,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;;oDAA6C;kEACtD,8OAAC;wDAAK,WAAU;kEAAkB;;;;;;;;;;;;0DAEzC,8OAAC;gDAAK,WAAU;0DAA4C;;;;;;;;;;;;;;;;;8CAKhE,8OAAC,wIAAA,CAAA,kBAAe;oCACd,QAAQ;oCACR,SAAS,IAAM,oBAAoB,CAAC;;;;;;;;;;;;sCAIxC,8OAAC,wIAAA,CAAA,gBAAa;4BACZ,QAAQ;4BACR,SAAS,IAAM,oBAAoB;4BACnC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,MAAM;wCACnB,MAAM,gBAAgB,KAAK,IAAI;wCAC/B,qBACE,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,oBAAoB;4CACnC,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAc,WAAU;;;;;;;;;;;sEAE3B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EACb,KAAK,IAAI;;;;;;gEAEX,KAAK,KAAK,kBACT,8OAAC;oEAAK,WAAU;8EACb,KAAK,KAAK;;;;;;;;;;;;;;;;;;8DAKnB,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;2CApBjB,CAAC,YAAY,EAAE,KAAK;;;;;oCAuB/B;;;;;;8CAEF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,cAAW;;;;;wCACX,CAAC,UACA,qBACE,8OAAC,wIAAA,CAAA,eAAY;4CACX,SAAQ;4CACR,IAAG;4CACH,WAAU;4CACV,SAAS;gDACP,oBAAoB;gDACpB,OAAO,QAAQ,CAAC,IAAI,GAAG;4CACzB;sDACD;;;;;iEAID,8OAAC,wIAAA,CAAA,eAAY;4CACX,SAAQ;4CACR,IAAG;4CACH,WAAU;4CACV,SAAS;gDACP,oBAAoB;gDACpB,OAAO,QAAQ,CAAC,IAAI,GAAG;4CACzB;sDACD;;;;;iEAKH,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/B;uCAEe", "debugId": null}}, {"offset": {"line": 1778, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/site-config.ts"], "sourcesContent": ["/**\r\n * Site-wide configuration file\r\n *\r\n * This file contains all the site-wide configuration data like:\r\n * - Contact information\r\n * - Site metadata\r\n * - Legal information\r\n *\r\n * Update this file to change information across the entire site\r\n */\r\n\r\nexport const siteConfig = {\r\n  name: \"Dukancard\",\r\n  description:\r\n    \"Create and share digital business cards, showcase products, and connect with customers.\",\r\n  url: \"https://dukancard.in\",\r\n  ogImage: \"https://dukancard.in/opengraph-image.png\",\r\n\r\n  // Contact Information\r\n  contact: {\r\n    email: \"<EMAIL>\",\r\n    phone: \"+91 8458060663\",\r\n    address: {\r\n      street: \"Bisra Road\",\r\n      city: \"Rourkela\",\r\n      state: \"Odisha\",\r\n      postalCode: \"769001\",\r\n      country: \"India\",\r\n      full: \"Bisra Road, Rourkela, Odisha - 769001\",\r\n    },\r\n    // Hours of operation\r\n    hours: \"Monday - Friday: 9:00 AM - 6:00 PM\",\r\n  },\r\n\r\n  // Legal\r\n  legal: {\r\n    privacyPolicy: \"/privacy\",\r\n    termsOfService: \"/terms\",\r\n    refundPolicy: \"/refund\",\r\n  },\r\n\r\n  // Support\r\n  support: {\r\n    email: \"<EMAIL>\",\r\n    phone: \"+91 8458060663\",\r\n    helpCenter: \"/support\",\r\n  },\r\n\r\n  // Advertising\r\n  advertising: {\r\n    email: \"<EMAIL>\",\r\n    phone: \"+************\",\r\n    page: \"/advertise\",\r\n  },\r\n};\r\n\r\nexport type SiteConfig = typeof siteConfig;\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC;;;AAEM,MAAM,aAAa;IACxB,MAAM;IACN,aACE;IACF,KAAK;IACL,SAAS;IAET,sBAAsB;IACtB,SAAS;QACP,OAAO;QACP,OAAO;QACP,SAAS;YACP,QAAQ;YACR,MAAM;YACN,OAAO;YACP,YAAY;YACZ,SAAS;YACT,MAAM;QACR;QACA,qBAAqB;QACrB,OAAO;IACT;IAEA,QAAQ;IACR,OAAO;QACL,eAAe;QACf,gBAAgB;QAChB,cAAc;IAChB;IAEA,UAAU;IACV,SAAS;QACP,OAAO;QACP,OAAO;QACP,YAAY;IACd;IAEA,cAAc;IACd,aAAa;QACX,OAAO;QACP,OAAO;QACP,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 1835, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/Footer.tsx"], "sourcesContent": ["// components/Footer.tsx\r\n\"use client\";\r\n\r\nimport React from \"react\";\r\nimport Link from \"next/link\";\r\nimport { motion } from \"framer-motion\"; // Import motion\r\nimport { cn } from \"@/lib/utils\";\r\nimport { siteConfig } from \"@/lib/site-config\";\r\n\r\n// Define types for our footer links\r\ninterface FooterLink {\r\n  title: string;\r\n  href: string;\r\n}\r\n\r\ninterface FooterSection {\r\n  title: string;\r\n  links: FooterLink[];\r\n}\r\n\r\ninterface FooterProps {\r\n  className?: string;\r\n}\r\n\r\nconst Footer: React.FC<FooterProps> = ({ className }) => {\r\n  // We don't need to use isMobile here since we're using CSS to hide on mobile\r\n  // const isMobile = useIsMobile();\r\n  // Define footer sections and links\r\n  const footerSections: FooterSection[] = [\r\n    {\r\n      title: \"Company\",\r\n      links: [\r\n        { title: \"About Us\", href: \"/about\" },\r\n        { title: \"Contact Us\", href: \"/contact\" },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Product\",\r\n      links: [\r\n        { title: \"Features\", href: \"/features\" },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Resources\",\r\n      links: [\r\n        { title: \"Blog\", href: \"/blog\" },\r\n        { title: \"Support\", href: \"/support\" },\r\n        { title: \"Advertise\", href: siteConfig.advertising.page },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Legal\",\r\n      links: [\r\n        { title: \"Privacy Policy\", href: siteConfig.legal.privacyPolicy },\r\n        { title: \"Terms of Service\", href: siteConfig.legal.termsOfService },\r\n        { title: \"Refund Policy\", href: siteConfig.legal.refundPolicy },\r\n        { title: \"Cookie Policy\", href: \"/cookies\" },\r\n      ],\r\n    },\r\n  ];\r\n\r\n  // Animation variants\r\n  const footerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: { opacity: 1, transition: { duration: 0.5, ease: \"easeOut\" } },\r\n  };\r\n\r\n  const itemVariants = {\r\n    hidden: { opacity: 0, y: 15 },\r\n    visible: (i: number) => ({\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: { duration: 0.4, delay: i * 0.05, ease: \"easeOut\" },\r\n    }),\r\n  };\r\n\r\n  return (\r\n    <motion.footer\r\n      variants={footerVariants}\r\n      initial=\"hidden\"\r\n      whileInView=\"visible\"\r\n      viewport={{ once: true, amount: 0.2 }}\r\n      // Use theme variables for background and border\r\n      className={cn(\r\n        \"bg-gradient-to-b from-muted/50 to-background dark:from-background dark:to-black/30 border-t border-border pt-16 pb-20 md:pb-8 relative overflow-hidden\",\r\n        className\r\n      )}\r\n    >\r\n      {/* Background Elements - Use theme colors */}\r\n      <div className=\"absolute inset-0 -z-10 overflow-hidden\">\r\n        <div className=\"absolute left-1/4 bottom-1/2 w-1/3 h-1/3 bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/10 rounded-full blur-3xl opacity-60\"></div>\r\n        <div className=\"absolute right-1/4 top-1/2 w-1/4 h-1/4 bg-blue-500/5 dark:bg-blue-500/10 rounded-full blur-3xl opacity-60\"></div>\r\n      </div>\r\n\r\n      <div className=\"max-w-7xl mx-auto px-4 md:px-6 lg:px-8\">\r\n        {/* Main Footer Content */}\r\n        <motion.div\r\n          initial=\"hidden\"\r\n          whileInView=\"visible\"\r\n          viewport={{ once: true, amount: 0.2 }}\r\n          transition={{ staggerChildren: 0.1 }}\r\n          className=\"grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 md:gap-8 lg:gap-12 pb-12\"\r\n        >\r\n          {/* Company Info */}\r\n          <motion.div\r\n            variants={itemVariants}\r\n            custom={0}\r\n            className=\"col-span-2 md:col-span-3 lg:col-span-2\"\r\n          >\r\n            <div className=\"mb-6\">\r\n              <h2 className=\"text-2xl font-bold text-foreground flex items-center\">\r\n                {\" \"}\r\n                {/* Use foreground */}\r\n                <span className=\"text-[var(--brand-gold)]\">Dukan</span>card\r\n              </h2>\r\n              <p className=\"text-muted-foreground mt-4\">\r\n                {\" \"}\r\n                {/* Use muted-foreground */}\r\n                Elevate your business with our premium digital card solution.\r\n                Connect with customers, showcase your offerings, and grow your\r\n                brand.\r\n              </p>\r\n            </div>\r\n            {/* Contact information removed as per request - available on Contact and About pages */}\r\n          </motion.div>\r\n\r\n          {/* Footer Link Sections */}\r\n          {footerSections.map((section, index) => (\r\n            <motion.div\r\n              key={section.title} // Use title as key\r\n              variants={itemVariants}\r\n              custom={index + 1} // Stagger delay\r\n              className=\"lg:col-span-1\"\r\n            >\r\n              <h3 className=\"text-lg font-semibold text-foreground mb-4\">\r\n                {\" \"}\r\n                {/* Use foreground */}\r\n                {section.title}\r\n              </h3>\r\n              <ul className=\"space-y-2\">\r\n                {section.links.map((link) => (\r\n                  <li key={link.href}>\r\n                    {\" \"}\r\n                    {/* Use href as key */}\r\n                    <Link\r\n                      href={link.href}\r\n                      className=\"text-muted-foreground hover:text-[var(--brand-gold)] transition-colors duration-200 text-sm\" // Added text-sm\r\n                    >\r\n                      {link.title}\r\n                    </Link>\r\n                  </li>\r\n                ))}\r\n              </ul>\r\n            </motion.div>\r\n          ))}\r\n        </motion.div>\r\n\r\n        {/* Divider */}\r\n        <div className=\"border-t border-border py-6\">\r\n          {\" \"}\r\n          {/* Use border */}\r\n          <div className=\"flex flex-col md:flex-row justify-center items-center\">\r\n            <p className=\"text-muted-foreground text-sm mb-4 md:mb-0 text-center\">\r\n              {\" \"}\r\n              {/* Use muted-foreground */}© {new Date().getFullYear()}{\" \"}\r\n              {siteConfig.name}. All rights reserved.\r\n            </p>\r\n            {/* Optional: Add social links here if desired */}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </motion.footer>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": "AAAA,wBAAwB;;;;;AAIxB;AACA,oUAAwC,gBAAgB;AACxD;AACA;AANA;;;;;;AAuBA,MAAM,SAAgC,CAAC,EAAE,SAAS,EAAE;IAClD,6EAA6E;IAC7E,kCAAkC;IAClC,mCAAmC;IACnC,MAAM,iBAAkC;QACtC;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,OAAO;oBAAY,MAAM;gBAAS;gBACpC;oBAAE,OAAO;oBAAc,MAAM;gBAAW;aACzC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,OAAO;oBAAY,MAAM;gBAAY;aACxC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,OAAO;oBAAQ,MAAM;gBAAQ;gBAC/B;oBAAE,OAAO;oBAAW,MAAM;gBAAW;gBACrC;oBAAE,OAAO;oBAAa,MAAM,qHAAA,CAAA,aAAU,CAAC,WAAW,CAAC,IAAI;gBAAC;aACzD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,OAAO;oBAAkB,MAAM,qHAAA,CAAA,aAAU,CAAC,KAAK,CAAC,aAAa;gBAAC;gBAChE;oBAAE,OAAO;oBAAoB,MAAM,qHAAA,CAAA,aAAU,CAAC,KAAK,CAAC,cAAc;gBAAC;gBACnE;oBAAE,OAAO;oBAAiB,MAAM,qHAAA,CAAA,aAAU,CAAC,KAAK,CAAC,YAAY;gBAAC;gBAC9D;oBAAE,OAAO;oBAAiB,MAAM;gBAAW;aAC5C;QACH;KACD;IAED,qBAAqB;IACrB,MAAM,iBAAiB;QACrB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YAAE,SAAS;YAAG,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;QAAE;IACxE;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS,CAAC,IAAc,CAAC;gBACvB,SAAS;gBACT,GAAG;gBACH,YAAY;oBAAE,UAAU;oBAAK,OAAO,IAAI;oBAAM,MAAM;gBAAU;YAChE,CAAC;IACH;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,UAAU;QACV,SAAQ;QACR,aAAY;QACZ,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;QACpC,gDAAgD;QAChD,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0JACA;;0BAIF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAQ;wBACR,aAAY;wBACZ,UAAU;4BAAE,MAAM;4BAAM,QAAQ;wBAAI;wBACpC,YAAY;4BAAE,iBAAiB;wBAAI;wBACnC,WAAU;;0CAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,UAAU;gCACV,QAAQ;gCACR,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;gDACX;8DAED,8OAAC;oDAAK,WAAU;8DAA2B;;;;;;gDAAY;;;;;;;sDAEzD,8OAAC;4CAAE,WAAU;;gDACV;gDAC2B;;;;;;;;;;;;;;;;;;4BAUjC,eAAe,GAAG,CAAC,CAAC,SAAS,sBAC5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,UAAU;oCACV,QAAQ,QAAQ;oCAChB,WAAU;;sDAEV,8OAAC;4CAAG,WAAU;;gDACX;gDAEA,QAAQ,KAAK;;;;;;;sDAEhB,8OAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,8OAAC;;wDACE;sEAED,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,KAAK,IAAI;4DACf,WAAU,8FAA8F,gBAAgB;;sEAEvH,KAAK,KAAK;;;;;;;mDAPN,KAAK,IAAI;;;;;;;;;;;mCAZjB,QAAQ,KAAK;;;;;;;;;;;kCA6BxB,8OAAC;wBAAI,WAAU;;4BACZ;0CAED,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;wCACV;wCAC2B;wCAAG,IAAI,OAAO,WAAW;wCAAI;wCACxD,qHAAA,CAAA,aAAU,CAAC,IAAI;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/B;uCAEe", "debugId": null}}, {"offset": {"line": 2147, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/70\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2193, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\ninterface DialogContentProps extends React.ComponentProps<typeof DialogPrimitive.Content> {\r\n  hideClose?: boolean;\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  hideClose = false,\r\n  ...props\r\n}: DialogContentProps) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        {!hideClose && (\r\n          <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 cursor-pointer\">\r\n            <XIcon />\r\n            <span className=\"sr-only\">Close</span>\r\n          </DialogPrimitive.Close>\r\n        )}\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  )\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn(\"text-lg leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAMA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,YAAY,KAAK,EACjB,GAAG,OACgB;IACnB,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,CAAC,2BACA,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2366, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/qrCodeUtils.ts"], "sourcesContent": ["/**\n * QR Code validation utilities for Next.js\n * Adapted from React Native implementation for web use\n */\n\nexport interface QRCodeValidationResult {\n  isValid: boolean;\n  error?: string;\n  businessSlug?: string;\n  url?: string;\n}\n\n/**\n * Validates if a business slug has the correct format\n * @param slug - The business slug to validate\n * @returns Validation result\n */\nfunction validateBusinessSlug(slug: string): { isValid: boolean; error?: string } {\n  if (!slug || typeof slug !== 'string') {\n    return {\n      isValid: false,\n      error: 'Business slug is required'\n    };\n  }\n\n  const cleanSlug = slug.trim();\n\n  if (!cleanSlug) {\n    return {\n      isValid: false,\n      error: 'Business slug cannot be empty'\n    };\n  }\n\n  // Check length (3-50 characters)\n  if (cleanSlug.length < 3 || cleanSlug.length > 50) {\n    return {\n      isValid: false,\n      error: 'Business slug must be between 3 and 50 characters'\n    };\n  }\n\n  // Check format: lowercase letters, numbers, hyphens only\n  const slugPattern = /^[a-z0-9-]+$/;\n  if (!slugPattern.test(cleanSlug)) {\n    return {\n      isValid: false,\n      error: 'Business slug can only contain lowercase letters, numbers, and hyphens'\n    };\n  }\n\n  // Cannot start or end with hyphen\n  if (cleanSlug.startsWith('-') || cleanSlug.endsWith('-')) {\n    return {\n      isValid: false,\n      error: 'Business slug cannot start or end with a hyphen'\n    };\n  }\n\n  // Cannot have consecutive hyphens\n  if (cleanSlug.includes('--')) {\n    return {\n      isValid: false,\n      error: 'Business slug cannot contain consecutive hyphens'\n    };\n  }\n\n  return {\n    isValid: true\n  };\n}\n\n/**\n * Validates if a QR code contains a valid dukancard.in URL\n * @param qrData - The raw data from the QR code scan\n * @returns Validation result with business slug if valid\n */\nexport function validateQRCodeUrl(qrData: string): QRCodeValidationResult {\n  if (!qrData || typeof qrData !== 'string') {\n    return {\n      isValid: false,\n      error: 'Invalid QR code data'\n    };\n  }\n\n  // Clean the data\n  const cleanData = qrData.trim();\n\n  if (!cleanData) {\n    return {\n      isValid: false,\n      error: 'Empty QR code data'\n    };\n  }\n\n  // Check if it's a valid URL\n  let url: URL;\n  try {\n    // Handle cases where the QR code might not have a protocol\n    const urlString = cleanData.startsWith('http') ? cleanData : `https://${cleanData}`;\n    url = new URL(urlString);\n  } catch (_error) {\n    return {\n      isValid: false,\n      error: 'QR code does not contain a valid URL'\n    };\n  }\n\n  // Check if it's a dukancard.in domain\n  const validDomains = ['dukancard.in', 'www.dukancard.in'];\n  if (!validDomains.includes(url.hostname.toLowerCase())) {\n    return {\n      isValid: false,\n      error: 'QR code is not from Dukancard'\n    };\n  }\n\n  // Extract the business slug from the path\n  const pathSegments = url.pathname.split('/').filter(segment => segment.length > 0);\n  \n  if (pathSegments.length === 0) {\n    return {\n      isValid: false,\n      error: 'QR code does not contain a business profile URL'\n    };\n  }\n\n  const businessSlug = pathSegments[0];\n\n  // Validate the business slug format\n  const slugValidation = validateBusinessSlug(businessSlug);\n  if (!slugValidation.isValid) {\n    return {\n      isValid: false,\n      error: slugValidation.error || 'Invalid business URL format'\n    };\n  }\n\n  return {\n    isValid: true,\n    businessSlug,\n    url: url.toString()\n  };\n}\n\n/**\n * Generates a dukancard.in URL for a business slug\n * @param businessSlug - The business slug\n * @returns Complete dukancard.in URL\n */\nexport function generateDukancardUrl(businessSlug: string): string {\n  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://dukancard.in';\n  return `${baseUrl}/${businessSlug}`;\n}\n\n/**\n * Extracts business slug from a dukancard.in URL\n * @param url - The dukancard.in URL\n * @returns Business slug if valid, null otherwise\n */\nexport function extractBusinessSlugFromUrl(url: string): string | null {\n  const validation = validateQRCodeUrl(url);\n  return validation.isValid ? validation.businessSlug! : null;\n}\n\n/**\n * Checks if a URL is a valid dukancard.in business URL\n * @param url - The URL to check\n * @returns True if it's a valid dukancard business URL\n */\nexport function isDukancardBusinessUrl(url: string): boolean {\n  const validation = validateQRCodeUrl(url);\n  return validation.isValid;\n}\n\n/**\n * Validates QR code data and provides user-friendly error messages\n * @param qrData - The raw QR code data\n * @returns User-friendly validation result\n */\nexport function validateQRCodeForUser(qrData: string): QRCodeValidationResult {\n  const result = validateQRCodeUrl(qrData);\n  \n  if (!result.isValid) {\n    // Provide more user-friendly error messages\n    let userFriendlyError = result.error;\n    \n    if (result.error?.includes('not from Dukancard')) {\n      userFriendlyError = 'This QR code is not from Dukancard. Please scan a valid Dukancard business QR code.';\n    } else if (result.error?.includes('not contain a valid URL')) {\n      userFriendlyError = 'Invalid QR code format. Please scan a valid Dukancard business QR code.';\n    } else if (result.error?.includes('business profile URL')) {\n      userFriendlyError = 'This QR code does not link to a business profile. Please scan a valid Dukancard business QR code.';\n    }\n    \n    return {\n      ...result,\n      error: userFriendlyError\n    };\n  }\n  \n  return result;\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AASD;;;;CAIC,GACD,SAAS,qBAAqB,IAAY;IACxC,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;QACrC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,MAAM,YAAY,KAAK,IAAI;IAE3B,IAAI,CAAC,WAAW;QACd,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,iCAAiC;IACjC,IAAI,UAAU,MAAM,GAAG,KAAK,UAAU,MAAM,GAAG,IAAI;QACjD,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,yDAAyD;IACzD,MAAM,cAAc;IACpB,IAAI,CAAC,YAAY,IAAI,CAAC,YAAY;QAChC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,kCAAkC;IAClC,IAAI,UAAU,UAAU,CAAC,QAAQ,UAAU,QAAQ,CAAC,MAAM;QACxD,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,kCAAkC;IAClC,IAAI,UAAU,QAAQ,CAAC,OAAO;QAC5B,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,OAAO;QACL,SAAS;IACX;AACF;AAOO,SAAS,kBAAkB,MAAc;IAC9C,IAAI,CAAC,UAAU,OAAO,WAAW,UAAU;QACzC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,iBAAiB;IACjB,MAAM,YAAY,OAAO,IAAI;IAE7B,IAAI,CAAC,WAAW;QACd,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,4BAA4B;IAC5B,IAAI;IACJ,IAAI;QACF,2DAA2D;QAC3D,MAAM,YAAY,UAAU,UAAU,CAAC,UAAU,YAAY,CAAC,QAAQ,EAAE,WAAW;QACnF,MAAM,IAAI,IAAI;IAChB,EAAE,OAAO,QAAQ;QACf,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,sCAAsC;IACtC,MAAM,eAAe;QAAC;QAAgB;KAAmB;IACzD,IAAI,CAAC,aAAa,QAAQ,CAAC,IAAI,QAAQ,CAAC,WAAW,KAAK;QACtD,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,0CAA0C;IAC1C,MAAM,eAAe,IAAI,QAAQ,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,CAAA,UAAW,QAAQ,MAAM,GAAG;IAEhF,IAAI,aAAa,MAAM,KAAK,GAAG;QAC7B,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,MAAM,eAAe,YAAY,CAAC,EAAE;IAEpC,oCAAoC;IACpC,MAAM,iBAAiB,qBAAqB;IAC5C,IAAI,CAAC,eAAe,OAAO,EAAE;QAC3B,OAAO;YACL,SAAS;YACT,OAAO,eAAe,KAAK,IAAI;QACjC;IACF;IAEA,OAAO;QACL,SAAS;QACT;QACA,KAAK,IAAI,QAAQ;IACnB;AACF;AAOO,SAAS,qBAAqB,YAAoB;IACvD,MAAM,UAAU,6DAAoC;IACpD,OAAO,GAAG,QAAQ,CAAC,EAAE,cAAc;AACrC;AAOO,SAAS,2BAA2B,GAAW;IACpD,MAAM,aAAa,kBAAkB;IACrC,OAAO,WAAW,OAAO,GAAG,WAAW,YAAY,GAAI;AACzD;AAOO,SAAS,uBAAuB,GAAW;IAChD,MAAM,aAAa,kBAAkB;IACrC,OAAO,WAAW,OAAO;AAC3B;AAOO,SAAS,sBAAsB,MAAc;IAClD,MAAM,SAAS,kBAAkB;IAEjC,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,4CAA4C;QAC5C,IAAI,oBAAoB,OAAO,KAAK;QAEpC,IAAI,OAAO,KAAK,EAAE,SAAS,uBAAuB;YAChD,oBAAoB;QACtB,OAAO,IAAI,OAAO,KAAK,EAAE,SAAS,4BAA4B;YAC5D,oBAAoB;QACtB,OAAO,IAAI,OAAO,KAAK,EAAE,SAAS,yBAAyB;YACzD,oBAAoB;QACtB;QAEA,OAAO;YACL,GAAG,MAAM;YACT,OAAO;QACT;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2525, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/cameraUtils.ts"], "sourcesContent": ["/**\n * Camera detection and permission utilities for web browsers\n */\n\nexport interface CameraCapabilities {\n  hasCamera: boolean;\n  hasPermission: boolean | null; // null means unknown/not requested yet\n  isSecureContext: boolean;\n  supportedConstraints: MediaTrackSupportedConstraints | null;\n  error?: string;\n}\n\nexport interface CameraDevice {\n  deviceId: string;\n  label: string;\n  kind: 'videoinput';\n  groupId: string;\n}\n\n/**\n * Check if the browser supports camera access\n * @returns True if getUserMedia is supported\n */\nexport function isCameraSupported(): boolean {\n  return !!(\n    navigator.mediaDevices &&\n    navigator.mediaDevices.getUserMedia &&\n    typeof navigator.mediaDevices.getUserMedia === 'function'\n  );\n}\n\n/**\n * Check if the current context is secure (HTTPS or localhost)\n * Camera access requires secure context\n * @returns True if context is secure\n */\nexport function isSecureContext(): boolean {\n  return window.isSecureContext || location.protocol === 'https:' || location.hostname === 'localhost';\n}\n\n/**\n * Get available camera devices\n * @returns Promise with array of camera devices\n */\nexport async function getCameraDevices(): Promise<CameraDevice[]> {\n  if (!isCameraSupported()) {\n    throw new Error('Camera not supported in this browser');\n  }\n\n  try {\n    const devices = await navigator.mediaDevices.enumerateDevices();\n    return devices\n      .filter(device => device.kind === 'videoinput')\n      .map(device => ({\n        deviceId: device.deviceId,\n        label: device.label || `Camera ${device.deviceId.slice(0, 8)}`,\n        kind: device.kind as 'videoinput',\n        groupId: device.groupId\n      }));\n  } catch (error) {\n    console.error('Error enumerating camera devices:', error);\n    throw new Error('Failed to enumerate camera devices');\n  }\n}\n\n/**\n * Check camera permission status\n * @returns Permission state: 'granted', 'denied', 'prompt', or null if not supported\n */\nexport async function checkCameraPermission(): Promise<PermissionState | null> {\n  if (!navigator.permissions || !navigator.permissions.query) {\n    return null; // Permissions API not supported\n  }\n\n  try {\n    const permission = await navigator.permissions.query({ name: 'camera' as PermissionName });\n    return permission.state;\n  } catch (error) {\n    console.warn('Error checking camera permission:', error);\n    return null;\n  }\n}\n\n/**\n * Request camera access and check capabilities\n * @param constraints - Optional camera constraints\n * @returns Promise with camera capabilities\n */\nexport async function requestCameraAccess(\n  constraints: MediaStreamConstraints = { video: true }\n): Promise<{ stream: MediaStream; capabilities: CameraCapabilities }> {\n  if (!isCameraSupported()) {\n    throw new Error('Camera not supported in this browser');\n  }\n\n  if (!isSecureContext()) {\n    throw new Error('Camera access requires HTTPS or localhost');\n  }\n\n  try {\n    const stream = await navigator.mediaDevices.getUserMedia(constraints);\n    \n    const capabilities: CameraCapabilities = {\n      hasCamera: true,\n      hasPermission: true,\n      isSecureContext: isSecureContext(),\n      supportedConstraints: navigator.mediaDevices.getSupportedConstraints()\n    };\n\n    return { stream, capabilities };\n  } catch (error: unknown) {\n    let errorMessage = 'Failed to access camera';\n    let hasPermission = false;\n\n    if (error instanceof Error) {\n      if (error.name === 'NotAllowedError') {\n        errorMessage = 'Camera access denied by user';\n        hasPermission = false;\n      } else if (error.name === 'NotFoundError') {\n        errorMessage = 'No camera found on this device';\n      } else if (error.name === 'NotReadableError') {\n        errorMessage = 'Camera is already in use by another application';\n      } else if (error.name === 'OverconstrainedError') {\n        errorMessage = 'Camera does not support the requested constraints';\n      } else if (error.name === 'SecurityError') {\n        errorMessage = 'Camera access blocked due to security restrictions';\n      }\n    }\n\n    const capabilities: CameraCapabilities = {\n      hasCamera: error instanceof Error ? error.name !== 'NotFoundError' : false,\n      hasPermission,\n      isSecureContext: isSecureContext(),\n      supportedConstraints: navigator.mediaDevices?.getSupportedConstraints() || null,\n      error: errorMessage\n    };\n\n    throw { error: errorMessage, capabilities };\n  }\n}\n\n/**\n * Get comprehensive camera capabilities without requesting access\n * @returns Promise with camera capabilities\n */\nexport async function getCameraCapabilities(): Promise<CameraCapabilities> {\n  const capabilities: CameraCapabilities = {\n    hasCamera: false,\n    hasPermission: null,\n    isSecureContext: isSecureContext(),\n    supportedConstraints: null\n  };\n\n  if (!isCameraSupported()) {\n    capabilities.error = 'Camera not supported in this browser';\n    return capabilities;\n  }\n\n  if (!isSecureContext()) {\n    capabilities.error = 'Camera access requires HTTPS or localhost';\n    return capabilities;\n  }\n\n  capabilities.supportedConstraints = navigator.mediaDevices.getSupportedConstraints();\n\n  try {\n    // Check permission status\n    const permissionStatus = await checkCameraPermission();\n    capabilities.hasPermission = permissionStatus === 'granted';\n\n    // Try to enumerate devices to check if camera exists\n    const devices = await getCameraDevices();\n    capabilities.hasCamera = devices.length > 0;\n\n    if (!capabilities.hasCamera) {\n      capabilities.error = 'No camera found on this device';\n    }\n  } catch (error: unknown) {\n    capabilities.error = error instanceof Error ? error.message : 'Failed to check camera capabilities';\n  }\n\n  return capabilities;\n}\n\n/**\n * Stop a media stream and release camera resources\n * @param stream - The media stream to stop\n */\nexport function stopCameraStream(stream: MediaStream): void {\n  if (stream) {\n    stream.getTracks().forEach(track => {\n      track.stop();\n    });\n  }\n}\n\n/**\n * Check if the device is likely a mobile device\n * @returns True if device appears to be mobile\n */\nexport function isMobileDevice(): boolean {\n  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n}\n\n/**\n * Get preferred camera constraints for QR scanning\n * @param preferredDeviceId - Optional preferred camera device ID\n * @returns Camera constraints optimized for QR scanning\n */\nexport function getQRScanConstraints(preferredDeviceId?: string): MediaStreamConstraints {\n  const constraints: MediaStreamConstraints = {\n    video: {\n      width: { ideal: 1280 },\n      height: { ideal: 720 },\n      facingMode: isMobileDevice() ? 'environment' : 'user', // Back camera on mobile, front on desktop\n      frameRate: { ideal: 30 }\n    }\n  };\n\n  if (preferredDeviceId) {\n    (constraints.video as MediaTrackConstraints).deviceId = { exact: preferredDeviceId };\n  }\n\n  return constraints;\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;;AAqBM,SAAS;IACd,OAAO,CAAC,CAAC,CACP,UAAU,YAAY,IACtB,UAAU,YAAY,CAAC,YAAY,IACnC,OAAO,UAAU,YAAY,CAAC,YAAY,KAAK,UACjD;AACF;AAOO,SAAS;IACd,OAAO,OAAO,eAAe,IAAI,SAAS,QAAQ,KAAK,YAAY,SAAS,QAAQ,KAAK;AAC3F;AAMO,eAAe;IACpB,IAAI,CAAC,qBAAqB;QACxB,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI;QACF,MAAM,UAAU,MAAM,UAAU,YAAY,CAAC,gBAAgB;QAC7D,OAAO,QACJ,MAAM,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK,cACjC,GAAG,CAAC,CAAA,SAAU,CAAC;gBACd,UAAU,OAAO,QAAQ;gBACzB,OAAO,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI;gBAC9D,MAAM,OAAO,IAAI;gBACjB,SAAS,OAAO,OAAO;YACzB,CAAC;IACL,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM,IAAI,MAAM;IAClB;AACF;AAMO,eAAe;IACpB,IAAI,CAAC,UAAU,WAAW,IAAI,CAAC,UAAU,WAAW,CAAC,KAAK,EAAE;QAC1D,OAAO,MAAM,gCAAgC;IAC/C;IAEA,IAAI;QACF,MAAM,aAAa,MAAM,UAAU,WAAW,CAAC,KAAK,CAAC;YAAE,MAAM;QAA2B;QACxF,OAAO,WAAW,KAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,qCAAqC;QAClD,OAAO;IACT;AACF;AAOO,eAAe,oBACpB,cAAsC;IAAE,OAAO;AAAK,CAAC;IAErD,IAAI,CAAC,qBAAqB;QACxB,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,CAAC,mBAAmB;QACtB,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI;QACF,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;QAEzD,MAAM,eAAmC;YACvC,WAAW;YACX,eAAe;YACf,iBAAiB;YACjB,sBAAsB,UAAU,YAAY,CAAC,uBAAuB;QACtE;QAEA,OAAO;YAAE;YAAQ;QAAa;IAChC,EAAE,OAAO,OAAgB;QACvB,IAAI,eAAe;QACnB,IAAI,gBAAgB;QAEpB,IAAI,iBAAiB,OAAO;YAC1B,IAAI,MAAM,IAAI,KAAK,mBAAmB;gBACpC,eAAe;gBACf,gBAAgB;YAClB,OAAO,IAAI,MAAM,IAAI,KAAK,iBAAiB;gBACzC,eAAe;YACjB,OAAO,IAAI,MAAM,IAAI,KAAK,oBAAoB;gBAC5C,eAAe;YACjB,OAAO,IAAI,MAAM,IAAI,KAAK,wBAAwB;gBAChD,eAAe;YACjB,OAAO,IAAI,MAAM,IAAI,KAAK,iBAAiB;gBACzC,eAAe;YACjB;QACF;QAEA,MAAM,eAAmC;YACvC,WAAW,iBAAiB,QAAQ,MAAM,IAAI,KAAK,kBAAkB;YACrE;YACA,iBAAiB;YACjB,sBAAsB,UAAU,YAAY,EAAE,6BAA6B;YAC3E,OAAO;QACT;QAEA,MAAM;YAAE,OAAO;YAAc;QAAa;IAC5C;AACF;AAMO,eAAe;IACpB,MAAM,eAAmC;QACvC,WAAW;QACX,eAAe;QACf,iBAAiB;QACjB,sBAAsB;IACxB;IAEA,IAAI,CAAC,qBAAqB;QACxB,aAAa,KAAK,GAAG;QACrB,OAAO;IACT;IAEA,IAAI,CAAC,mBAAmB;QACtB,aAAa,KAAK,GAAG;QACrB,OAAO;IACT;IAEA,aAAa,oBAAoB,GAAG,UAAU,YAAY,CAAC,uBAAuB;IAElF,IAAI;QACF,0BAA0B;QAC1B,MAAM,mBAAmB,MAAM;QAC/B,aAAa,aAAa,GAAG,qBAAqB;QAElD,qDAAqD;QACrD,MAAM,UAAU,MAAM;QACtB,aAAa,SAAS,GAAG,QAAQ,MAAM,GAAG;QAE1C,IAAI,CAAC,aAAa,SAAS,EAAE;YAC3B,aAAa,KAAK,GAAG;QACvB;IACF,EAAE,OAAO,OAAgB;QACvB,aAAa,KAAK,GAAG,iBAAiB,QAAQ,MAAM,OAAO,GAAG;IAChE;IAEA,OAAO;AACT;AAMO,SAAS,iBAAiB,MAAmB;IAClD,IAAI,QAAQ;QACV,OAAO,SAAS,GAAG,OAAO,CAAC,CAAA;YACzB,MAAM,IAAI;QACZ;IACF;AACF;AAMO,SAAS;IACd,OAAO,iEAAiE,IAAI,CAAC,UAAU,SAAS;AAClG;AAOO,SAAS,qBAAqB,iBAA0B;IAC7D,MAAM,cAAsC;QAC1C,OAAO;YACL,OAAO;gBAAE,OAAO;YAAK;YACrB,QAAQ;gBAAE,OAAO;YAAI;YACrB,YAAY,mBAAmB,gBAAgB;YAC/C,WAAW;gBAAE,OAAO;YAAG;QACzB;IACF;IAEA,IAAI,mBAAmB;QACpB,YAAY,KAAK,CAA2B,QAAQ,GAAG;YAAE,OAAO;QAAkB;IACrF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2695, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/qr/QRScanner.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useRef, useState } from 'react';\r\nimport { Html5QrcodeScanner, Html5QrcodeScanType } from 'html5-qrcode';\r\nimport { validateQRCodeForUser } from '@/lib/utils/qrCodeUtils';\r\nimport { requestCameraAccess } from '@/lib/utils/cameraUtils';\r\nimport type { CameraCapabilities } from '@/lib/utils/cameraUtils';\r\nimport './qr-scanner.css';\r\n\r\ninterface QRScannerProps {\r\n  onScanSuccess: (_businessSlug: string) => void;\r\n  onScanError?: (_error: string) => void;\r\n  onClose?: () => void;\r\n  className?: string;\r\n}\r\n\r\nconst QRScanner: React.FC<QRScannerProps> = ({\r\n  onScanSuccess,\r\n  onScanError,\r\n  onClose,\r\n  className = \"\"\r\n}) => {\r\n  const scannerRef = useRef<Html5QrcodeScanner | null>(null);\r\n  const elementRef = useRef<HTMLDivElement>(null);\r\n  const [isScanning, setIsScanning] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [capabilities, setCapabilities] = useState<CameraCapabilities | null>(null);\r\n  const [isProcessing, setIsProcessing] = useState(false);\r\n\r\n  const qrCodeRegionId = \"qr-scanner-region\";\r\n\r\n  // Request camera access and check capabilities on mount\r\n  useEffect(() => {\r\n    const initializeCamera = async () => {\r\n      try {\r\n        const { stream, capabilities: newCaps } = await requestCameraAccess();\r\n        setCapabilities(newCaps);\r\n        // Stop the stream immediately after getting capabilities, as html5-qrcode will create its own\r\n        stream.getTracks().forEach(track => track.stop());\r\n\r\n        if (newCaps.error) {\r\n          setError(newCaps.error);\r\n          onScanError?.(newCaps.error);\r\n        }\r\n      } catch (err: unknown) {\r\n        const errorMsg = (err as { error?: string })?.error || (err instanceof Error ? err.message : 'Failed to get camera access');\r\n        const caps = (err as { capabilities?: CameraCapabilities })?.capabilities || { hasCamera: false, hasPermission: false, isSecureContext: false, supportedConstraints: null, error: errorMsg };\r\n        setCapabilities(caps);\r\n        setError(errorMsg);\r\n        onScanError?.(errorMsg);\r\n      }\r\n    };\r\n\r\n    initializeCamera();\r\n  }, [onScanError]);\r\n\r\n  // Initialize scanner when capabilities are ready\r\n  useEffect(() => {\r\n    if (!capabilities || capabilities.error || isScanning) {\r\n      return;\r\n    }\r\n\r\n    const initializeScanner = async () => {\r\n      try {\r\n        setIsScanning(true);\r\n        setError(null);\r\n\r\n        // Create scanner configuration\r\n        const config = {\r\n          fps: 10,\r\n          qrbox: {\r\n            width: 250,\r\n            height: 250,\r\n          },\r\n          aspectRatio: 1.0,\r\n          disableFlip: false,\r\n          supportedScanTypes: [Html5QrcodeScanType.SCAN_TYPE_CAMERA],\r\n          showTorchButtonIfSupported: true,\r\n          showZoomSliderIfSupported: false,\r\n          defaultZoomValueIfSupported: 1,\r\n          // Custom styling to match theme\r\n          colorScheme: 'dark',\r\n        };\r\n\r\n        // Success callback\r\n        const onScanSuccessCallback = async (decodedText: string) => {\r\n          if (isProcessing) return; // Prevent multiple scans\r\n          \r\n          setIsProcessing(true);\r\n          \r\n          try {\r\n            // Validate the QR code\r\n            const validation = validateQRCodeForUser(decodedText);\r\n            \r\n            if (!validation.isValid) {\r\n              const errorMessage = validation.error || 'Invalid QR code';\r\n              setError(errorMessage);\r\n              onScanError?.(errorMessage);\r\n              \r\n              // Reset processing after a delay\r\n              setTimeout(() => {\r\n                setIsProcessing(false);\r\n              }, 2000);\r\n              return;\r\n            }\r\n\r\n            // Extract business slug and call success callback\r\n            const businessSlug = validation.businessSlug!;\r\n            onScanSuccess(businessSlug);\r\n            \r\n          } catch (err: unknown) {\r\n            const errorMessage = err instanceof Error ? err.message : 'Failed to process QR code';\r\n            setError(errorMessage);\r\n            onScanError?.(errorMessage);\r\n            setIsProcessing(false);\r\n          }\r\n        };\r\n\r\n        // Error callback\r\n        const onScanErrorCallback = (errorMessage: string) => {\r\n          // Only log errors, don't show them to user (too noisy)\r\n          console.debug('QR scan error:', errorMessage);\r\n        };\r\n\r\n        // Create and start scanner\r\n        const scanner = new Html5QrcodeScanner(\r\n          qrCodeRegionId,\r\n          config,\r\n          false // verbose\r\n        );\r\n\r\n        scannerRef.current = scanner;\r\n        scanner.render(onScanSuccessCallback, onScanErrorCallback);\r\n\r\n      } catch (err: unknown) {\r\n        const errorMessage = err instanceof Error ? err.message : 'Failed to initialize QR scanner';\r\n        setError(errorMessage);\r\n        onScanError?.(errorMessage);\r\n        setIsScanning(false);\r\n      }\r\n    };\r\n\r\n    initializeScanner();\r\n\r\n    // Cleanup function\r\n    return () => {\r\n      if (scannerRef.current) {\r\n        scannerRef.current.clear().catch((err) => {\r\n          console.error('Error clearing QR scanner:', err);\r\n        });\r\n        scannerRef.current = null;\r\n      }\r\n      setIsScanning(false);\r\n      setIsProcessing(false);\r\n    };\r\n  }, [capabilities, onScanSuccess, onScanError, isProcessing, isScanning]);\r\n\r\n  // Cleanup on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      if (scannerRef.current) {\r\n        scannerRef.current.clear().catch((err) => {\r\n          console.error('Error clearing QR scanner on unmount:', err);\r\n        });\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  if (error) {\r\n    return (\r\n      <div className={`flex flex-col items-center justify-center p-8 text-center ${className}`}>\r\n        <div className=\"text-red-500 mb-4\">\r\n          <svg className=\"w-16 h-16 mx-auto mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n          </svg>\r\n        </div>\r\n        <h3 className=\"text-lg font-semibold text-foreground mb-2\">Camera Access Required</h3>\r\n        <p className=\"text-muted-foreground mb-4\">{error}</p>\r\n        {onClose && (\r\n          <button\r\n            onClick={onClose}\r\n            className=\"px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors\"\r\n          >\r\n            Close\r\n          </button>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!capabilities) {\r\n    return (\r\n      <div className={`flex items-center justify-center p-8 ${className}`}>\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\r\n          <p className=\"text-muted-foreground\">Checking camera availability...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={`relative ${className}`}>\r\n      {isProcessing && (\r\n        <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center z-10 rounded-lg\">\r\n          <div className=\"bg-white rounded-lg p-6 text-center\">\r\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\r\n            <p className=\"text-foreground\">Processing QR code...</p>\r\n          </div>\r\n        </div>\r\n      )}\r\n      \r\n      <div \r\n        id={qrCodeRegionId} \r\n        ref={elementRef}\r\n        className=\"w-full\"\r\n      />\r\n      \r\n      <div className=\"mt-4 text-center\">\r\n        <p className=\"text-sm text-muted-foreground\">\r\n          Point your camera at a Dukancard QR code\r\n        </p>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default QRScanner;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AALA;;;;;;;AAgBA,MAAM,YAAsC,CAAC,EAC3C,aAAa,EACb,WAAW,EACX,OAAO,EACP,YAAY,EAAE,EACf;IACC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA6B;IACrD,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IAC5E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,iBAAiB;IAEvB,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI;gBACF,MAAM,EAAE,MAAM,EAAE,cAAc,OAAO,EAAE,GAAG,MAAM,CAAA,GAAA,2HAAA,CAAA,sBAAmB,AAAD;gBAClE,gBAAgB;gBAChB,8FAA8F;gBAC9F,OAAO,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;gBAE9C,IAAI,QAAQ,KAAK,EAAE;oBACjB,SAAS,QAAQ,KAAK;oBACtB,cAAc,QAAQ,KAAK;gBAC7B;YACF,EAAE,OAAO,KAAc;gBACrB,MAAM,WAAW,AAAC,KAA4B,SAAS,CAAC,eAAe,QAAQ,IAAI,OAAO,GAAG,6BAA6B;gBAC1H,MAAM,OAAO,AAAC,KAA+C,gBAAgB;oBAAE,WAAW;oBAAO,eAAe;oBAAO,iBAAiB;oBAAO,sBAAsB;oBAAM,OAAO;gBAAS;gBAC3L,gBAAgB;gBAChB,SAAS;gBACT,cAAc;YAChB;QACF;QAEA;IACF,GAAG;QAAC;KAAY;IAEhB,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,gBAAgB,aAAa,KAAK,IAAI,YAAY;YACrD;QACF;QAEA,MAAM,oBAAoB;YACxB,IAAI;gBACF,cAAc;gBACd,SAAS;gBAET,+BAA+B;gBAC/B,MAAM,SAAS;oBACb,KAAK;oBACL,OAAO;wBACL,OAAO;wBACP,QAAQ;oBACV;oBACA,aAAa;oBACb,aAAa;oBACb,oBAAoB;wBAAC,8IAAA,CAAA,sBAAmB,CAAC,gBAAgB;qBAAC;oBAC1D,4BAA4B;oBAC5B,2BAA2B;oBAC3B,6BAA6B;oBAC7B,gCAAgC;oBAChC,aAAa;gBACf;gBAEA,mBAAmB;gBACnB,MAAM,wBAAwB,OAAO;oBACnC,IAAI,cAAc,QAAQ,yBAAyB;oBAEnD,gBAAgB;oBAEhB,IAAI;wBACF,uBAAuB;wBACvB,MAAM,aAAa,CAAA,GAAA,2HAAA,CAAA,wBAAqB,AAAD,EAAE;wBAEzC,IAAI,CAAC,WAAW,OAAO,EAAE;4BACvB,MAAM,eAAe,WAAW,KAAK,IAAI;4BACzC,SAAS;4BACT,cAAc;4BAEd,iCAAiC;4BACjC,WAAW;gCACT,gBAAgB;4BAClB,GAAG;4BACH;wBACF;wBAEA,kDAAkD;wBAClD,MAAM,eAAe,WAAW,YAAY;wBAC5C,cAAc;oBAEhB,EAAE,OAAO,KAAc;wBACrB,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;wBAC1D,SAAS;wBACT,cAAc;wBACd,gBAAgB;oBAClB;gBACF;gBAEA,iBAAiB;gBACjB,MAAM,sBAAsB,CAAC;oBAC3B,uDAAuD;oBACvD,QAAQ,KAAK,CAAC,kBAAkB;gBAClC;gBAEA,2BAA2B;gBAC3B,MAAM,UAAU,IAAI,oKAAA,CAAA,qBAAkB,CACpC,gBACA,QACA,MAAM,UAAU;;gBAGlB,WAAW,OAAO,GAAG;gBACrB,QAAQ,MAAM,CAAC,uBAAuB;YAExC,EAAE,OAAO,KAAc;gBACrB,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,SAAS;gBACT,cAAc;gBACd,cAAc;YAChB;QACF;QAEA;QAEA,mBAAmB;QACnB,OAAO;YACL,IAAI,WAAW,OAAO,EAAE;gBACtB,WAAW,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;oBAChC,QAAQ,KAAK,CAAC,8BAA8B;gBAC9C;gBACA,WAAW,OAAO,GAAG;YACvB;YACA,cAAc;YACd,gBAAgB;QAClB;IACF,GAAG;QAAC;QAAc;QAAe;QAAa;QAAc;KAAW;IAEvE,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,WAAW,OAAO,EAAE;gBACtB,WAAW,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;oBAChC,QAAQ,KAAK,CAAC,yCAAyC;gBACzD;YACF;QACF;IACF,GAAG,EAAE;IAEL,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAW,CAAC,0DAA0D,EAAE,WAAW;;8BACtF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;wBAAyB,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCAChF,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;8BAGzE,8OAAC;oBAAG,WAAU;8BAA6C;;;;;;8BAC3D,8OAAC;oBAAE,WAAU;8BAA8B;;;;;;gBAC1C,yBACC,8OAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;;IAMT;IAEA,IAAI,CAAC,cAAc;QACjB,qBACE,8OAAC;YAAI,WAAW,CAAC,qCAAqC,EAAE,WAAW;sBACjE,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;;YACpC,8BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAkB;;;;;;;;;;;;;;;;;0BAKrC,8OAAC;gBACC,IAAI;gBACJ,KAAK;gBACL,WAAU;;;;;;0BAGZ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAgC;;;;;;;;;;;;;;;;;AAMrD;uCAEe", "debugId": null}}, {"offset": {"line": 3018, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/qr/QRScannerModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useCallback } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>ontent,\r\n  <PERSON><PERSON>Header,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Upload, Camera } from \"lucide-react\";\r\nimport { toast } from \"sonner\";\r\nimport QRScanner from \"./QRScanner\";\r\nimport { Html5Qrcode } from \"html5-qrcode\";\r\nimport { validateQRCodeForUser } from \"@/lib/utils/qrCodeUtils\";\r\n\r\ninterface QRScannerModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onScanSuccess: (_businessSlug: string) => void;\r\n}\r\n\r\nconst QRScannerModal: React.FC<QRScannerModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  onScanSuccess,\r\n}) => {\r\n  const [isProcessing, setIsProcessing] = useState(false);\r\n  const [scanMode, setScanMode] = useState<\"camera\" | \"upload\">(\"camera\");\r\n\r\n  const handleScanSuccess = useCallback(\r\n    (businessSlug: string) => {\r\n      setIsProcessing(true);\r\n\r\n      // Add a small delay to show success state\r\n      setTimeout(() => {\r\n        toast.success(\"QR code scanned successfully!\");\r\n        onScanSuccess(businessSlug);\r\n        onClose();\r\n        setIsProcessing(false);\r\n      }, 500);\r\n    },\r\n    [onScanSuccess, onClose]\r\n  );\r\n\r\n  const handleScanError = useCallback((error: string) => {\r\n    console.error(\"QR scan error:\", error);\r\n    toast.error(error);\r\n  }, []);\r\n\r\n  const handleFileUpload = useCallback(\r\n    async (event: React.ChangeEvent<HTMLInputElement>) => {\r\n      const file = event.target.files?.[0];\r\n      if (!file) return;\r\n\r\n      // Check if file is an image\r\n      if (!file.type.startsWith(\"image/\")) {\r\n        toast.error(\"Please select an image file\");\r\n        return;\r\n      }\r\n\r\n      setIsProcessing(true);\r\n\r\n      try {\r\n        let html5QrCode: Html5Qrcode | undefined; // Declare outside to ensure scope for finally\r\n        try {\r\n          html5QrCode = new Html5Qrcode(\"qr-file-scanner-region\");\r\n          const qrCodeResult = await html5QrCode.scanFile(file);\r\n          console.log(\"Html5Qrcode scan result:\", qrCodeResult);\r\n\r\n          // Validate the QR code\r\n          const validation = validateQRCodeForUser(qrCodeResult);\r\n          console.log(\"QR code validation result:\", validation);\r\n\r\n          if (!validation.isValid) {\r\n            const errorMessage = validation.error || \"Invalid QR code\";\r\n            toast.error(errorMessage);\r\n            setIsProcessing(false);\r\n            return;\r\n          }\r\n\r\n          // Extract business slug and call success callback\r\n          const businessSlug = validation.businessSlug!;\r\n          handleScanSuccess(businessSlug);\r\n        } catch (err: unknown) {\r\n          console.error(\"QR code scan from image failed:\", err);\r\n          let errorMessage = \"Failed to process image\";\r\n          if (typeof err === \"string\") {\r\n            if (err.includes(\"QR code not found\")) {\r\n              errorMessage =\r\n                \"No Dukancard QR code found in the image. Please try another image.\";\r\n            } else if (err.includes(\"no multiformat readers\")) {\r\n              errorMessage =\r\n                \"No QR code found in the image. Please ensure it's a clear image of a Dukancard QR code.\";\r\n            } else if (err.includes(\"no multiformat readers\")) {\r\n              errorMessage =\r\n                \"No QR code found in the image. Please ensure it's a clear image of a Dukancard QR code.\";\r\n            } else if (err.includes(\"Image parse error\")) {\r\n              errorMessage =\r\n                \"Could not read the image file. Please ensure it's a valid image.\";\r\n            } else {\r\n              errorMessage = err; // Fallback to raw error message if it\\'s a string\r\n            }\r\n          } else if (err instanceof Error) {\r\n            errorMessage = err.message;\r\n          }\r\n          toast.error(errorMessage);\r\n          setIsProcessing(false);\r\n        } finally {\r\n          if (html5QrCode) {\r\n            try {\r\n              html5QrCode.clear();\r\n            } catch (e: unknown) {\r\n              console.error(\"Error clearing html5QrCode\", e);\r\n            }\r\n          }\r\n        }\r\n      } catch (_error) {\r\n        console.error(\"Outer catch: Failed to process image\", _error);\r\n        toast.error(\"Failed to process image\");\r\n        setIsProcessing(false);\r\n      }\r\n\r\n      // Reset file input\r\n      event.target.value = \"\";\r\n    },\r\n    [handleScanSuccess]\r\n  );\r\n\r\n  const handleClose = useCallback(() => {\r\n    if (!isProcessing) {\r\n      onClose();\r\n    }\r\n  }, [isProcessing, onClose]);\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={handleClose}>\r\n      <DialogContent className=\"sm:max-w-md w-full max-h-[90vh] overflow-hidden\">\r\n        <DialogHeader className=\"pb-4\">\r\n          <DialogTitle className=\"text-lg font-semibold\">\r\n            Scan QR Code\r\n          </DialogTitle>\r\n        </DialogHeader>\r\n\r\n        <div className=\"space-y-4\">\r\n          {/* Mode Toggle */}\r\n          <div className=\"flex rounded-lg bg-muted p-1\">\r\n            <button\r\n              onClick={() => setScanMode(\"camera\")}\r\n              disabled={isProcessing}\r\n              className={`flex-1 flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium rounded-md transition-colors ${\r\n                scanMode === \"camera\"\r\n                  ? \"bg-background text-foreground shadow-sm\"\r\n                  : \"text-muted-foreground hover:text-foreground\"\r\n              }`}\r\n            >\r\n              <Camera className=\"h-4 w-4\" />\r\n              Camera\r\n            </button>\r\n            <button\r\n              onClick={() => setScanMode(\"upload\")}\r\n              disabled={isProcessing}\r\n              className={`flex-1 flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium rounded-md transition-colors ${\r\n                scanMode === \"upload\"\r\n                  ? \"bg-background text-foreground shadow-sm\"\r\n                  : \"text-muted-foreground hover:text-foreground\"\r\n              }`}\r\n            >\r\n              <Upload className=\"h-4 w-4\" />\r\n              Upload\r\n            </button>\r\n          </div>\r\n\r\n          {/* Scanner Content */}\r\n          <div className=\"relative\">\r\n            {scanMode === \"camera\" ? (\r\n              <QRScanner\r\n                onScanSuccess={handleScanSuccess}\r\n                onScanError={handleScanError}\r\n                onClose={handleClose}\r\n                className=\"min-h-[300px]\"\r\n              />\r\n            ) : (\r\n              <div className=\"min-h-[300px] flex flex-col items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg p-8\">\r\n                <Upload className=\"h-12 w-12 text-muted-foreground mb-4\" />\r\n                <h3 className=\"text-lg font-semibold mb-2\">\r\n                  Upload QR Code Image\r\n                </h3>\r\n                <p className=\"text-muted-foreground text-center mb-4\">\r\n                  Select an image containing a Dukancard QR code\r\n                </p>\r\n                <label htmlFor=\"qr-upload\" className=\"cursor-pointer\">\r\n                  <Button asChild disabled={isProcessing}>\r\n                    <span>\r\n                      {isProcessing ? \"Processing...\" : \"Choose Image\"}\r\n                    </span>\r\n                  </Button>\r\n                  <input\r\n                    id=\"qr-upload\"\r\n                    type=\"file\"\r\n                    accept=\"image/*\"\r\n                    onChange={handleFileUpload}\r\n                    disabled={isProcessing}\r\n                    className=\"hidden\"\r\n                  />\r\n                </label>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Hidden div for Html5Qrcode file scanning */}\r\n          <div id=\"qr-file-scanner-region\" style={{ display: \"none\" }} />\r\n\r\n          {/* Instructions */}\r\n          <div className=\"text-center space-y-2\">\r\n            <p className=\"text-sm text-muted-foreground\">\r\n              {scanMode === \"camera\"\r\n                ? \"Position the QR code within the camera frame\"\r\n                : \"Upload an image containing a Dukancard QR code\"}\r\n            </p>\r\n            <p className=\"text-xs text-muted-foreground\">\r\n              Only Dukancard business QR codes are supported\r\n            </p>\r\n          </div>\r\n\r\n          {/* Processing Overlay */}\r\n          {isProcessing && (\r\n            <div className=\"absolute inset-0 bg-background/80 flex items-center justify-center z-50 rounded-lg\">\r\n              <div className=\"text-center\">\r\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\r\n                <p className=\"text-foreground font-medium\">\r\n                  Processing QR code...\r\n                </p>\r\n                <p className=\"text-sm text-muted-foreground\">Please wait</p>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n};\r\n\r\nexport default QRScannerModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAMA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAdA;;;;;;;;;;AAsBA,MAAM,iBAAgD,CAAC,EACrD,MAAM,EACN,OAAO,EACP,aAAa,EACd;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAE9D,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,CAAC;QACC,gBAAgB;QAEhB,0CAA0C;QAC1C,WAAW;YACT,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,cAAc;YACd;YACA,gBAAgB;QAClB,GAAG;IACL,GACA;QAAC;QAAe;KAAQ;IAG1B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,QAAQ,KAAK,CAAC,kBAAkB;QAChC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;IACd,GAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACjC,OAAO;QACL,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,MAAM;QAEX,4BAA4B;QAC5B,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,IAAI,aAAsC,8CAA8C;YACxF,IAAI;gBACF,cAAc,IAAI,yJAAA,CAAA,cAAW,CAAC;gBAC9B,MAAM,eAAe,MAAM,YAAY,QAAQ,CAAC;gBAChD,QAAQ,GAAG,CAAC,4BAA4B;gBAExC,uBAAuB;gBACvB,MAAM,aAAa,CAAA,GAAA,2HAAA,CAAA,wBAAqB,AAAD,EAAE;gBACzC,QAAQ,GAAG,CAAC,8BAA8B;gBAE1C,IAAI,CAAC,WAAW,OAAO,EAAE;oBACvB,MAAM,eAAe,WAAW,KAAK,IAAI;oBACzC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,gBAAgB;oBAChB;gBACF;gBAEA,kDAAkD;gBAClD,MAAM,eAAe,WAAW,YAAY;gBAC5C,kBAAkB;YACpB,EAAE,OAAO,KAAc;gBACrB,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,IAAI,eAAe;gBACnB,IAAI,OAAO,QAAQ,UAAU;oBAC3B,IAAI,IAAI,QAAQ,CAAC,sBAAsB;wBACrC,eACE;oBACJ,OAAO,IAAI,IAAI,QAAQ,CAAC,2BAA2B;wBACjD,eACE;oBACJ,OAAO,IAAI,IAAI,QAAQ,CAAC,2BAA2B;wBACjD,eACE;oBACJ,OAAO,IAAI,IAAI,QAAQ,CAAC,sBAAsB;wBAC5C,eACE;oBACJ,OAAO;wBACL,eAAe,KAAK,kDAAkD;oBACxE;gBACF,OAAO,IAAI,eAAe,OAAO;oBAC/B,eAAe,IAAI,OAAO;gBAC5B;gBACA,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,gBAAgB;YAClB,SAAU;gBACR,IAAI,aAAa;oBACf,IAAI;wBACF,YAAY,KAAK;oBACnB,EAAE,OAAO,GAAY;wBACnB,QAAQ,KAAK,CAAC,8BAA8B;oBAC9C;gBACF;YACF;QACF,EAAE,OAAO,QAAQ;YACf,QAAQ,KAAK,CAAC,wCAAwC;YACtD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,gBAAgB;QAClB;QAEA,mBAAmB;QACnB,MAAM,MAAM,CAAC,KAAK,GAAG;IACvB,GACA;QAAC;KAAkB;IAGrB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI,CAAC,cAAc;YACjB;QACF;IACF,GAAG;QAAC;QAAc;KAAQ;IAE1B,qBACE,8OAAC,2HAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,8OAAC,2HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,2HAAA,CAAA,eAAY;oBAAC,WAAU;8BACtB,cAAA,8OAAC,2HAAA,CAAA,cAAW;wBAAC,WAAU;kCAAwB;;;;;;;;;;;8BAKjD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,YAAY;oCAC3B,UAAU;oCACV,WAAW,CAAC,yGAAyG,EACnH,aAAa,WACT,4CACA,+CACJ;;sDAEF,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGhC,8OAAC;oCACC,SAAS,IAAM,YAAY;oCAC3B,UAAU;oCACV,WAAW,CAAC,yGAAyG,EACnH,aAAa,WACT,4CACA,+CACJ;;sDAEF,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;sCAMlC,8OAAC;4BAAI,WAAU;sCACZ,aAAa,yBACZ,8OAAC,8HAAA,CAAA,UAAS;gCACR,eAAe;gCACf,aAAa;gCACb,SAAS;gCACT,WAAU;;;;;qDAGZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAG3C,8OAAC;wCAAE,WAAU;kDAAyC;;;;;;kDAGtD,8OAAC;wCAAM,SAAQ;wCAAY,WAAU;;0DACnC,8OAAC,2HAAA,CAAA,SAAM;gDAAC,OAAO;gDAAC,UAAU;0DACxB,cAAA,8OAAC;8DACE,eAAe,kBAAkB;;;;;;;;;;;0DAGtC,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,QAAO;gDACP,UAAU;gDACV,UAAU;gDACV,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAQpB,8OAAC;4BAAI,IAAG;4BAAyB,OAAO;gCAAE,SAAS;4BAAO;;;;;;sCAG1D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CACV,aAAa,WACV,iDACA;;;;;;8CAEN,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;wBAM9C,8BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAE,WAAU;kDAA8B;;;;;;kDAG3C,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7D;uCAEe", "debugId": null}}, {"offset": {"line": 3391, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/BottomNav.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport Link from \"next/link\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\nimport { motion } from \"framer-motion\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { Home, Search, User, Store, QrCode } from \"lucide-react\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport QRScannerModal from \"@/components/qr/QRScannerModal\";\r\nimport { createClient } from \"@/utils/supabase/client\";\r\n\r\ninterface BottomNavItemProps {\r\n  href?: string;\r\n  icon: React.ReactNode;\r\n  label: string;\r\n  isActive: boolean;\r\n  isTablet?: boolean;\r\n  badge?: string;\r\n  disabled?: boolean;\r\n  onClick?: () => void;\r\n  isSpecial?: boolean;\r\n}\r\n\r\nconst BottomNavItem = ({\r\n  href,\r\n  icon,\r\n  label,\r\n  isActive,\r\n  isTablet = false,\r\n  badge,\r\n  disabled = false,\r\n  onClick,\r\n  isSpecial = false\r\n}: BottomNavItemProps) => {\r\n  const content = (\r\n    <>\r\n      <div className={cn(\r\n        \"relative mb-1 transition-all duration-200\",\r\n        isSpecial && \"bg-[var(--brand-gold)] rounded-full p-3 -mt-2 shadow-lg\"\r\n      )}>\r\n        <div className={cn(\r\n          isSpecial && \"text-white\"\r\n        )}>\r\n          {icon}\r\n        </div>\r\n        {badge && (\r\n          <Badge\r\n            variant=\"outline\"\r\n            className=\"absolute -top-2 -right-3 px-1 py-0 text-[7px] bg-[var(--brand-gold)] text-[var(--brand-gold-foreground)] border-[var(--brand-gold)]\"\r\n          >\r\n            {badge}\r\n          </Badge>\r\n        )}\r\n      </div>\r\n      <span className={cn(\r\n        \"transition-all\",\r\n        isTablet ? \"text-[9px]\" : \"text-[10px]\",\r\n        isSpecial && \"text-[var(--brand-gold)] font-medium\"\r\n      )}>{label}</span>\r\n    </>\r\n  );\r\n\r\n  const itemClassName = cn(\r\n    \"flex flex-col items-center justify-center flex-1 py-2 text-xs transition-colors cursor-pointer\",\r\n    isActive\r\n      ? \"text-[var(--brand-gold)]\"\r\n      : \"text-muted-foreground hover:text-[var(--brand-gold)]\",\r\n    disabled && \"opacity-70 pointer-events-none\",\r\n    isSpecial && \"transform hover:scale-105\"\r\n  );\r\n\r\n  if (disabled) {\r\n    return (\r\n      <div className={itemClassName}>\r\n        {content}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (onClick) {\r\n    return (\r\n      <button onClick={onClick} className={itemClassName}>\r\n        {content}\r\n      </button>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Link href={href!} className={itemClassName}>\r\n      {content}\r\n    </Link>\r\n  );\r\n};\r\n\r\nexport default function BottomNav() {\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const isMobile = useIsMobile();\r\n  const [isTablet, setIsTablet] = useState(false);\r\n  const [isQRScannerOpen, setIsQRScannerOpen] = useState(false);\r\n  const [_businessSlug, setBusinessSlug] = useState<string | null>(null);\r\n  const [userType, setUserType] = useState<'business' | 'customer' | null>(null);\r\n\r\n  useEffect(() => {\r\n    if (typeof window === \"undefined\") return;\r\n\r\n    // Check if device is a tablet (between 768px and 1024px)\r\n    const checkTablet = () => {\r\n      setIsTablet(window.innerWidth >= 768 && window.innerWidth < 1024);\r\n    };\r\n\r\n    // Initial check\r\n    checkTablet();\r\n\r\n    // Add event listener for resize\r\n    window.addEventListener('resize', checkTablet);\r\n\r\n    // Cleanup\r\n    return () => window.removeEventListener('resize', checkTablet);\r\n  }, []);\r\n\r\n  // Fetch user type and business slug for proper navigation\r\n  useEffect(() => {\r\n    const fetchUserInfo = async () => {\r\n      const supabase = createClient();\r\n      const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n      if (authError || !user) return;\r\n\r\n      try {\r\n        // Check if user has business profile using API\r\n        const response = await fetch('/api/business/me', {\r\n          method: 'GET',\r\n          headers: {\r\n            'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,\r\n          },\r\n        });\r\n\r\n        if (response.ok) {\r\n          const result = await response.json();\r\n          if (result.business) {\r\n            setUserType('business');\r\n            setBusinessSlug(result.business.business_slug);\r\n            return;\r\n          }\r\n        }\r\n\r\n        // Check for customer profile (still using direct call as customer API not implemented yet)\r\n        const { data: customerProfile } = await supabase\r\n          .from('customer_profiles')\r\n          .select('id')\r\n          .eq('user_id', user.id)\r\n          .single();\r\n\r\n        if (customerProfile) {\r\n          setUserType('customer');\r\n        }\r\n      } catch (error) {\r\n        console.error('Error fetching user info:', error);\r\n      }\r\n    };\r\n\r\n    fetchUserInfo();\r\n  }, []);\r\n\r\n  // QR Scanner handlers\r\n  const handleQRScanPress = () => {\r\n    setIsQRScannerOpen(true);\r\n  };\r\n\r\n  const handleQRScanSuccess = (businessSlug: string) => {\r\n    setIsQRScannerOpen(false);\r\n    // Navigate to business card page\r\n    router.push(`/${businessSlug}`);\r\n  };\r\n\r\n  const handleQRScannerClose = () => {\r\n    setIsQRScannerOpen(false);\r\n  };\r\n\r\n  // Don't render on desktop\r\n  if (!isMobile && !isTablet) {\r\n    return null;\r\n  }\r\n\r\n  // Determine navigation links based on current path and user context\r\n  let accountLink = \"/login\";\r\n  let accountIsActive = false;\r\n  let homeLink = \"/\";\r\n  let homeIsActive = false;\r\n\r\n  // If user is in business dashboard\r\n  if (pathname.startsWith(\"/dashboard/business\")) {\r\n    // Business users: Account button goes to their dashboard card page, Home goes to business feed\r\n    accountLink = \"/dashboard/business/card\";\r\n    accountIsActive = pathname === \"/dashboard/business/card\";\r\n    homeLink = \"/dashboard/business\";\r\n    homeIsActive = pathname === \"/dashboard/business\";\r\n  }\r\n  // If user is in customer dashboard\r\n  else if (pathname.startsWith(\"/dashboard/customer\")) {\r\n    // Customer users: Account button goes to profile, Home goes to customer feed\r\n    accountLink = \"/dashboard/customer/profile\";\r\n    accountIsActive = pathname.includes(\"/dashboard/customer/profile\");\r\n    homeLink = \"/dashboard/customer\";\r\n    homeIsActive = pathname === \"/dashboard/customer\";\r\n  }\r\n  // If user is in auth or onboarding flow\r\n  else if (pathname.startsWith(\"/login\") ||\r\n           pathname.startsWith(\"/choose-role\") || pathname.startsWith(\"/onboarding\")) {\r\n    accountLink = pathname; // Keep current page\r\n    accountIsActive = true;\r\n    homeLink = \"/\";\r\n    homeIsActive = pathname === \"/\";\r\n  }\r\n  // For public pages, determine based on user type\r\n  else {\r\n    if (userType === 'business') {\r\n      accountLink = \"/dashboard/business/card\";\r\n      accountIsActive = false;\r\n      homeLink = \"/dashboard/business\";\r\n      homeIsActive = false;\r\n    } else if (userType === 'customer') {\r\n      accountLink = \"/dashboard/customer/profile\";\r\n      accountIsActive = false;\r\n      homeLink = \"/dashboard/customer\";\r\n      homeIsActive = false;\r\n    } else {\r\n      accountLink = \"/login\";\r\n      accountIsActive = pathname === \"/login\";\r\n      homeLink = \"/\";\r\n      homeIsActive = pathname === \"/\";\r\n    }\r\n  }\r\n\r\n  // QR Scanner is always available (no authentication required for scanning)\r\n\r\n  // Unified navigation items\r\n  const navItems = [\r\n    {\r\n      key: \"home\",\r\n      href: homeLink,\r\n      icon: <Home size={20} />,\r\n      label: \"Home\",\r\n      isActive: homeIsActive\r\n    },\r\n    {\r\n      key: \"discover\",\r\n      href: \"/discover\",\r\n      icon: <Search size={20} />,\r\n      label: \"Discover\",\r\n      isActive: pathname === \"/discover\"\r\n    },\r\n    {\r\n      key: \"scan\",\r\n      icon: <QrCode size={20} />,\r\n      label: \"Scan\",\r\n      isActive: false,\r\n      onClick: handleQRScanPress,\r\n      isSpecial: true\r\n    },\r\n    {\r\n      key: \"dukan-ai\",\r\n      href: \"#\",\r\n      icon: <Store size={20} />,\r\n      label: \"Dukan AI\",\r\n      isActive: false,\r\n      badge: \"Soon\",\r\n      disabled: true\r\n    },\r\n    {\r\n      key: \"account\",\r\n      href: accountLink,\r\n      icon: <User size={20} />,\r\n      label: \"Account\",\r\n      isActive: accountIsActive\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <>\r\n      <motion.div\r\n        initial={{ y: 100 }}\r\n        animate={{ y: 0 }}\r\n        transition={{ duration: 0.3 }}\r\n        className={cn(\r\n          \"fixed bottom-0 left-0 right-0 z-50 flex items-center justify-around bg-background/95 backdrop-blur-lg border-t border-border/80 px-2\",\r\n          isTablet ? \"h-14\" : \"h-16\"\r\n        )}\r\n      >\r\n        {navItems.map((item) => (\r\n          <BottomNavItem\r\n            key={item.key}\r\n            href={item.href}\r\n            icon={item.icon}\r\n            label={item.label}\r\n            isActive={item.isActive}\r\n            isTablet={isTablet}\r\n            badge={item.badge}\r\n            disabled={item.disabled}\r\n            onClick={item.onClick}\r\n            isSpecial={item.isSpecial}\r\n          />\r\n        ))}\r\n      </motion.div>\r\n\r\n      {/* QR Scanner Modal */}\r\n      <QRScannerModal\r\n        isOpen={isQRScannerOpen}\r\n        onClose={handleQRScannerClose}\r\n        onScanSuccess={handleQRScanSuccess}\r\n      />\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;AA0BA,MAAM,gBAAgB,CAAC,EACrB,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,QAAQ,EACR,WAAW,KAAK,EAChB,KAAK,EACL,WAAW,KAAK,EAChB,OAAO,EACP,YAAY,KAAK,EACE;IACnB,MAAM,wBACJ;;0BACE,8OAAC;gBAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACf,6CACA,aAAa;;kCAEb,8OAAC;wBAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACf,aAAa;kCAEZ;;;;;;oBAEF,uBACC,8OAAC,0HAAA,CAAA,QAAK;wBACJ,SAAQ;wBACR,WAAU;kCAET;;;;;;;;;;;;0BAIP,8OAAC;gBAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAChB,kBACA,WAAW,eAAe,eAC1B,aAAa;0BACX;;;;;;;;IAIR,MAAM,gBAAgB,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACrB,kGACA,WACI,6BACA,wDACJ,YAAY,kCACZ,aAAa;IAGf,IAAI,UAAU;QACZ,qBACE,8OAAC;YAAI,WAAW;sBACb;;;;;;IAGP;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAO,SAAS;YAAS,WAAW;sBAClC;;;;;;IAGP;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAM;QAAO,WAAW;kBAC3B;;;;;;AAGP;AAEe,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACjE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkC;IAEzE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAmC;;QAEnC,yDAAyD;QACzD,MAAM;IAYR,GAAG,EAAE;IAEL,0DAA0D;IAC1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,MAAM,WAAW,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;YAC5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YAExE,IAAI,aAAa,CAAC,MAAM;YAExB,IAAI;gBACF,+CAA+C;gBAC/C,MAAM,WAAW,MAAM,MAAM,oBAAoB;oBAC/C,QAAQ;oBACR,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,CAAC,MAAM,SAAS,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,cAAc;oBAC5F;gBACF;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,SAAS,MAAM,SAAS,IAAI;oBAClC,IAAI,OAAO,QAAQ,EAAE;wBACnB,YAAY;wBACZ,gBAAgB,OAAO,QAAQ,CAAC,aAAa;wBAC7C;oBACF;gBACF;gBAEA,2FAA2F;gBAC3F,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,MAAM,SACrC,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,MAAM;gBAET,IAAI,iBAAiB;oBACnB,YAAY;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;YAC7C;QACF;QAEA;IACF,GAAG,EAAE;IAEL,sBAAsB;IACtB,MAAM,oBAAoB;QACxB,mBAAmB;IACrB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,mBAAmB;QACnB,iCAAiC;QACjC,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,cAAc;IAChC;IAEA,MAAM,uBAAuB;QAC3B,mBAAmB;IACrB;IAEA,0BAA0B;IAC1B,IAAI,CAAC,YAAY,CAAC,UAAU;QAC1B,OAAO;IACT;IAEA,oEAAoE;IACpE,IAAI,cAAc;IAClB,IAAI,kBAAkB;IACtB,IAAI,WAAW;IACf,IAAI,eAAe;IAEnB,mCAAmC;IACnC,IAAI,SAAS,UAAU,CAAC,wBAAwB;QAC9C,+FAA+F;QAC/F,cAAc;QACd,kBAAkB,aAAa;QAC/B,WAAW;QACX,eAAe,aAAa;IAC9B,OAEK,IAAI,SAAS,UAAU,CAAC,wBAAwB;QACnD,6EAA6E;QAC7E,cAAc;QACd,kBAAkB,SAAS,QAAQ,CAAC;QACpC,WAAW;QACX,eAAe,aAAa;IAC9B,OAEK,IAAI,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,mBAAmB,SAAS,UAAU,CAAC,gBAAgB;QAClF,cAAc,UAAU,oBAAoB;QAC5C,kBAAkB;QAClB,WAAW;QACX,eAAe,aAAa;IAC9B,OAEK;QACH,IAAI,aAAa,YAAY;YAC3B,cAAc;YACd,kBAAkB;YAClB,WAAW;YACX,eAAe;QACjB,OAAO,IAAI,aAAa,YAAY;YAClC,cAAc;YACd,kBAAkB;YAClB,WAAW;YACX,eAAe;QACjB,OAAO;YACL,cAAc;YACd,kBAAkB,aAAa;YAC/B,WAAW;YACX,eAAe,aAAa;QAC9B;IACF;IAEA,2EAA2E;IAE3E,2BAA2B;IAC3B,MAAM,WAAW;QACf;YACE,KAAK;YACL,MAAM;YACN,oBAAM,8OAAC,mMAAA,CAAA,OAAI;gBAAC,MAAM;;;;;;YAClB,OAAO;YACP,UAAU;QACZ;QACA;YACE,KAAK;YACL,MAAM;YACN,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,MAAM;;;;;;YACpB,OAAO;YACP,UAAU,aAAa;QACzB;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,0MAAA,CAAA,SAAM;gBAAC,MAAM;;;;;;YACpB,OAAO;YACP,UAAU;YACV,SAAS;YACT,WAAW;QACb;QACA;YACE,KAAK;YACL,MAAM;YACN,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,MAAM;;;;;;YACnB,OAAO;YACP,UAAU;YACV,OAAO;YACP,UAAU;QACZ;QACA;YACE,KAAK;YACL,MAAM;YACN,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,MAAM;;;;;;YAClB,OAAO;YACP,UAAU;QACZ;KACD;IAED,qBACE;;0BACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,GAAG;gBAAI;gBAClB,SAAS;oBAAE,GAAG;gBAAE;gBAChB,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wIACA,WAAW,SAAS;0BAGrB,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC;wBAEC,MAAM,KAAK,IAAI;wBACf,MAAM,KAAK,IAAI;wBACf,OAAO,KAAK,KAAK;wBACjB,UAAU,KAAK,QAAQ;wBACvB,UAAU;wBACV,OAAO,KAAK,KAAK;wBACjB,UAAU,KAAK,QAAQ;wBACvB,SAAS,KAAK,OAAO;wBACrB,WAAW,KAAK,SAAS;uBATpB,KAAK,GAAG;;;;;;;;;;0BAenB,8OAAC,mIAAA,CAAA,UAAc;gBACb,QAAQ;gBACR,SAAS;gBACT,eAAe;;;;;;;;AAIvB", "debugId": null}}, {"offset": {"line": 3719, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/MobileFooter.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\n\r\nconst MobileFooter: React.FC = () => {\r\n  const isMobile = useIsMobile();\r\n\r\n  // Only show on mobile\r\n  if (!isMobile) {\r\n    return null;\r\n  }\r\n\r\n  // We're now showing the main Footer on mobile, so this component is no longer needed\r\n  return null;\r\n};\r\n\r\nexport default MobileFooter;\r\n"], "names": [], "mappings": ";;;AAGA;AAHA;;AAKA,MAAM,eAAyB;IAC7B,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;IAE3B,sBAAsB;IACtB,IAAI,CAAC,UAAU;QACb,OAAO;IACT;IAEA,qFAAqF;IACrF,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 3741, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/AdvertiseButton.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { Megaphone } from \"lucide-react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { siteConfig } from \"@/lib/site-config\";\r\nimport { usePathname } from \"next/navigation\";\r\n\r\n// CSS for vertical text\r\nconst verticalTextStyle = {\r\n  writingMode: 'vertical-rl' as const,\r\n  textOrientation: 'mixed' as const,\r\n  transform: 'rotate(180deg)',\r\n  letterSpacing: '0.05em',\r\n  fontSize: '0.8rem',\r\n  fontWeight: 600,\r\n};\r\n\r\nexport default function AdvertiseButton() {\r\n  const [isHovered, setIsHovered] = useState(false);\r\n  const [isClient, setIsClient] = useState(false);\r\n  const pathname = usePathname();\r\n\r\n  // Use useEffect to detect client-side rendering\r\n  useEffect(() => {\r\n    setIsClient(true);\r\n  }, []);\r\n\r\n  // Don't show on dashboard pages or advertise page\r\n  const isDashboardPage = pathname?.includes(\"/dashboard\");\r\n  const isAdvertisePage = pathname === \"/advertise\";\r\n\r\n  if (!isClient || isDashboardPage || isAdvertisePage) {\r\n    return null; // Don't render during SSR, on dashboard pages, or on advertise page\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {/* Desktop and tablet version - side button */}\r\n      <div className=\"fixed right-0 top-1/2 -translate-y-1/2 z-40 hidden sm:block\">\r\n        <div className=\"relative group\">\r\n          {/* Button glow effect */}\r\n          <motion.div\r\n            className=\"absolute -inset-0.5 bg-gradient-to-r from-[var(--brand-gold)]/30 to-[var(--brand-gold)]/50 rounded-l-lg blur-md\"\r\n            animate={{ opacity: isHovered ? 0.8 : 0.5 }}\r\n            transition={{ duration: 0.3 }}\r\n          />\r\n\r\n          <Link href={siteConfig.advertising.page}>\r\n            <motion.div\r\n              className=\"relative\"\r\n              onMouseEnter={() => setIsHovered(true)}\r\n              onMouseLeave={() => setIsHovered(false)}\r\n              whileHover={{ scale: 1.03 }}\r\n              whileTap={{ scale: 0.98 }}\r\n            >\r\n              <div\r\n                className=\"cursor-pointer bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 px-2 py-6 rounded-l-lg font-medium text-xs relative overflow-hidden shadow-lg flex flex-col items-center justify-center w-10 h-32\"\r\n              >\r\n                {/* Button content with animated icon */}\r\n                <div className=\"flex flex-col items-center justify-center gap-2\">\r\n                  <Megaphone className=\"w-4 h-4\" />\r\n                  <span style={verticalTextStyle}>Advertise</span>\r\n                </div>\r\n\r\n                {/* Shimmer effect */}\r\n                <motion.div\r\n                  className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none\"\r\n                  initial={{ x: \"-100%\" }}\r\n                  animate={{ x: \"100%\" }}\r\n                  transition={{\r\n                    duration: 2,\r\n                    repeat: Infinity,\r\n                    ease: \"linear\",\r\n                    repeatDelay: 1\r\n                  }}\r\n                />\r\n              </div>\r\n            </motion.div>\r\n          </Link>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Mobile version - floating button */}\r\n      <div className=\"fixed left-4 bottom-20 z-40 sm:hidden\">\r\n        <div className=\"relative group\">\r\n          {/* Button glow effect */}\r\n          <motion.div\r\n            className=\"absolute -inset-0.5 bg-gradient-to-r from-[var(--brand-gold)]/30 to-[var(--brand-gold)]/50 rounded-full blur-md\"\r\n            animate={{ opacity: 0.6 }}\r\n            transition={{ duration: 0.3 }}\r\n          />\r\n\r\n          <Link href={siteConfig.advertising.page}>\r\n            <motion.div\r\n              className=\"relative\"\r\n              whileTap={{ scale: 0.95 }}\r\n            >\r\n              <div\r\n                className=\"cursor-pointer bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 p-2 rounded-full font-medium text-xs relative overflow-hidden shadow-md flex items-center justify-center w-10 h-10\"\r\n              >\r\n                <Megaphone className=\"w-4 h-4\" />\r\n\r\n                {/* Shimmer effect */}\r\n                <motion.div\r\n                  className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none\"\r\n                  initial={{ x: \"-100%\" }}\r\n                  animate={{ x: \"100%\" }}\r\n                  transition={{\r\n                    duration: 2,\r\n                    repeat: Infinity,\r\n                    ease: \"linear\",\r\n                    repeatDelay: 1\r\n                  }}\r\n                />\r\n              </div>\r\n            </motion.div>\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASA,wBAAwB;AACxB,MAAM,oBAAoB;IACxB,aAAa;IACb,iBAAiB;IACjB,WAAW;IACX,eAAe;IACf,UAAU;IACV,YAAY;AACd;AAEe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,kDAAkD;IAClD,MAAM,kBAAkB,UAAU,SAAS;IAC3C,MAAM,kBAAkB,aAAa;IAErC,IAAI,CAAC,YAAY,mBAAmB,iBAAiB;QACnD,OAAO,MAAM,oEAAoE;IACnF;IAEA,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS,YAAY,MAAM;4BAAI;4BAC1C,YAAY;gCAAE,UAAU;4BAAI;;;;;;sCAG9B,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAM,qHAAA,CAAA,aAAU,CAAC,WAAW,CAAC,IAAI;sCACrC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,cAAc,IAAM,aAAa;gCACjC,cAAc,IAAM,aAAa;gCACjC,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;0CAExB,cAAA,8OAAC;oCACC,WAAU;;sDAGV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,4MAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,8OAAC;oDAAK,OAAO;8DAAmB;;;;;;;;;;;;sDAIlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,GAAG;4CAAQ;4CACtB,SAAS;gDAAE,GAAG;4CAAO;4CACrB,YAAY;gDACV,UAAU;gDACV,QAAQ;gDACR,MAAM;gDACN,aAAa;4CACf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASZ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAI;4BACxB,YAAY;gCAAE,UAAU;4BAAI;;;;;;sCAG9B,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAM,qHAAA,CAAA,aAAU,CAAC,WAAW,CAAC,IAAI;sCACrC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,UAAU;oCAAE,OAAO;gCAAK;0CAExB,cAAA,8OAAC;oCACC,WAAU;;sDAEV,8OAAC,4MAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDAGrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,GAAG;4CAAQ;4CACtB,SAAS;gDAAE,GAAG;4CAAO;4CACrB,YAAY;gDACV,UAAU;gDACV,QAAQ;gDACR,MAAM;gDACN,aAAa;4CACf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlB", "debugId": null}}, {"offset": {"line": 3977, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction TooltipProvider({\r\n  delayDuration = 0,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\r\n  return (\r\n    <TooltipPrimitive.Provider\r\n      data-slot=\"tooltip-provider\"\r\n      delayDuration={delayDuration}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction Tooltip({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\r\n  return (\r\n    <TooltipProvider>\r\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\r\n    </TooltipProvider>\r\n  )\r\n}\r\n\r\nfunction TooltipTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\r\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />\r\n}\r\n\r\nfunction TooltipContent({\r\n  className,\r\n  sideOffset = 0,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\r\n  return (\r\n    <TooltipPrimitive.Portal>\r\n      <TooltipPrimitive.Content\r\n        data-slot=\"tooltip-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\r\n      </TooltipPrimitive.Content>\r\n    </TooltipPrimitive.Portal>\r\n  )\r\n}\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,8OAAC,mKAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,8OAAC;kBACC,cAAA,8OAAC,mKAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,8OAAC,mKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 4062, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/FloatingAIButton.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { Store } from \"lucide-react\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\n\r\nexport default function FloatingAIButton() {\r\n  const [isDesktop, setIsDesktop] = useState(false);\r\n\r\n  // Check if we're on desktop (>= 1024px)\r\n  useEffect(() => {\r\n    const checkDesktop = () => {\r\n      setIsDesktop(window.innerWidth >= 1024);\r\n    };\r\n\r\n    // Initial check\r\n    checkDesktop();\r\n\r\n    // Add event listener for resize\r\n    window.addEventListener('resize', checkDesktop);\r\n\r\n    // Cleanup\r\n    return () => window.removeEventListener('resize', checkDesktop);\r\n  }, []);\r\n\r\n  // Only render on desktop\r\n  if (!isDesktop) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      <motion.div\r\n        className=\"fixed bottom-24 right-6 z-50\"\r\n        initial={{ opacity: 0, scale: 0.5 }}\r\n        animate={{ opacity: 1, scale: 1 }}\r\n        exit={{ opacity: 0, scale: 0.5 }}\r\n        transition={{ duration: 0.3 }}\r\n      >\r\n        <TooltipProvider>\r\n          <Tooltip>\r\n            <TooltipTrigger asChild>\r\n              <div className=\"relative group\">\r\n                <motion.button\r\n                  className=\"relative flex items-center justify-center p-3 rounded-full bg-[var(--brand-gold)]/80 text-[var(--brand-gold-foreground)] shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-[var(--brand-gold)] focus:ring-offset-2 dark:focus:ring-offset-black cursor-not-allowed opacity-90\"\r\n                  whileHover={{ scale: 1.05 }}\r\n                  disabled={true}\r\n                >\r\n                  {/* Small \"soon\" indicator dot */}\r\n                  <span className=\"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-white dark:border-black\"></span>\r\n                  {/* Button glow effect */}\r\n                  <div className=\"absolute inset-0 rounded-full bg-[var(--brand-gold)]/40 blur-md -z-10\"></div>\r\n\r\n                  {/* Animated sparkles */}\r\n                  <motion.div\r\n                    animate={{\r\n                      rotate: [0, 15, -15, 0],\r\n                      scale: [1, 1.1, 1]\r\n                    }}\r\n                    transition={{\r\n                      duration: 2,\r\n                      repeat: Infinity,\r\n                      repeatType: \"loop\",\r\n                      ease: \"easeInOut\"\r\n                    }}\r\n                  >\r\n                    <Store className=\"h-6 w-6\" />\r\n                  </motion.div>\r\n                </motion.button>\r\n\r\n                {/* No floating label, using tooltip instead */}\r\n              </div>\r\n            </TooltipTrigger>\r\n            <TooltipContent side=\"right\" className=\"bg-black/90 dark:bg-white/90 text-white dark:text-black border-none px-3 py-2 font-medium\">\r\n              <p className=\"flex items-center gap-1.5\">\r\n                <Store className=\"h-4 w-4\" />\r\n                <span>Dukan AI - Coming Soon!</span>\r\n              </p>\r\n            </TooltipContent>\r\n          </Tooltip>\r\n        </TooltipProvider>\r\n      </motion.div>\r\n    </AnimatePresence>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AALA;;;;;;AAYe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,aAAa,OAAO,UAAU,IAAI;QACpC;QAEA,gBAAgB;QAChB;QAEA,gCAAgC;QAChC,OAAO,gBAAgB,CAAC,UAAU;QAElC,UAAU;QACV,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,yBAAyB;IACzB,IAAI,CAAC,WAAW;QACd,OAAO;IACT;IAEA,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAClC,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,MAAM;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAC/B,YAAY;gBAAE,UAAU;YAAI;sBAE5B,cAAA,8OAAC,4HAAA,CAAA,kBAAe;0BACd,cAAA,8OAAC,4HAAA,CAAA,UAAO;;sCACN,8OAAC,4HAAA,CAAA,iBAAc;4BAAC,OAAO;sCACrB,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;;sDAGV,8OAAC;4CAAK,WAAU;;;;;;sDAEhB,8OAAC;4CAAI,WAAU;;;;;;sDAGf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDACP,QAAQ;oDAAC;oDAAG;oDAAI,CAAC;oDAAI;iDAAE;gDACvB,OAAO;oDAAC;oDAAG;oDAAK;iDAAE;4CACpB;4CACA,YAAY;gDACV,UAAU;gDACV,QAAQ;gDACR,YAAY;gDACZ,MAAM;4CACR;sDAEA,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOzB,8OAAC,4HAAA,CAAA,iBAAc;4BAAC,MAAK;4BAAQ,WAAU;sCACrC,cAAA,8OAAC;gCAAE,WAAU;;kDACX,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB", "debugId": null}}, {"offset": {"line": 4248, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/scroll-to-top-button.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { ChevronUp } from \"lucide-react\";\r\nimport { usePathname } from \"next/navigation\";\r\n\r\ninterface ScrollToTopButtonProps {\r\n  excludePaths?: string[];\r\n}\r\n\r\nexport default function ScrollToTopButton({ excludePaths = [] }: ScrollToTopButtonProps) {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const pathname = usePathname();\r\n\r\n  // Check if current path should be excluded\r\n  const shouldExclude = excludePaths.some(path => pathname.startsWith(path));\r\n\r\n  // Show button when page is scrolled down\r\n  useEffect(() => {\r\n    // Don't run effect if path is excluded\r\n    if (shouldExclude) {\r\n      return;\r\n    }\r\n    const toggleVisibility = () => {\r\n      if (window.scrollY > 500) {\r\n        setIsVisible(true);\r\n      } else {\r\n        setIsVisible(false);\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"scroll\", toggleVisibility);\r\n    return () => window.removeEventListener(\"scroll\", toggleVisibility);\r\n  }, [shouldExclude]);\r\n\r\n  const scrollToTop = () => {\r\n    window.scrollTo({\r\n      top: 0,\r\n      behavior: \"smooth\",\r\n    });\r\n  };\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      {isVisible && (\r\n        <motion.button\r\n          className=\"fixed bottom-[70px] md:bottom-[60px] lg:bottom-8 right-6 z-50 p-3 rounded-full bg-[var(--brand-gold)] text-[var(--brand-gold-foreground)] shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-[var(--brand-gold)] focus:ring-offset-2 dark:focus:ring-offset-black cursor-pointer\"\r\n          onClick={scrollToTop}\r\n          initial={{ opacity: 0, scale: 0.5, y: 20 }}\r\n          animate={{ opacity: 1, scale: 1, y: 0 }}\r\n          exit={{ opacity: 0, scale: 0.5, y: 20 }}\r\n          transition={{ duration: 0.3 }}\r\n          whileHover={{ scale: 1.1 }}\r\n          whileTap={{ scale: 0.9 }}\r\n        >\r\n          {/* Button glow effect */}\r\n          <div className=\"absolute inset-0 rounded-full bg-[var(--brand-gold)]/50 blur-md -z-10\"></div>\r\n\r\n          {/* Animated arrow */}\r\n          <motion.div\r\n            animate={{ y: [0, -3, 0] }}\r\n            transition={{\r\n              duration: 1.5,\r\n              repeat: Infinity,\r\n              repeatType: \"loop\",\r\n              ease: \"easeInOut\"\r\n            }}\r\n          >\r\n            <ChevronUp className=\"h-6 w-6\" />\r\n          </motion.div>\r\n        </motion.button>\r\n      )}\r\n    </AnimatePresence>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AALA;;;;;;AAWe,SAAS,kBAAkB,EAAE,eAAe,EAAE,EAA0B;IACrF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,2CAA2C;IAC3C,MAAM,gBAAgB,aAAa,IAAI,CAAC,CAAA,OAAQ,SAAS,UAAU,CAAC;IAEpE,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAuC;QACvC,IAAI,eAAe;YACjB;QACF;QACA,MAAM,mBAAmB;YACvB,IAAI,OAAO,OAAO,GAAG,KAAK;gBACxB,aAAa;YACf,OAAO;gBACL,aAAa;YACf;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG;QAAC;KAAc;IAElB,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;YACZ,WAAU;YACV,SAAS;YACT,SAAS;gBAAE,SAAS;gBAAG,OAAO;gBAAK,GAAG;YAAG;YACzC,SAAS;gBAAE,SAAS;gBAAG,OAAO;gBAAG,GAAG;YAAE;YACtC,MAAM;gBAAE,SAAS;gBAAG,OAAO;gBAAK,GAAG;YAAG;YACtC,YAAY;gBAAE,UAAU;YAAI;YAC5B,YAAY;gBAAE,OAAO;YAAI;YACzB,UAAU;gBAAE,OAAO;YAAI;;8BAGvB,8OAAC;oBAAI,WAAU;;;;;;8BAGf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;4BAAC;4BAAG,CAAC;4BAAG;yBAAE;oBAAC;oBACzB,YAAY;wBACV,UAAU;wBACV,QAAQ;wBACR,YAAY;wBACZ,MAAM;oBACR;8BAEA,cAAA,8OAAC,gNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMjC", "debugId": null}}]}