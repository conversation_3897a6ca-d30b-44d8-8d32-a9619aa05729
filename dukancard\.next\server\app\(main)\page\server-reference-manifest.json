{"node": {"00a8cc48c10fab6f16e252026656352c7c5fad9033": {"workers": {"app/(main)/page": {"moduleId": "[project]/.next-internal/server/app/(main)/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/actions/getHomepageBusinessCard.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(main)/page": "rsc"}}, "00fb28f2104d350da9091aac6cd03550c4bf0d5e04": {"workers": {"app/(main)/page": {"moduleId": "[project]/.next-internal/server/app/(main)/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/actions/getHomepageBusinessCard.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(main)/page": "rsc"}}, "408078c315b7b0094b642ab831ed7eb03d4fd98823": {"workers": {"app/(main)/page": {"moduleId": "[project]/.next-internal/server/app/(main)/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/actions/getHomepageBusinessCard.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(main)/page": "action-browser"}}, "40c76fcf00cbeff9df8975c5b6c7b4d802b3ba9578": {"workers": {"app/(main)/page": {"moduleId": "[project]/.next-internal/server/app/(main)/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/actions/getHomepageBusinessCard.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(main)/page": "action-browser"}}}, "edge": {}}