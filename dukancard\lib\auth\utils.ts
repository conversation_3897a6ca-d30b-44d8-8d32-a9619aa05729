import { NextRequest } from 'next/server';
import { headers } from 'next/headers';
import { createClient } from '@/utils/supabase/server';

/**
 * Extracts the authenticated user ID from JWT middleware headers in server components
 * @returns The user ID if authenticated, null otherwise
 */
export async function getUserFromHeaders(): Promise<{ id: string; roles: string[] } | null> {
  try {
    const headersList = headers();
    const userId = headersList.get('x-user-id');
    const userRoles = headersList.get('x-user-roles');

    if (!userId) {
      return null;
    }

    return {
      id: userId,
      roles: userRoles ? JSON.parse(userRoles) : []
    };
  } catch (error) {
    console.error('Error getting user from headers:', error);
    return null;
  }
}

/**
 * Extracts the authenticated user ID from a Next.js API request using JWT validation
 * @param req - The Next.js request object
 * @returns The user ID if authenticated, null otherwise
 */
export async function getUserIdFromRequest(req: NextRequest): Promise<string | null> {
  try {
    // Try to get from middleware headers first (JWT-based)
    const userId = req.headers.get('x-user-id');
    if (userId) {
      return userId;
    }

    // Fallback to Supabase auth (legacy, will be removed)
    const supabase = await createClient();
    const { data: { user }, error } = await supabase.auth.getUser();

    if (error || !user) {
      return null;
    }

    return user.id;
  } catch (error) {
    console.error('Error getting user from request:', error);
    return null;
  }
}