"use server";

import { createClient } from "@/utils/supabase/server";
import { ProductServiceData } from "@/app/(dashboard)/dashboard/business/products/actions";
import { ProductVariant } from "@/types/variants";
import { ProductWithVariants } from "@/types/products";
// import { ProductVariantsRow } from "@/types/database/product-variants";

// Type for raw variant data from database
interface RawVariantData {
  id: string;
  product_id: string;
  variant_name: string;
  variant_values: unknown; // Use unknown to handle Json type from Supabase
  base_price: number | null;
  discounted_price: number | null;
  is_available: boolean;
  images: string[] | null;
  featured_image_index: number | null;
  created_at: string;
  updated_at: string;
}

// Type for item with business profiles relation


/**
 * Fetch a single product by ID
 */
export async function getProductById(
  productId: string,
  businessId: string
): Promise<{
  data?: ProductServiceData;
  error?: string;
}> {
  if (!productId || !businessId) {
    return { error: "Product ID and Business ID are required." };
  }

  try {
    // Use regular client - products_services has public read access for online businesses
    const supabase = await createClient();

    // Fetch the product
    const { data, error } = await supabase
      .from("products_services")
      .select(
        `
        id,
        business_id,
        name,
        description,
        base_price,
        discounted_price,
        product_type,
        is_available,
        image_url,
        images,
        featured_image_index,
        created_at,
        updated_at,
        slug
      `
      )
      .eq("id", productId)
      .eq("business_id", businessId)
      .eq("is_available", true)
      .single();

    if (error) {
      console.error("Error fetching product:", error);
      return { error: "Failed to fetch product details" };
    }

    if (!data) {
      return { error: "Product not found" };
    }

    // Transform the data to match ProductServiceData type
    const transformedData: ProductServiceData = {
      ...data,
      base_price: data.base_price || 0,
      product_type: (data.product_type as "physical" | "service") || "physical",
      created_at: data.created_at,
      updated_at: data.updated_at,
      slug: data.slug || undefined,
      description: data.description || undefined,
    };

    return { data: transformedData };
  } catch (error) {
    console.error("Unexpected error in getProductById:", error);
    return { error: "An unexpected error occurred" };
  }
}

/**
 * Fetch a single product by slug
 */
export async function getProductBySlug(
  slug: string,
  businessId: string
): Promise<{
  data?: ProductServiceData;
  error?: string;
}> {
  if (!slug || !businessId) {
    return { error: "Product slug and Business ID are required." };
  }

  try {
    // Use regular client - products_services has public read access for online businesses
    const supabase = await createClient();

    // Fetch the product
    const { data, error } = await supabase
      .from("products_services")
      .select(
        `
        id,
        business_id,
        name,
        description,
        base_price,
        discounted_price,
        product_type,
        is_available,
        image_url,
        images,
        featured_image_index,
        created_at,
        updated_at,
        slug
      `
      )
      .eq("slug", slug)
      .eq("business_id", businessId)
      .eq("is_available", true)
      .single();

    // Handle PGRST116 error (no rows returned) silently - this is an expected case
    if (error && error.code === "PGRST116") {
      return { data: undefined };
    }

    // Log other unexpected errors
    if (error) {
      console.error("Unexpected error fetching product by slug:", error);
      return { error: "Failed to fetch product details" };
    }

    if (!data) {
      return { data: undefined };
    }

    // Transform the data to match ProductServiceData type
    const transformedData: ProductServiceData = {
      ...data,
      base_price: data.base_price || 0,
      product_type: (data.product_type as "physical" | "service") || "physical",
      created_at: data.created_at,
      updated_at: data.updated_at,
      slug: data.slug || undefined,
      description: data.description || undefined,
    };

    return { data: transformedData };
  } catch (error) {
    console.error("Unexpected error in getProductBySlug:", error);
    return { error: "An unexpected error occurred" };
  }
}

/**
 * Fetch a single product with its variants by slug
 */
export async function getProductWithVariantsBySlug(
  slug: string,
  businessId: string
): Promise<{
  data?: ProductWithVariants;
  error?: string;
}> {
  if (!slug || !businessId) {
    return { error: "Product slug and Business ID are required." };
  }

  try {
    // Use regular client - products_services has public read access for online businesses
    const supabase = await createClient();

    // Fetch the product
    const { data: productData, error: productError } = await supabase
      .from("products_services")
      .select(
        `
        id,
        business_id,
        name,
        description,
        base_price,
        discounted_price,
        product_type,
        is_available,
        image_url,
        images,
        featured_image_index,
        created_at,
        updated_at,
        slug,
        dimensions_cm,
        is_fragile,
        is_high_value,
        is_oversized,
        weight_kg
      `
      )
      .eq("slug", slug)
      .eq("business_id", businessId)
      .eq("is_available", true)
      .single();

    // Handle PGRST116 error (no rows returned) silently - this is an expected case
    if (productError && productError.code === "PGRST116") {
      return { data: undefined };
    }

    // Log other unexpected errors
    if (productError) {
      console.error("Unexpected error fetching product by slug:", productError);
      return { error: "Failed to fetch product details" };
    }

    if (!productData) {
      return { data: undefined };
    }

    // Fetch variants for this product
    const { data: variantsData, error: variantsError } = await supabase
      .from("product_variants")
      .select(
        `
        id,
        product_id,
        variant_name,
        variant_values,
        base_price,
        discounted_price,
        is_available,
        images,
        featured_image_index,
        created_at,
        updated_at
      `
      )
      .eq("product_id", productData.id)
      .order("created_at", { ascending: true });

    if (variantsError) {
      console.error("Error fetching product variants:", variantsError);
      // Continue without variants rather than failing completely
    }

    // Transform variants data
    const variants: ProductVariant[] = (variantsData || []).map(
      (variant: RawVariantData) => ({
        id: variant.id,
        product_id: variant.product_id,
        variant_name: variant.variant_name,
        variant_values:
          typeof variant.variant_values === "string"
            ? JSON.parse(variant.variant_values)
            : variant.variant_values,
        base_price: variant.base_price,
        discounted_price: variant.discounted_price,
        is_available: variant.is_available,
        images: variant.images,
        featured_image_index: variant.featured_image_index,
        created_at: variant.created_at,
        updated_at: variant.updated_at,
      })
    );

    // Create the product with variants
    const productWithVariants: ProductWithVariants = {
      ...productData,
      variant_count: variants.length,
      has_variants: variants.length > 0,
      available_variant_count: variants.filter((v) => v.is_available).length,
      variants,
    };

    return { data: productWithVariants };
  } catch (error) {
    console.error("Unexpected error in getProductWithVariantsBySlug:", error);
    return { error: "An unexpected error occurred" };
  }
}

/**
 * Get more products from the same business
 */
export async function getMoreProductsFromBusiness(
  businessId: string,
  currentProductId: string,
  limit: number = 12
): Promise<{
  data?: ProductServiceData[];
  error?: string;
}> {
  if (!businessId || !currentProductId) {
    return { error: "Business ID and current product ID are required." };
  }

  try {
    // Use regular client - products_services has public read access for online businesses
    const supabase = await createClient();

    // Fetch more products from the same business, excluding the current product
    const { data, error } = await supabase
      .from("products_services")
      .select(
        `
        id,
        business_id,
        name,
        description,
        base_price,
        discounted_price,
        product_type,
        is_available,
        image_url,
        images,
        featured_image_index,
        created_at,
        updated_at,
        slug
      `
      )
      .eq("business_id", businessId)
      .eq("is_available", true)
      .neq("id", currentProductId)
      .order("created_at", { ascending: false })
      .limit(limit);

    if (error) {
      console.error("Error fetching more products:", error);
      return { error: "Failed to fetch more products" };
    }

    // Transform the data to match ProductServiceData type
    const transformedData: ProductServiceData[] = (data || []).map(item => ({
      ...item,
      base_price: item.base_price || 0,
      product_type: (item.product_type as "physical" | "service") || "physical",
      created_at: item.created_at,
      updated_at: item.updated_at,
      slug: item.slug || undefined,
      description: item.description || undefined,
    }));

    return { data: transformedData };
  } catch (error) {
    console.error("Unexpected error in getMoreProductsFromBusiness:", error);
    return { error: "An unexpected error occurred" };
  }
}

type FetchedProduct = {
  id: string;
  business_id: string;
  name: string | null;
  description: string | null;
  base_price: number | null;
  discounted_price: number | null;
  product_type: string | null;
  is_available: boolean | null;
  image_url: string | null;
  images: string[] | null;
  featured_image_index: number | null;
  created_at: string | null;
  updated_at: string | null;
  slug: string | null;
  business_profiles:
    | { business_slug: string | null }
    | { business_slug: string | null }[]
    | null;
};

/**
 * Get products from other businesses
 */
export async function getProductsFromOtherBusinesses(
  businessId: string,
  limit: number = 12
): Promise<{
  data?: Array<ProductServiceData & { business_slug: string }>;
  error?: string;
}> {
  if (!businessId) {
    return { error: "Business ID is required." };
  }

  try {
    // Use regular client - business_profiles has public read access
    const supabase = await createClient();

    // Get all online business IDs
    const { data: validBusinesses, error: businessError } = await supabase
      .from("business_profiles")
      .select("id")
      .neq("id", businessId)
      .eq("status", "online");

    if (businessError) {
      console.error("Error filtering valid businesses:", businessError);
      return { error: "Failed to filter valid businesses" };
    }

    // If no valid businesses found, return empty result
    if (!validBusinesses || validBusinesses.length === 0) {
      return { data: [] };
    }

    // Get the IDs of valid businesses
    const validBusinessIds = validBusinesses.map((b: { id: string }) => b.id);

    // Fetch products from valid businesses
    const { data, error } = await supabase
      .from("products_services")
      .select(
        `
        id,
        business_id,
        name,
        description,
        base_price,
        discounted_price,
        product_type,
        is_available,
        image_url,
        images,
        featured_image_index,
        created_at,
        updated_at,
        slug,
        business_profiles!business_id(business_slug)
      `
      )
      .in("business_id", validBusinessIds)
      .eq("is_available", true)
      .order("created_at", { ascending: false })
      .limit(limit);

    if (error) {
      console.error("Error fetching products from other businesses:", error);
      return { error: "Failed to fetch products from other businesses" };
    }

    // Transform the data to include business_slug directly and add required fields
    const transformedData = data.map((item: FetchedProduct) => {
      // Extract business_slug from the nested business_profiles object
      // Handle different response formats from Supabase
      let businessSlug = null;
      if (item.business_profiles) {
        if (Array.isArray(item.business_profiles)) {
          businessSlug = item.business_profiles[0]?.business_slug || null;
        } else {
          businessSlug = (item.business_profiles as { business_slug: string | null }).business_slug || null;
        }
      }

      // Create a new object without the nested business_profiles property
      // to avoid issues with serialization and type conflicts
      const { ...productData } = item;

      // Create a properly structured product with all required fields
      return {
        id: productData.id,
        business_id: productData.business_id,
        name: productData.name || "",
        description: productData.description || "",
        base_price: Number(productData.base_price) || 0,
        discounted_price: productData.discounted_price
          ? Number(productData.discounted_price)
          : undefined,
        product_type:
          (productData.product_type as "physical" | "service") || "physical",
        is_available: Boolean(productData.is_available) || false,
        image_url: productData.image_url,
        images: productData.images || [],
        featured_image_index:
          typeof productData.featured_image_index === "number"
            ? productData.featured_image_index
            : 0,
        created_at: productData.created_at
          ? new Date(productData.created_at)
          : undefined,
        updated_at: productData.updated_at
          ? new Date(productData.updated_at)
          : undefined,
        slug: productData.slug,
        business_slug: businessSlug,
      };
    });

    return {
      data: transformedData as Array<
        ProductServiceData & { business_slug: string }
      >,
    };
  } catch (error) {
    console.error("Unexpected error in getProductsFromOtherBusinesses:", error);
    return { error: "An unexpected error occurred" };
  }
}

/**
 * Get business profile with contact information
 */
export async function getBusinessWithContactInfo(businessId: string): Promise<{
  data?: {
    business_name: string;
    whatsapp_number: string | null;
    phone: string | null;
    business_slug: string;
  };
  error?: string;
}> {
  if (!businessId) {
    return { error: "Business ID is required." };
  }

  try {
    // Use the business profile API endpoint for public access
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business/${businessId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result = await response.json();

    if (!response.ok) {
      console.error("Error fetching business profile:", result);
      return { error: "Failed to fetch business details" };
    }

    const data = result.business;
    if (!data) {
      return { error: "Business not found" };
    }

    // Transform data to ensure business_slug is not null
    const transformedData = {
      business_name: data.business_name || "",
      whatsapp_number: data.whatsapp_number,
      phone: data.phone,
      business_slug: data.business_slug || "",
    };

    return { data: transformedData };
  } catch (error) {
    console.error("Unexpected error in getBusinessWithContactInfo:", error);
    return { error: "An unexpected error occurred" };
  }
}
