import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import LikeButton from '@/src/components/ui/LikeButton';

// Mock react-native-reanimated
jest.mock('react-native-reanimated', () => {
  const React = require('react');
  const { View, Text } = require('react-native');
  const Animated = { View, Text };
  return {
    __esModule: true,
    useSharedValue: (initial) => ({ value: initial }),
    useAnimatedStyle: (fn) => fn(),
    withSpring: (value) => value,
    withSequence: (...values) => values[values.length - 1],
    withTiming: (value) => value,
    interpolateColor: () => '#000000',
    runOnJS: (fn) => fn,
    default: Animated,
  };
});
// Mock icons used in component
jest.mock('lucide-react-native', () => {
  const React = require('react');
  const { Text } = require('react-native');
  const Heart = (props: any) => React.createElement(Text, { testID: 'Heart-icon', ...props }, 'Heart');
  return { __esModule: true, Heart, default: { Heart } };
});


// Mock dependencies
jest.mock('@/src/stores/likeCommentStore', () => ({
  useLikeCommentStore: () => ({
    postLikes: {},
    likingPosts: new Set(),
    togglePostLike: jest.fn(),
    getPostLikeStatus: jest.fn(),
  }),
}));

jest.mock('@/src/contexts/ThemeContext', () => ({
  useThemeContext: () => ({
    isDark: false,
  }),
}));

jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: {
      primary: '#f59e0b',
      background: '#ffffff',
      textPrimary: '#000000',
      destructive: '#ef4444',
      border: '#e5e5e5',
    },
  }),
}));

jest.mock('@/src/utils/formatNumber', () => ({
  formatIndianNumberShort: (num: number) => num.toString(),
}));

describe('LikeButton', () => {
  const defaultProps = {
    postId: 'test-post-id',
    postSource: 'business' as const,
    initialLikeCount: 5,
    initialIsLiked: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with theme colors', () => {
    const { toJSON } = render(<LikeButton {...defaultProps} />);
    expect(JSON.stringify(toJSON())).toContain('5');
  });

  it('displays like count when showCount is true', () => {
    const { toJSON } = render(
      <LikeButton {...defaultProps} showCount={true} />
    );
    expect(JSON.stringify(toJSON())).toContain('5');
  });

  it('hides like count when showCount is false', () => {
    const { queryByText } = render(
      <LikeButton {...defaultProps} showCount={false} />
    );
    expect(queryByText('5')).toBeNull();
  });

  it('uses theme colors for ripple effect', () => {
    const { getByTestId } = render(
      <LikeButton {...defaultProps} testID="like-button" />
    );
    const button = getByTestId('like-button');
    expect(button).toBeTruthy();
  });

  it('handles press events', () => {
    const onLikeChange = jest.fn();
    const { getByTestId } = render(
      <LikeButton
        {...defaultProps}
        onLikeChange={onLikeChange}
        testID="like-button"
      />
    );

    const button = getByTestId('like-button');
    fireEvent.press(button);

    // Button press should trigger animation and state changes
    expect(button).toBeTruthy();
  });

  it('respects disabled state', () => {
    const { getByTestId } = render(
      <LikeButton
        {...defaultProps}
        disabled={true}
        testID="like-button"
      />
    );

    const button = getByTestId('like-button');
    expect(button).toBeTruthy();
  });

  it('handles long press events', () => {
    const onLongPress = jest.fn();
    const { getByTestId } = render(
      <LikeButton
        {...defaultProps}
        onLongPress={onLongPress}
        testID="like-button"
      />
    );

    const button = getByTestId('like-button');
    fireEvent(button, 'onLongPress');

    expect(onLongPress).toHaveBeenCalled();
  });

  it('uses proper theme color interpolation', () => {
    // Test that the component properly uses theme.colors.primary and theme.colors.destructive
    const { getByTestId } = render(
      <LikeButton
        {...defaultProps}
        initialIsLiked={true}
        testID="like-button"
      />
    );

    const button = getByTestId('like-button');
    expect(button).toBeTruthy();
  });

  it('formats like count using formatIndianNumberShort', () => {
    const { toJSON } = render(
      <LikeButton {...defaultProps} initialLikeCount={1234} />
    );
    expect(JSON.stringify(toJSON())).toContain('1234'); // Mocked to return the number as string
  });
});