### User Story 2.4: Implement User Logout Endpoint

**Description:** This story creates the endpoint that allows a client to securely terminate its own session by invalidating its refresh token.

**Acceptance Criteria:**
- A new API endpoint `POST /api/auth/logout` is created.
- The endpoint is protected by JWT and HMAC authentication.
- It successfully revokes the provided refresh token.

---

### Development Tasks

-   [ ] **1. Create API Route File Structure**
    *   In the `dukancard` project, create the new API route file:
    *   **File Path:** `dukancard/app/api/auth/logout/route.ts`

-   [ ] **2. Implement Request Body Validation**
    *   Using Zod, define a schema that validates the shape of the incoming request body.
    *   The schema should enforce: `{ refreshToken: z.string() }`.

-   [ ] **3. Implement the Logout Route Handler**
    *   In `dukancard/app/api/auth/logout/route.ts`, implement the `POST` handler.
    *   **Note:** This endpoint must be protected by the full security middleware stack (JWT validation, HMAC signature verification, etc.).
    *   **Logic Steps:**
        1.  **Authentication & Authorization:** The middleware will handle this. The handler can assume the request is from a valid, authenticated user and device.
        2.  **Validate Request Body:** Parse and validate the incoming payload.
        3.  **Hash the Refresh Token:** Use the `hashSecret` utility to hash the provided `refreshToken` string so it can be found in the database.
        4.  **Revoke the Token:**
            *   Use the service role Supabase client.
            *   Find the record in the `refresh_tokens` table where the `refresh_token_hash` matches the hash you just created.
            *   If a record is found, update it by setting `revoked = true`.
        5.  **Return Success:** Respond with `200 OK` and a `{ "success": true }` body. **Important:** Return a success response even if the token was not found. This prevents attackers from using this endpoint to guess valid tokens.

-   [ ] **4. Write Integration Tests**
    *   Create a new test file.
    *   **File Path:** `dukancard/__tests__/app/api/auth/logout/route.test.ts`
    *   **Test Cases:**
        *   **Test 1: Successful Logout:** Seed the database with a valid device and refresh token. Mock a valid authenticated request (i.e., provide the necessary headers and user context that the middleware would). Call the endpoint with the correct `refreshToken`. Assert a `200 OK` response. Verify that the `revoked` flag for that token in the database has been set to `true`.
        *   **Test 2: Logout with Non-existent Token:** Mock a valid authenticated request. Call the endpoint with a `refreshToken` that is not in the database. Assert a `200 OK` response and verify no database write operations occurred.
        *   **Test 3: Unauthorized Request:** Attempt to call the endpoint without valid authentication context. Assert that the response is `401 Unauthorized` or `403 Forbidden` (depending on which middleware check fails).