import {
  validateRequestTimestamp,
  timestampMiddleware,
  getServerTimestamp,
  isTimestampFresh,
  TimestampConfig
} from '@/lib/middleware/timestamp';

describe('Timestamp Middleware', () => {
  beforeEach(() => {
    // Mock current time for consistent testing
    jest.spyOn(Date, 'now').mockReturnValue(1640995200000); // Fixed timestamp
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('validateRequestTimestamp', () => {
    it('should succeed for valid current timestamp', () => {
      const currentTimestamp = '1640995200000';
      
      const result = validateRequestTimestamp(currentTimestamp);
      
      expect(result.success).toBe(true);
    });

    it('should succeed for timestamp within default window', () => {
      const recentTimestamp = '1640995140000'; // 60 seconds ago
      
      const result = validateRequestTimestamp(recentTimestamp);
      
      expect(result.success).toBe(true);
    });

    it('should fail for timestamp outside default window', () => {
      const oldTimestamp = '1640995000000'; // 200 seconds ago
      
      const result = validateRequestTimestamp(oldTimestamp);
      
      expect(result.success).toBe(false);
      expect(result.status).toBe(408);
      expect(result.error).toBe('Request has expired');
    });

    it('should succeed for timestamp within custom window', () => {
      const oldTimestamp = '1640994900000'; // 300 seconds ago
      const config: TimestampConfig = { windowSeconds: 400 }; // 400 second window
      
      const result = validateRequestTimestamp(oldTimestamp, config);
      
      expect(result.success).toBe(true);
    });

    it('should fail for timestamp outside custom window', () => {
      const oldTimestamp = '1640994800000'; // 400 seconds ago
      const config: TimestampConfig = { windowSeconds: 300 }; // 300 second window
      
      const result = validateRequestTimestamp(oldTimestamp, config);
      
      expect(result.success).toBe(false);
      expect(result.status).toBe(408);
      expect(result.error).toBe('Request has expired');
    });

    it('should succeed for future timestamp within window', () => {
      const futureTimestamp = '1640995260000'; // 60 seconds in future
      
      const result = validateRequestTimestamp(futureTimestamp);
      
      expect(result.success).toBe(true);
    });

    it('should fail for future timestamp outside window', () => {
      const futureTimestamp = '1640995400000'; // 200 seconds in future
      
      const result = validateRequestTimestamp(futureTimestamp);
      
      expect(result.success).toBe(false);
      expect(result.status).toBe(408);
      expect(result.error).toBe('Request has expired');
    });

    it('should fail for null timestamp when required', () => {
      const result = validateRequestTimestamp(null);
      
      expect(result.success).toBe(false);
      expect(result.status).toBe(400);
      expect(result.error).toBe('Missing X-Timestamp header');
    });

    it('should succeed for null timestamp when not required', () => {
      const config: TimestampConfig = { required: false };
      
      const result = validateRequestTimestamp(null, config);
      
      expect(result.success).toBe(true);
    });

    it('should fail for invalid timestamp format', () => {
      const invalidTimestamp = 'not-a-number';
      
      const result = validateRequestTimestamp(invalidTimestamp);
      
      expect(result.success).toBe(false);
      expect(result.status).toBe(400);
      expect(result.error).toBe('Invalid timestamp format');
    });

    it('should handle empty string timestamp', () => {
      const emptyTimestamp = '';
      
      const result = validateRequestTimestamp(emptyTimestamp);
      
      expect(result.success).toBe(false);
      expect(result.status).toBe(400);
      expect(result.error).toBe('Invalid timestamp format');
    });

    it('should handle zero timestamp', () => {
      const zeroTimestamp = '0';
      
      const result = validateRequestTimestamp(zeroTimestamp);
      
      expect(result.success).toBe(false);
      expect(result.status).toBe(408);
      expect(result.error).toBe('Request has expired');
    });
  });

  describe('timestampMiddleware', () => {
    const createMockRequest = (timestamp?: string) => {
      return {
        headers: {
          get: (name: string) => {
            if (name.toLowerCase() === 'x-timestamp') {
              return timestamp || null;
            }
            return null;
          },
        },
      } as any;
    };

    it('should succeed with valid timestamp in request', async () => {
      const request = createMockRequest('1640995200000');
      
      const result = await timestampMiddleware(request);
      
      expect(result.success).toBe(true);
    });

    it('should fail with expired timestamp in request', async () => {
      const request = createMockRequest('1640995000000'); // 200 seconds ago
      
      const result = await timestampMiddleware(request);
      
      expect(result.success).toBe(false);
      expect(result.status).toBe(408);
      expect(result.error).toBe('Request has expired');
    });

    it('should fail with missing timestamp in request', async () => {
      const request = createMockRequest(); // No timestamp
      
      const result = await timestampMiddleware(request);
      
      expect(result.success).toBe(false);
      expect(result.status).toBe(400);
      expect(result.error).toBe('Missing X-Timestamp header');
    });

    it('should succeed when validation is not required', async () => {
      const request = createMockRequest(); // No timestamp
      const config: TimestampConfig = { required: false };
      
      const result = await timestampMiddleware(request, config);
      
      expect(result.success).toBe(true);
    });

    it('should use custom window configuration', async () => {
      const request = createMockRequest('1640994900000'); // 300 seconds ago
      const config: TimestampConfig = { windowSeconds: 400 }; // 400 second window
      
      const result = await timestampMiddleware(request, config);
      
      expect(result.success).toBe(true);
    });

    it('should handle header extraction errors gracefully', async () => {
      // Mock request that throws error on header access
      const request = {
        headers: {
          get: () => {
            throw new Error('Header access failed');
          },
        },
      } as any;
      
      const result = await timestampMiddleware(request);
      
      expect(result.success).toBe(false);
      expect(result.status).toBe(500);
      expect(result.error).toBe('Internal Server Error');
    });
  });

  describe('getServerTimestamp', () => {
    it('should return current server time', () => {
      const timestamp = getServerTimestamp();
      
      expect(timestamp).toBe(1640995200000);
      expect(typeof timestamp).toBe('number');
    });
  });

  describe('isTimestampFresh', () => {
    it('should return true for current timestamp', () => {
      const result = isTimestampFresh(1640995200000);
      
      expect(result).toBe(true);
    });

    it('should return true for timestamp within default window', () => {
      const recentTime = 1640995140000; // 60 seconds ago
      
      const result = isTimestampFresh(recentTime);
      
      expect(result).toBe(true);
    });

    it('should return false for timestamp outside default window', () => {
      const oldTime = 1640995000000; // 200 seconds ago
      
      const result = isTimestampFresh(oldTime);
      
      expect(result).toBe(false);
    });

    it('should return true for timestamp within custom window', () => {
      const oldTime = 1640994900000; // 300 seconds ago
      
      const result = isTimestampFresh(oldTime, 400); // 400 second window
      
      expect(result).toBe(true);
    });

    it('should return false for timestamp outside custom window', () => {
      const oldTime = 1640994800000; // 400 seconds ago
      
      const result = isTimestampFresh(oldTime, 300); // 300 second window
      
      expect(result).toBe(false);
    });

    it('should handle future timestamps correctly', () => {
      const futureTime = 1640995260000; // 60 seconds in future
      
      const result = isTimestampFresh(futureTime);
      
      expect(result).toBe(true);
    });

    it('should reject future timestamps outside window', () => {
      const futureTime = 1640995400000; // 200 seconds in future
      
      const result = isTimestampFresh(futureTime);
      
      expect(result).toBe(false);
    });
  });
});