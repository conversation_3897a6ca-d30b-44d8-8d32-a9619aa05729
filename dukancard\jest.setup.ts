import '@testing-library/jest-dom';

// Polyfill for TextEncoder
global.TextEncoder = require('util').TextEncoder;

// Mock fetch function for tests
global.fetch = jest.fn() as jest.Mock;

// Polyfill for Request, Response, Headers
// These are minimal polyfills to satisfy TypeScript and basic usage in tests.
// For more complex scenarios, consider a dedicated polyfill library or more detailed mocks.

// Mock the global Headers object
class MockHeaders {
  private headers: Map<string, string>;

  constructor(init?: HeadersInit) {
    this.headers = new Map();
    if (init) {
      if (Array.isArray(init)) {
        init.forEach(([name, value]) => this.headers.set(name.toLowerCase(), value));
      } else if (init instanceof Headers) {
        init.forEach((value, name) => this.headers.set(name.toLowerCase(), value));
      } else {
        Object.entries(init).forEach(([name, value]) => this.headers.set(name.toLowerCase(), value));
      }
    }
  }

  append(name: string, value: string) {
    this.headers.set(name.toLowerCase(), value);
  }

  delete(name: string) {
    this.headers.delete(name.toLowerCase());
  }

  get(name: string) {
    return this.headers.get(name.toLowerCase()) || null;
  }

  has(name: string) {
    return this.headers.has(name.toLowerCase());
  }

  set(name: string, value: string) {
    this.headers.set(name.toLowerCase(), value);
  }

  forEach(callbackfn: (value: string, key: string, parent: Headers) => void, thisArg?: any) {
    this.headers.forEach((value, key) => callbackfn.call(thisArg, value, key, this as any));
  }

  // Mock implementations for missing Headers methods
  getSetCookie(): string[] {
    return [];
  }

  *entries(): IterableIterator<[string, string]> {
    for (const entry of this.headers.entries()) {
      yield entry;
    }
  }

  *keys(): IterableIterator<string> {
    for (const key of this.headers.keys()) {
      yield key;
    }
  }

  *values(): IterableIterator<string> {
    for (const value of this.headers.values()) {
      yield value;
    }
  }

  [Symbol.iterator](): IterableIterator<[string, string]> {
    return this.entries();
  }
}
global.Headers = MockHeaders as any;

// Mock the global Request object
global.Request = class MockRequest {
  url: string;
  method: string;
  headers: Headers;
  private _body: string;

  constructor(input: RequestInfo | URL, init?: RequestInit) {
    this.url = ''; // Initialize url to satisfy TypeScript
    if (input instanceof Request) {
      // If input is a Request object, define 'url' as a getter
      Object.defineProperty(this, 'url', {
        get: () => input.url,
        enumerable: true,
        configurable: true,
      });
    } else {
      this.url = typeof input === 'string' ? input : (input as URL).href;
    }
    this.method = init?.method || 'GET';
    this.headers = new MockHeaders(init?.headers) as any;
    this._body = typeof init?.body === 'string' ? init.body : JSON.stringify(init?.body);
  }

  async json() {
    try {
      return JSON.parse(this._body);
    } catch {
      return {};
    }
  }

  async text() {
    return this._body;
  }
} as any;

// Mock the global Response object
global.Response = class MockResponse {
  status: number;
  statusText: string;
  headers: Headers;
  ok: boolean;
  body: any;
  private _body: string;

  constructor(body?: BodyInit | null, init?: ResponseInit) {
    this._body = typeof body === 'string' ? body : JSON.stringify(body);
    this.status = init?.status || 200;
    this.statusText = init?.statusText || 'OK';
    this.ok = this.status >= 200 && this.status < 300;
    this.headers = new MockHeaders(init?.headers) as any;
    this.body = body;
  }

  async json() {
    try {
      return JSON.parse(this._body);
    } catch {
      return {};
    }
  }

  async text() {
    return this._body;
  }

  clone() {
    return new MockResponse(this.body, {
      status: this.status,
      statusText: this.statusText,
      headers: this.headers,
    });
  }

  // Add static json method for NextResponse.json() compatibility
  static json(data: any, init?: ResponseInit) {
    return new MockResponse(JSON.stringify(data), {
      ...init,
      headers: {
        'Content-Type': 'application/json',
        ...(init?.headers || {}),
      },
    });
  }
} as any;

// Mock NextResponse for API route testing
jest.mock('next/server', () => {
  const MockResponse = global.Response;
  
  class MockNextResponse extends MockResponse {
    static json(data: any, init?: ResponseInit) {
      return new MockNextResponse(JSON.stringify(data), {
        ...init,
        headers: {
          'Content-Type': 'application/json',
          ...(init?.headers || {}),
        },
      });
    }
  }

  return {
    NextResponse: MockNextResponse,
    NextRequest: class MockNextRequest extends global.Request {
      constructor(url: string, init?: RequestInit) {
        super(url, init);
      }
    },
  };
});

// Polyfill for IntersectionObserver
global.IntersectionObserver = class MockIntersectionObserver implements IntersectionObserver {
  readonly root: Element | null = null;
  readonly rootMargin: string = '';
  readonly thresholds: ReadonlyArray<number> = [];
  takeRecords = () => [];
  observe = jest.fn();
  unobserve = jest.fn();
  disconnect = jest.fn();

  constructor(callback: IntersectionObserverCallback, options?: IntersectionObserverInit) {
    // Mock constructor, no actual functionality needed for most tests
  }
} as typeof IntersectionObserver;

jest.mock('@/utils/supabase/client', () => ({
  createClient: jest.fn(() => ({
    from: jest.fn(() => ({
      on: jest.fn(() => ({
        subscribe: jest.fn(),
        unsubscribe: jest.fn(),
      })),
    })),
    channel: jest.fn(() => ({
      on: jest.fn(() => ({
        subscribe: jest.fn(),
        unsubscribe: jest.fn(),
      })),
    })),
  })),
}));

// Suppress console.error messages from tests that intentionally trigger them
const originalConsoleError = console.error;
// console.error = (...args: any[]) => {
//   // You can add logic here to filter specific errors if needed,
//   // but for now, we'll just suppress all of them from printing.
//   // If you want to see specific errors, you can add conditions like:
//   // if (!args[0].includes('Expected error message')) {
//   //   originalConsoleError(...args);
//   // }
// };

// Suppress console.warn messages from tests
const originalConsoleWarn = console.warn;
console.warn = (...args: any[]) => {};

// Suppress console.log messages from tests
const originalConsoleLog = console.log;
// console.log = (...args: any[]) => {};

// If you need to restore console.error for specific tests,
// you can do it within those tests like:
// beforeEach(() => {
//   console.error = originalConsoleError;
// });
// afterEach(() => {
//   console.error = () => {}; // Re-suppress after the test
// });
