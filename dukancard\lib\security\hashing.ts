// dukancard/lib/security/hashing.ts
import bcrypt from 'bcryptjs';

const SALT_ROUNDS = 10;

/**
 * Hashes a plaintext secret.
 * @param secret The plaintext secret to hash.
 * @returns A promise that resolves to the hashed secret.
 */
export async function hashSecret(secret: string): Promise<string> {
    return bcrypt.hash(secret, SALT_ROUNDS);
}

/**
 * Compares a plaintext secret against a hash.
 * @param secret The plaintext secret.
 * @param hash The hash to compare against.
 * @returns A promise that resolves to true if the secret matches the hash, false otherwise.
 */
export async function compareSecret(secret: string, hash: string): Promise<boolean> {
    return bcrypt.compare(secret, hash);
}