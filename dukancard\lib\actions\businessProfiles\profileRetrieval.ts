"use server";

import {
  BusinessProfilePublicData,
  BusinessProfileWithProducts,
} from "./types";

/**
 * Securely fetch a business profile by slug using the service role key
 * This bypasses RLS and ensures sensitive data is not exposed to the client
 */
export async function getSecureBusinessProfileBySlug(slug: string): Promise<{
  data?: BusinessProfilePublicData;
  error?: string;
}> {
  if (!slug) {
    return { error: "Business slug is required." };
  }

  try {
    // Use the business profile API endpoint for public access by slug
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business/slug/${slug}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result = await response.json();

    if (!response.ok) {
      console.error("API Fetch Error:", result);
      return {
        error: result.error || "Failed to fetch business profile",
      };
    }

    const profileData = result.business;
    if (!profileData) {
      return { error: "Profile not found." };
    }

    return { data: profileData };
  } catch (e) {
    console.error("Exception in getSecureBusinessProfileBySlug:", e);
    return { error: "An unexpected error occurred." };
  }
}

/**
 * Securely fetch a business profile with products by slug using the service role key
 */
export async function getSecureBusinessProfileWithProductsBySlug(
  slug: string
): Promise<{
  data?: BusinessProfileWithProducts;
  error?: string;
}> {
  if (!slug) {
    return { error: "Business slug is required." };
  }

  try {
    // Use the business profile API endpoint for public access by slug
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business/slug/${slug}?include_products=true`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result = await response.json();

    if (!response.ok) {
      console.error("API Fetch Error:", result);
      return {
        error: result.error || "Failed to fetch business profile",
      };
    }

    const profileData = result.business;
    if (!profileData) {
      return { error: "Profile not found." };
    }

    const safeData: BusinessProfileWithProducts = {
      ...profileData,
      products_services: profileData.products_services || [],
    };

    return { data: safeData };
  } catch (e) {
    console.error(
      "Exception in getSecureBusinessProfileWithProductsBySlug:",
      e
    );
    return { error: "An unexpected error occurred." };
  }
}
