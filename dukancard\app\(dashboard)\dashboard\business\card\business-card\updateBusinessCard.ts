"use server";

import { createClient } from "@/utils/supabase/server";
import { revalidatePath } from "next/cache";
import { BusinessCardData } from "../schema";
import { validateBusinessCardData } from "../validation/businessCardValidation";
import { generateUniqueSlug } from "../slug/slugUtils";
import { processBusinessHours } from "../utils/businessHoursProcessor";

/**
 * Updates business card data with validation and processing
 * @param formData - The business card data to update
 * @returns Success/error response with updated data
 */
export async function updateBusinessCard(
  formData: BusinessCardData
): Promise<{ success: boolean; error?: string; data?: BusinessCardData }> {
  const supabase = await createClient();

  // 1. Validate the incoming data
  const validatedFields = validateBusinessCardData(formData);

  if (!validatedFields.success) {
    console.error(
      "Validation Error:",
      validatedFields.error.flatten().fieldErrors
    );
    return {
      success: false,
      error: "Invalid data provided. Please check the form fields.",
    };
  }

  // 2. Get the authenticated user
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    console.error("Auth Error:", authError);
    return { success: false, error: "User not authenticated." };
  }

  // Note: Phone comparison is now handled by the API route

  // Platform is now free for all users

  // 4. Handle Slug Logic if going online
  let finalSlug = validatedFields.data.business_slug;

  if (validatedFields.data.status === "online") {
    const slugResult = await generateUniqueSlug(
      validatedFields.data.business_name,
      finalSlug || "",
      user.id
    );

    if (!slugResult.success) {
      return {
        success: false,
        error: slugResult.error || "Failed to generate unique slug."
      };
    }

    finalSlug = slugResult.slug;
  } else {
    finalSlug = validatedFields.data.business_slug;
  }

  // Custom branding features removed since payment plans are no longer supported

  // Note: Phone uniqueness check removed as multiple businesses/customers can share the same number

  // 7. Prepare data for Supabase update
  const businessHoursData = processBusinessHours(validatedFields.data.business_hours);

  const dataToUpdate: Partial<BusinessCardData> = {
    business_name: validatedFields.data.business_name,
    member_name: validatedFields.data.member_name,
    title: validatedFields.data.title,
    logo_url: validatedFields.data.logo_url,
    established_year: validatedFields.data.established_year,
    address_line: validatedFields.data.address_line,
    city: validatedFields.data.city,
    state: validatedFields.data.state,
    pincode: validatedFields.data.pincode,
    phone: validatedFields.data.phone,
    delivery_info: validatedFields.data.delivery_info,
    
    instagram_url: validatedFields.data.instagram_url,
    facebook_url: validatedFields.data.facebook_url,
    whatsapp_number: validatedFields.data.whatsapp_number,
    about_bio: validatedFields.data.about_bio,
    locality: validatedFields.data.locality,
    business_hours: businessHoursData,
    status: validatedFields.data.status,
    business_slug: finalSlug,
    contact_email: validatedFields.data.contact_email,
    business_category: validatedFields.data.business_category,
  };

  // 7. Update the business profile via API
  const { data: { session } } = await supabase.auth.getSession();
  if (!session?.access_token) {
    return { success: false, error: "Authentication session not found." };
  }

  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business/${user.id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`,
      },
      body: JSON.stringify(dataToUpdate),
    });

    const result = await response.json();

    if (!response.ok) {
      console.error("API Update Error:", result);
      return {
        success: false,
        error: result.error || "Failed to update profile",
      };
    }

    const updatedProfile = result.business;
    if (!updatedProfile) {
      return {
        success: false,
        error: "Failed to update profile. Profile not found after update.",
      };
    }

    // 9. Revalidate paths
    revalidatePath("/dashboard/business/card");
    if (dataToUpdate.status === "online" && dataToUpdate.business_slug) {
      revalidatePath(`/(main)/card/${dataToUpdate.business_slug}`, "page");
    }

    // 10. Return success response with the updated data
    return { success: true, data: updatedProfile as BusinessCardData };
  } catch (fetchError) {
    console.error("Network error updating business profile:", fetchError);
    return { success: false, error: "Network error. Please try again." };
  }
}
