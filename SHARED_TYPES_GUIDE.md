# Shared Types Synchronization Guide

## 1. Overview

This project contains two separate applications (`dukancard` and `dukancard-app`) that share a single Supabase database. To ensure both applications are always aware of the correct database schema and to prevent data-related bugs, we use TypeScript types generated directly from the database schema.

This guide documents the **mandatory process** for updating and synchronizing these types after any database schema change.

**The `dukancard` (Next.js) project is the single source of truth for generating the types.** The flow is always one-way: `dukancard` -> `dukancard-app`.

---

## 2. The Synchronization Process

This process must be followed for **any task that includes a database migration**.

**Step 1: Make Database Changes**
*   Create your new `.sql` migration file in the `dukancard/supabase/migrations` directory as usual.

**Step 2: Apply Migrations and Reset Local Database**
*   Run the reset command from the `dukancard` directory. This will apply all migrations, including your new one.
*   `supabase db reset`

**Step 3: Regenerate the Master Types File**
*   Once the database is up to date, run the following command from the `dukancard` directory to regenerate the master `supabase.ts` file.
*   `supabase gen types typescript --local > lib/types/supabase.ts`

**Step 4: Copy the Updated Types File**
*   Manually copy the newly updated file from the `dukancard` project:
*   `dukancard/lib/types/supabase.ts`

**Step 5: Paste and Overwrite in the Mobile App**
*   Paste the file into the `dukancard-app` project, overwriting the existing file at:
*   `dukancard-app/src/types/supabase.ts`

**Step 6: Commit Changes**
*   Commit the new migration file and the updated `supabase.ts` file in the `dukancard` project.
*   Commit the updated `supabase.ts` file in the `dukancard-app` project.

---

## 3. Definition of Done

A pull request that contains a database migration will not be considered "Done" and cannot be merged unless:

-   [ ] The PR for the `dukancard` repository includes the updated `supabase.ts` file.
-   [ ] A corresponding PR for the `dukancard-app` repository has been created with its updated `supabase.ts` file.

---

## 4. Quick Reference Commands

From the `dukancard` directory:

```bash
# Apply migrations
supabase db reset

# Generate types
supabase gen types typescript --local > lib/types/supabase.ts
```

Then manually copy `dukancard/lib/types/supabase.ts` to `dukancard-app/src/types/supabase.ts`.

---

## 5. Troubleshooting

**Problem**: `supabase gen types` command fails
**Solution**: Ensure you're in the `dukancard` directory and that your local Supabase instance is running with `supabase start`

**Problem**: Types don't seem to update after copying
**Solution**: Restart your development server and TypeScript language server in your IDE

**Problem**: Build errors after updating types
**Solution**: Check for any breaking changes in the schema and update dependent code accordingly