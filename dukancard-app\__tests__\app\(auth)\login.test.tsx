/**
 * @jest-environment node
 */
import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react-native';
import LoginScreen from '@/app/(auth)/login';
import { useAuth } from '@/src/contexts/AuthContext';
import { useToast } from '@/src/components/ui/Toast';
import { useAuthErrorHandler } from '@/src/hooks/useAuthErrorHandler';
import { router } from 'expo-router';
import {
  sendEmailOTP,
  validateEmail,
  validateOTP,
  verifyEmailOTP,
} from '@/backend/supabase/services/auth/emailOtpService';
import { signInWithGoogleNative } from '@/backend/supabase/services/auth/nativeGoogleAuth2025';
import { MobileAuthService } from '@/backend/supabase/services/auth/mobileAuthService';

// Mock all external dependencies
jest.mock('@/src/contexts/AuthContext', () => ({
  useAuth: jest.fn(),
}));
jest.mock('@/src/hooks/useTheme');
jest.mock('@/src/components/ui/Toast', () => ({
  useToast: jest.fn(),
}));
jest.mock('@/src/hooks/useAuthErrorHandler', () => ({
  useAuthErrorHandler: jest.fn(),
}));
jest.mock('@/backend/supabase/services/auth/emailOtpService', () => ({
  sendEmailOTP: jest.fn(),
  validateEmail: jest.fn(),
  validateOTP: jest.fn(),
  verifyEmailOTP: jest.fn(),
}));
jest.mock('@/backend/supabase/services/auth/nativeGoogleAuth2025', () => ({
  signInWithGoogleNative: jest.fn(),
}));
jest.mock('@/backend/supabase/services/auth/mobileAuthService', () => ({
  MobileAuthService: {
    signInWithMobilePassword: jest.fn(),
  },
}));
jest.mock('@/styles/auth/login-styles');

// Mock theme
const mockTheme = {
  colors: {
    primary: '#D4AF37',
    textSecondary: '#666666',
    background: '#FFFFFF',
  },
  spacing: { md: 16, lg: 24 },
  typography: { fontSize: { base: 16 } },
};

jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => mockTheme,
}));

// Mock styles
jest.mock('@/styles/auth/login-styles', () => ({
  createLoginStyles: () => ({
    safeArea: { flex: 1 },
    formContainer: { padding: 20 },
    loginSignUpContainer: { flexDirection: 'row' },
    loginSignUpLine: { flex: 1 },
    loginSignUpText: { marginHorizontal: 10 },
    dividerContainer: { flexDirection: 'row' },
    dividerLine: { flex: 1 },
    dividerText: { marginHorizontal: 10 },
    socialLoginContainer: { flexDirection: 'row' },
    googleButton: { flex: 1 },
    mobileButton: { flex: 1 },
    otpFullScreenContainer: { flex: 1 },
    otpScrollContent: { flexGrow: 1 },
    otpHeaderSection: { alignItems: 'center' },
    otpIconContainer: { padding: 10 },
    otpIcon: { width: 50, height: 50 },
    otpMainTitle: { fontSize: 24 },
    otpSubtitle: { fontSize: 16 },
    otpEmailDisplay: { fontSize: 16 },
    otpInputSection: { padding: 20 },
    otpInputLabel: { fontSize: 14 },
    otpInputWrapper: { flexDirection: 'row' },
    otpLoadingOverlay: { position: 'absolute' },
    otpActionsSection: { padding: 20 },
    otpResendButton: { padding: 10 },
    otpButtonDisabled: { opacity: 0.5 },
    otpResendButtonText: { color: 'blue' },
    otpButtonTextDisabled: { color: 'gray' },
    otpChangeEmailButton: { padding: 10 },
    otpChangeEmailText: { color: 'blue' },
    twoRowContainer: { flex: 1 },
    upperRow: { flex: 1 },
    backgroundImage: { flex: 1 },
    logoOverlay: { position: 'absolute' },
    lowerRow: { flex: 1 },
    loadingContainer: { flex: 1 },
    loadingText: { fontSize: 16 },
    platformTagline: { fontSize: 18 },
    footerContainer: { padding: 10 },
    footerLinksContainer: { flexDirection: 'row' },
    footerText: { fontSize: 12 },
    footerLinkText: { fontSize: 12 },
    centeredView: { flex: 1 },
    modalView: { margin: 20 },
    closeButton: { position: 'absolute' },
    modalText: { fontSize: 20 },
    mobileInputContainer: { marginBottom: 10 },
    inputLabel: { fontSize: 14 },
    countryCodeContainer: { padding: 5 },
    countryCode: { fontSize: 16 },
    mobileInputWrapper: { flexDirection: 'row' },
    mobileInput: { flex: 1 },
    errorText: { color: 'red' },
    absolute: { position: 'absolute', top: 0, bottom: 0, left: 0, right: 0 },
    otpMainContainer: { flex: 1 },
  }),
}));

// Mock UI components
jest.mock('@/src/components/ui/Button', () => ({
  Button: ({ children, onPress, title, testID, ...props }: any) => (
    <button onClick={onPress} data-testid={testID} {...props}>
      {title || children}
    </button>
  ),
}));

jest.mock('@/src/components/ui/Input', () => ({
  Input: ({ onChangeText, testID, placeholder, ...props }: any) => (
    <input
      onChange={(e) => onChangeText?.(e.target.value)}
      data-testid={testID}
      placeholder={placeholder}
      {...props}
    />
  ),
}));

jest.mock('@/src/components/ui/OTPInput', () => ({
  OTPInput: ({ onComplete, onChangeText, testID, ...props }: any) => (
    <input
      onChange={(e) => {
        onChangeText?.(e.target.value);
        if (e.target.value.length === 6) {
          onComplete?.(e.target.value);
        }
      }}
      data-testid={testID || 'otp-input'}
      maxLength={6}
      {...props}
    />
  ),
}));

jest.mock('@/src/components/ui/DukancardLogo', () => ({
  DukancardLogo: ({ testID }: any) => <div data-testid={testID || 'dukancard-logo'}>Logo</div>,
}));

jest.mock('@/src/components/ui/GoogleIcon', () => ({
  GoogleIcon: ({ testID }: any) => <div data-testid={testID || 'google-icon'}>G</div>,
}));

describe('LoginScreen', () => {
  const mockCheckUserRole = jest.fn();
  const mockToast = {
    success: jest.fn(),
    error: jest.fn(),
    warning: jest.fn(),
    info: jest.fn(),
  };
  const mockErrorHandler = {
    executeWithErrorHandling: jest.fn(),
    clearError: jest.fn(),
    isOnline: true,
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Setup mock implementations
    (useAuth as jest.Mock).mockReturnValue({
      checkUserRole: mockCheckUserRole,
    });

    (useToast as jest.Mock).mockReturnValue(mockToast);

    (useAuthErrorHandler as jest.Mock).mockReturnValue(mockErrorHandler);

    // Mock validation functions
    (validateEmail as jest.Mock).mockReturnValue({ isValid: true });
    (validateOTP as jest.Mock).mockReturnValue({ isValid: true });

    // Mock email OTP service
    (sendEmailOTP as jest.Mock).mockResolvedValue({ success: true });
    (verifyEmailOTP as jest.Mock).mockResolvedValue({ success: true });

    // Mock Google auth service
    (signInWithGoogleNative as jest.Mock).mockResolvedValue({ success: true });

    // Mock mobile auth service
    (MobileAuthService.signInWithMobilePassword as jest.Mock).mockResolvedValue({
      data: { user: { id: 'test-user' } },
      error: null,
    });

    // Reset mock responses for the global mocks
    mockCheckUserRole.mockResolvedValue({
      needsRoleSelection: false,
      needsOnboarding: false,
      role: 'customer',
    });
  });

  describe('Initial Render', () => {
    it('should render the login screen with all main elements', () => {
      render(<LoginScreen />);
      
      expect(screen.getByText('Login or Sign Up')).toBeTruthy();
      expect(screen.getByText('Shop what you TRUST, not just what\'s FAST')).toBeTruthy();
      expect(screen.getByPlaceholderText('Enter your email address')).toBeTruthy();
      expect(screen.getByText('Continue')).toBeTruthy();
      expect(screen.getByText('or')).toBeTruthy();
    });

    it('should render Google and mobile login buttons', () => {
      render(<LoginScreen />);
      
      expect(screen.getByTestId('google-icon')).toBeTruthy();
      // Mobile button should be present (Phone icon)
      const buttons = screen.getAllByRole('button');
      expect(buttons.length).toBeGreaterThanOrEqual(3); // Continue, Google, Mobile
    });

    it('should render footer with Terms and Privacy links', () => {
      render(<LoginScreen />);
      
      expect(screen.getByText('By continuing, you agree to our')).toBeTruthy();
      expect(screen.getByText('Terms of Service')).toBeTruthy();
      expect(screen.getByText('Privacy Policy')).toBeTruthy();
    });
  });

  describe('Email Step', () => {
    it('should handle email input changes', () => {
      render(<LoginScreen />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email address');
      fireEvent.changeText(emailInput, '<EMAIL>');
      
      expect(emailInput.props.value).toBe('<EMAIL>');
    });

    it('should call sendEmailOTP when continue button is pressed with valid email', async () => {
      mockErrorHandler.executeWithErrorHandling.mockImplementation(async ({ operation, onSuccess }) => {
        const result = await operation();
        onSuccess?.(result);
        return result;
      });

      render(<LoginScreen />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email address');
      const continueButton = screen.getByText('Continue');
      
      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.press(continueButton);
      
      await waitFor(() => {
        expect(sendEmailOTP).toHaveBeenCalledWith('<EMAIL>');
      });
    });

    it('should show error when email validation fails', async () => {
      (validateEmail as jest.Mock).mockReturnValue({
        isValid: false,
        message: 'Invalid email format',
      });

      render(<LoginScreen />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email address');
      const continueButton = screen.getByText('Continue');
      
      fireEvent.changeText(emailInput, 'invalid-email');
      fireEvent.press(continueButton);
      
      // Should not call the service
      expect(sendEmailOTP).not.toHaveBeenCalled();
    });

    it('should navigate to OTP step when email is sent successfully', async () => {
      mockErrorHandler.executeWithErrorHandling.mockImplementation(async ({ operation, onSuccess }) => {
        const result = await operation();
        onSuccess?.(result);
        return result;
      });

      render(<LoginScreen />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email address');
      const continueButton = screen.getByText('Continue');
      
      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.press(continueButton);
      
      await waitFor(() => {
        expect(screen.getByText('Verify Your Email')).toBeTruthy();
      });
    });
  });

  describe('OTP Step', () => {
    beforeEach(async () => {
      mockErrorHandler.executeWithErrorHandling.mockImplementation(async ({ operation, onSuccess }) => {
        const result = await operation();
        onSuccess?.(result);
        return result;
      });

      render(<LoginScreen />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email address');
      const continueButton = screen.getByText('Continue');
      
      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.press(continueButton);
      
      await waitFor(() => {
        expect(screen.getByText('Verify Your Email')).toBeTruthy();
      });
    });

    it('should render OTP input screen', () => {
      expect(screen.getByText('Verify Your Email')).toBeTruthy();
      expect(screen.getByText('We&apos;ve sent a 6-digit verification code to')).toBeTruthy();
      expect(screen.getByText('<EMAIL>')).toBeTruthy();
      expect(screen.getByTestId('otp-input')).toBeTruthy();
    });

    it('should handle OTP input and verification', async () => {
      const otpInput = screen.getByTestId('otp-input');
      
      fireEvent.changeText(otpInput, '123456');
      
      await waitFor(() => {
        expect(verifyEmailOTP).toHaveBeenCalledWith('<EMAIL>', '123456');
      });
    });

    it('should show error when OTP validation fails', async () => {
      (validateOTP as jest.Mock).mockReturnValue({
        isValid: false,
        message: 'Invalid OTP format',
      });

      const otpInput = screen.getByTestId('otp-input');
      
      fireEvent.changeText(otpInput, '12345');
      
      // Should not call verification
      await waitFor(() => {
        expect(verifyEmailOTP).not.toHaveBeenCalled();
      });
    });

    it('should handle resend OTP functionality', async () => {
      const resendButton = screen.getByText('Resend code');
      
      fireEvent.press(resendButton);
      
      await waitFor(() => {
        expect(sendEmailOTP).toHaveBeenCalledWith('<EMAIL>');
      });
    });

    it('should allow going back to email step', () => {
      const changeEmailButton = screen.getByText('← Change email');
      
      fireEvent.press(changeEmailButton);
      
      expect(screen.getByPlaceholderText('Enter your email address')).toBeTruthy();
    });

    it('should redirect after successful OTP verification', async () => {
      const otpInput = screen.getByTestId('otp-input');
      
      fireEvent.changeText(otpInput, '123456');
      
      await waitFor(() => {
        expect(mockCheckUserRole).toHaveBeenCalled();
        expect(router.replace).toHaveBeenCalledWith('/(dashboard)/customer');
      });
    });
  });

  describe('Google Login', () => {
    it('should call Google sign-in when Google button is pressed', async () => {
      mockErrorHandler.executeWithErrorHandling.mockImplementation(async ({ operation, onSuccess }) => {
        const result = await operation();
        onSuccess?.(result);
        return result;
      });

      render(<LoginScreen />);
      
      const buttons = screen.getAllByRole('button');
      const googleButton = buttons.find(button => 
        button.props.children === '' && // Google button has empty title
        button.props.variant === 'outline'
      );
      
      expect(googleButton).toBeTruthy();
      fireEvent.press(googleButton!);
      
      await waitFor(() => {
        expect(signInWithGoogleNative).toHaveBeenCalled();
      });
    });

    it('should handle Google sign-in cancellation gracefully', async () => {
      (signInWithGoogleNative as jest.Mock).mockResolvedValue({
        success: false,
        message: 'cancelled',
      });

      mockErrorHandler.executeWithErrorHandling.mockImplementation(async ({ operation, onSuccess }) => {
        const result = await operation();
        if (result) {
          onSuccess?.(result);
        }
        return result;
      });

      render(<LoginScreen />);
      
      const buttons = screen.getAllByRole('button');
      const googleButton = buttons.find(button => 
        button.props.children === '' && 
        button.props.variant === 'outline'
      );
      
      fireEvent.press(googleButton!);
      
      await waitFor(() => {
        expect(signInWithGoogleNative).toHaveBeenCalled();
        // Should not show error toast for cancellation
        expect(mockToast.error).not.toHaveBeenCalled();
      });
    });

    it('should redirect after successful Google login', async () => {
      mockErrorHandler.executeWithErrorHandling.mockImplementation(async ({ operation, onSuccess }) => {
        const result = await operation();
        onSuccess?.(result);
        return result;
      });

      render(<LoginScreen />);
      
      const buttons = screen.getAllByRole('button');
      const googleButton = buttons.find(button => 
        button.props.children === '' && 
        button.props.variant === 'outline'
      );
      
      fireEvent.press(googleButton!);
      
      await waitFor(() => {
        expect(mockCheckUserRole).toHaveBeenCalled();
        expect(router.replace).toHaveBeenCalledWith('/(dashboard)/customer');
      });
    });
  });

  describe('Mobile Password Login', () => {
    beforeEach(() => {
      render(<LoginScreen />);
      
      // Open mobile modal
      const buttons = screen.getAllByRole('button');
      const mobileButton = buttons.find(button => 
        button.props.children === '' && 
        button.props.variant === 'outline' &&
        !button.props.children?.includes?.('G') // Not the Google button
      );
      
      fireEvent.press(mobileButton!);
    });

    it('should open mobile password modal', () => {
      expect(screen.getByText('Login with Mobile')).toBeTruthy();
      expect(screen.getByText('Mobile Number')).toBeTruthy();
      expect(screen.getByText('Password')).toBeTruthy();
    });

    it('should handle mobile input formatting', () => {
      const mobileInput = screen.getByPlaceholderText('9876543210');
      
      fireEvent.changeText(mobileInput, '9876543210');
      
      expect(mobileInput.props.value).toBe('9876543210');
    });

    it('should validate mobile number format', async () => {
      mockErrorHandler.executeWithErrorHandling.mockImplementation(async ({ operation }) => {
        return await operation();
      });

      const mobileInput = screen.getByPlaceholderText('9876543210');
      const passwordInput = screen.getByPlaceholderText('••••••••');
      const signInButton = screen.getByText('Sign In');
      
      fireEvent.changeText(mobileInput, '123'); // Invalid mobile
      fireEvent.changeText(passwordInput, 'password');
      fireEvent.press(signInButton);
      
      // Should not call the service due to validation error
      expect(MobileAuthService.signInWithMobilePassword).not.toHaveBeenCalled();
    });

    it('should call mobile auth service with valid credentials', async () => {
      mockErrorHandler.executeWithErrorHandling.mockImplementation(async ({ operation, onSuccess }) => {
        const result = await operation();
        onSuccess?.(result);
        return result;
      });

      const mobileInput = screen.getByPlaceholderText('9876543210');
      const passwordInput = screen.getByPlaceholderText('••••••••');
      const signInButton = screen.getByText('Sign In');
      
      fireEvent.changeText(mobileInput, '9876543210');
      fireEvent.changeText(passwordInput, 'password123');
      fireEvent.press(signInButton);
      
      await waitFor(() => {
        expect(MobileAuthService.signInWithMobilePassword).toHaveBeenCalledWith(
          '9876543210',
          'password123'
        );
      });
    });

    it('should close modal when close button is pressed', () => {
      const closeButton = screen.getByTestId('XCircle-icon').parent;
      
      fireEvent.press(closeButton!);
      
      expect(screen.queryByText('Login with Mobile')).toBeFalsy();
    });
  });

  describe('Navigation and Role Handling', () => {
    it('should navigate to choose-role when user needs role selection', async () => {
      mockCheckUserRole.mockResolvedValue({
        needsRoleSelection: true,
        needsOnboarding: false,
        role: null,
      });

      mockErrorHandler.executeWithErrorHandling.mockImplementation(async ({ operation, onSuccess }) => {
        const result = await operation();
        onSuccess?.(result);
        return result;
      });

      render(<LoginScreen />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email address');
      const continueButton = screen.getByText('Continue');
      
      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.press(continueButton);
      
      await waitFor(() => {
        expect(screen.getByText('Verify Your Email')).toBeTruthy();
      });

      const otpInput = screen.getByTestId('otp-input');
      fireEvent.changeText(otpInput, '123456');
      
      await waitFor(() => {
        expect(router.replace).toHaveBeenCalledWith('/(auth)/choose-role');
      });
    });

    it('should navigate to onboarding when user needs onboarding', async () => {
      mockCheckUserRole.mockResolvedValue({
        needsRoleSelection: false,
        needsOnboarding: true,
        role: 'business',
      });

      mockErrorHandler.executeWithErrorHandling.mockImplementation(async ({ operation, onSuccess }) => {
        const result = await operation();
        onSuccess?.(result);
        return result;
      });

      render(<LoginScreen />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email address');
      const continueButton = screen.getByText('Continue');
      
      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.press(continueButton);
      
      await waitFor(() => {
        expect(screen.getByText('Verify Your Email')).toBeTruthy();
      });

      const otpInput = screen.getByTestId('otp-input');
      fireEvent.changeText(otpInput, '123456');
      
      await waitFor(() => {
        expect(router.replace).toHaveBeenCalledWith('/(onboarding)/business-details');
      });
    });

    it('should navigate to business dashboard for business users', async () => {
      mockCheckUserRole.mockResolvedValue({
        needsRoleSelection: false,
        needsOnboarding: false,
        role: 'business',
      });

      mockErrorHandler.executeWithErrorHandling.mockImplementation(async ({ operation, onSuccess }) => {
        const result = await operation();
        onSuccess?.(result);
        return result;
      });

      render(<LoginScreen />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email address');
      const continueButton = screen.getByText('Continue');
      
      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.press(continueButton);
      
      await waitFor(() => {
        expect(screen.getByText('Verify Your Email')).toBeTruthy();
      });

      const otpInput = screen.getByTestId('otp-input');
      fireEvent.changeText(otpInput, '123456');
      
      await waitFor(() => {
        expect(router.replace).toHaveBeenCalledWith('/(dashboard)/business');
      });
    });

    it('should handle checkUserRole error gracefully', async () => {
      mockCheckUserRole.mockRejectedValue(new Error('Role check failed'));

      mockErrorHandler.executeWithErrorHandling.mockImplementation(async ({ operation, onSuccess }) => {
        const result = await operation();
        onSuccess?.(result);
        return result;
      });

      render(<LoginScreen />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email address');
      const continueButton = screen.getByText('Continue');
      
      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.press(continueButton);
      
      await waitFor(() => {
        expect(screen.getByText('Verify Your Email')).toBeTruthy();
      });

      const otpInput = screen.getByTestId('otp-input');
      fireEvent.changeText(otpInput, '123456');
      
      await waitFor(() => {
        expect(router.replace).toHaveBeenCalledWith('/');
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle email sending errors', async () => {
      (sendEmailOTP as jest.Mock).mockResolvedValue({
        success: false,
        message: 'Failed to send email',
      });

      mockErrorHandler.executeWithErrorHandling.mockImplementation(async ({ operation, onError }) => {
        try {
          await operation();
        } catch (error) {
          onError?.(error as any);
        }
        return null;
      });

      render(<LoginScreen />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email address');
      const continueButton = screen.getByText('Continue');
      
      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.press(continueButton);
      
      await waitFor(() => {
        expect(sendEmailOTP).toHaveBeenCalled();
      });
    });

    it('should handle OTP verification errors', async () => {
      mockErrorHandler.executeWithErrorHandling.mockImplementation(async ({ operation, onSuccess }) => {
        const result = await operation();
        onSuccess?.(result);
        return result;
      });

      render(<LoginScreen />);
      
      // Navigate to OTP step
      const emailInput = screen.getByPlaceholderText('Enter your email address');
      const continueButton = screen.getByText('Continue');
      
      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.press(continueButton);
      
      await waitFor(() => {
        expect(screen.getByText('Verify Your Email')).toBeTruthy();
      });

      // Mock OTP verification failure
      (verifyEmailOTP as jest.Mock).mockResolvedValue({
        success: false,
        message: 'Invalid OTP',
      });

      mockErrorHandler.executeWithErrorHandling.mockImplementation(async ({ operation, onError }) => {
        try {
          await operation();
        } catch (error) {
          onError?.(error as any);
        }
        return null;
      });

      const otpInput = screen.getByTestId('otp-input');
      fireEvent.changeText(otpInput, '123456');
      
      await waitFor(() => {
        expect(verifyEmailOTP).toHaveBeenCalled();
      });
    });

    it('should handle mobile login errors', async () => {
      (MobileAuthService.signInWithMobilePassword as jest.Mock).mockResolvedValue({
        data: null,
        error: { message: 'Invalid credentials' },
      });

      mockErrorHandler.executeWithErrorHandling.mockImplementation(async ({ operation, onError }) => {
        try {
          await operation();
        } catch (error) {
          onError?.(error as any);
        }
        return null;
      });

      render(<LoginScreen />);
      
      // Open mobile modal
      const buttons = screen.getAllByRole('button');
      const mobileButton = buttons.find(button => 
        button.props.children === '' && 
        button.props.variant === 'outline' &&
        !button.props.children?.includes?.('G')
      );
      
      fireEvent.press(mobileButton!);
      
      const mobileInput = screen.getByPlaceholderText('9876543210');
      const passwordInput = screen.getByPlaceholderText('••••••••');
      const signInButton = screen.getByText('Sign In');
      
      fireEvent.changeText(mobileInput, '9876543210');
      fireEvent.changeText(passwordInput, 'wrongpassword');
      fireEvent.press(signInButton);
      
      await waitFor(() => {
        expect(MobileAuthService.signInWithMobilePassword).toHaveBeenCalled();
      });
    });
  });

  describe('Loading States', () => {
    it('should show loading state during email submission', async () => {
      let resolveEmailOTP: (value: any) => void;
      (sendEmailOTP as jest.Mock).mockImplementation(() => 
        new Promise(resolve => { resolveEmailOTP = resolve; })
      );

      mockErrorHandler.executeWithErrorHandling.mockImplementation(async ({ operation, onSuccess }) => {
        const result = await operation();
        onSuccess?.(result);
        return result;
      });

      render(<LoginScreen />);
      
      const emailInput = screen.getByPlaceholderText('Enter your email address');
      const continueButton = screen.getByText('Continue');
      
      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.press(continueButton);
      
      // Button should be disabled during loading
      expect(continueButton.props.disabled).toBeTruthy();
      
      // Resolve the promise
      await act(async () => {
        resolveEmailOTP!({ success: true });
      });
    });

    it('should show loading state during OTP verification', async () => {
      mockErrorHandler.executeWithErrorHandling.mockImplementation(async ({ operation, onSuccess }) => {
        const result = await operation();
        onSuccess?.(result);
        return result;
      });

      render(<LoginScreen />);
      
      // Navigate to OTP step first
      const emailInput = screen.getByPlaceholderText('Enter your email address');
      const continueButton = screen.getByText('Continue');
      
      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.press(continueButton);
      
      await waitFor(() => {
        expect(screen.getByText('Verify Your Email')).toBeTruthy();
      });

      // Mock slow OTP verification
      let resolveOTPVerify: (value: any) => void;
      (verifyEmailOTP as jest.Mock).mockImplementation(() => 
        new Promise(resolve => { resolveOTPVerify = resolve; })
      );

      const otpInput = screen.getByTestId('otp-input');
      fireEvent.changeText(otpInput, '123456');
      
      // Should show loading overlay
      expect(screen.getByTestId('activity-indicator')).toBeTruthy();
      
      // Resolve the promise
      await act(async () => {
        resolveOTPVerify!({ success: true });
      });
    });
  });
});