import { NextRequest, NextResponse } from 'next/server';
import { verifyAccessToken, extractBearerToken } from '@/lib/auth/jwt';
import { verifyHMACMiddleware } from '@/lib/middleware/hmac';
import { createServiceRoleClient } from '@/utils/supabase/service-role';
import { 
  bruteForceProtectionMiddleware, 
  getClientIP 
} from '@/lib/middleware/bruteForceProtection';

/**
 * Security middleware wrapper for business API routes
 * For public routes like slug lookup, HMAC might be optional
 */
async function applySecurityMiddleware(req: NextRequest, requireHMAC: boolean = false) {
  // 1. Apply rate limiting and brute force protection
  const ipAddress = getClientIP(req);
  const bruteForceCheck = await bruteForceProtectionMiddleware(req, {
    operation: 'business_api',
    ipAddress,
  });

  if (bruteForceCheck) {
    return bruteForceCheck;
  }

  // 2. For public routes, JWT might be optional
  const token = extractBearerToken(req);
  let jwtPayload = null;
  
  if (token) {
    jwtPayload = verifyAccessToken(token);
    if (!jwtPayload) {
      return new NextResponse(JSON.stringify({ error: 'Invalid or expired token' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }
  }

  // 3. Verify HMAC signature (if required)
  if (requireHMAC) {
    const hmacResult = await verifyHMACMiddleware(req, true);
    if (!hmacResult.success) {
      return new NextResponse(JSON.stringify({ error: hmacResult.error }), {
        status: hmacResult.status || 403,
        headers: { 'Content-Type': 'application/json' },
      });
    }
  }

  return { jwtPayload };
}

/**
 * GET /api/business/slug/[slug] - Get a business profile by slug
 * This endpoint supports both authenticated and public access
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;

    // Apply security middleware (HMAC not required for public access)
    const securityResult = await applySecurityMiddleware(req, false);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { jwtPayload } = securityResult;

    // Validate slug format
    if (!slug || typeof slug !== 'string') {
      return new NextResponse(JSON.stringify({ error: 'Invalid business slug' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const supabase = createServiceRoleClient();

    // Parse query parameters for additional data
    const url = new URL(req.url);
    const includeProducts = url.searchParams.get('include_products') === 'true';
    const includeGallery = url.searchParams.get('include_gallery') === 'true';

    // Build the select query based on what's requested
    let selectQuery = `
      id, business_name, business_slug, contact_email, member_name, title,
      business_category, phone, whatsapp_number, address_line, city, state,
      pincode, locality, about_bio, status, logo_url, instagram_url,
      facebook_url, established_year, delivery_info, business_hours,
      latitude, longitude, total_likes, total_subscriptions, average_rating,
      created_at, updated_at
    `;

    // Add products if requested
    if (includeProducts) {
      selectQuery += `, products_services (
        id, name, description, base_price, discounted_price, is_available, 
        image_url, images, featured_image_index, product_type, slug, created_at, updated_at
      )`;
    }

    // Add gallery if requested
    if (includeGallery) {
      selectQuery += `, gallery`;
    }

    // Fetch business profile
    const { data: business, error } = await supabase
      .from('business_profiles')
      .select(selectQuery)
      .eq('business_slug', slug)
      .maybeSingle();

    if (error) {
      console.error('Error fetching business profile by slug:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to fetch business profile' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    if (!business) {
      return new NextResponse(JSON.stringify({ error: 'Business profile not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // For public access, filter out sensitive information if user is not the owner
    const isOwner = jwtPayload && jwtPayload.user_id === business.id;
    
    if (!isOwner) {
      // Remove sensitive fields for non-owners
      const { contact_email, ...publicBusiness } = business;
      return NextResponse.json({ business: publicBusiness });
    }

    return NextResponse.json({ business });

  } catch (error) {
    console.error('Unexpected error in GET /api/business/slug/[slug]:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
