{"permissions": {"allow": ["Bash(npx tsc:*)", "Bash(npm run lint)", "Bash(npm install zustand immer)", "Bash(npm install)", "Bash(npm install:*)", "Bash(npm test:*)", "Bash(rm:*)", "<PERSON><PERSON>(mkdir:*)", "mcp__supabase__list_projects", "mcp__supabase__list_tables", "mcp__sequential-thinking__sequentialthinking", "<PERSON><PERSON>(mv:*)", "Bash(find:*)", "Bash(ls:*)", "<PERSON><PERSON>(npx jest:*)", "Bash(node:*)", "Bash(npm run test:*)", "Bash(npm run -s --prefix dukancard tsc -- --noEmit)", "Bash(npm run -s --prefix dukancard lint)", "<PERSON><PERSON>(sed:*)", "mcp__supabase__execute_sql", "Bash(npm run -s tsc -- --noEmit)", "Bash(cp:*)", "<PERSON><PERSON>(cat:*)", "Bash(grep:*)", "<PERSON><PERSON>(head:*)", "<PERSON><PERSON>(tail:*)", "Bash(git checkout:*)"], "deny": []}}