import React from 'react';
import { render } from '@testing-library/react-native';
import CommentDisplay from '@/src/components/ui/CommentDisplay';
import type { PostCommentWithUser } from '@/lib/types/like-comment';

// Mock dependencies
jest.mock('@/src/stores/likeCommentStore', () => ({
  useLikeCommentStore: () => ({
    commentLikes: {},
    likingComments: new Set(),
    toggleCommentLike: jest.fn(),
    getCommentLikeStatus: jest.fn(),
    editComment: jest.fn(),
    deleteComment: jest.fn(),
  }),
}));

jest.mock('@/src/utils/formatNumber', () => ({
  formatIndianNumberShort: (num: number) => num.toString(),
}));

jest.mock('@/src/components/ui/CommentInput', () => {
  const React = require('react');
  return {
    __esModule: true,
    default: ({ placeholder }: any) => React.createElement('TextInput', { placeholder }),
  };
});

jest.mock('@/src/components/ui/Toast', () => ({
  useToast: () => ({
    showToast: jest.fn(),
  }),
}));

// Mock lucide-react-native icons
jest.mock('lucide-react-native', () => ({
  Heart: 'Heart',
  MessageCircle: 'MessageCircle',
  Pin: 'Pin',
  MoreHorizontal: 'MoreHorizontal',
  Edit: 'Edit',
  Trash2: 'Trash2',
  Flag: 'Flag',
}));

jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: {
      primary: '#007AFF',
      destructive: '#FF3B30',
      background: '#FFFFFF',
      text: '#000000',
      border: '#E5E5E7',
    },
  }),
}));

jest.mock('@/src/contexts/ThemeContext', () => ({
  useThemeContext: () => ({
    theme: {
      colors: {
        primary: '#007AFF',
        destructive: '#FF3B30',
        background: '#FFFFFF',
        text: '#000000',
        border: '#E5E5E7',
      },
    },
  }),
}));

// Mock comment data
const mockComment: PostCommentWithUser = {
  id: 'test-comment-id',
  user_id: 'test-user-id',
  post_id: 'test-post-id',
  post_source: 'business',
  parent_comment_id: null,
  content: 'Test comment content',
  is_pinned: false,
  is_edited: false,
  edited_at: null,
  created_at: '2023-01-01T00:00:00.000Z',
  updated_at: '2023-01-01T00:00:00.000Z',
  user_name: 'Test User',
  user_type: 'business',
  user_avatar: undefined,
  like_count: 5,
  is_liked_by_current_user: false,
};

describe('CommentDisplay', () => {
  const defaultProps = {
    comment: mockComment,
    postId: 'test-post-id',
    postSource: 'business' as const,
    currentUserId: 'current-user-id',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders comment content correctly', () => {
    const { getByText } = render(<CommentDisplay {...defaultProps} />);
    expect(getByText('Test comment content')).toBeTruthy();
    expect(getByText('Test User')).toBeTruthy();
  });

  it('displays business badge with amber theme colors', () => {
    const { getByText } = render(<CommentDisplay {...defaultProps} />);
    const badge = getByText('Business');
    expect(badge).toBeTruthy();
  });

  it('displays customer badge with green theme colors', () => {
    const customerComment = {
      ...mockComment,
      user_type: 'customer' as const,
    };
    const { getByText } = render(<CommentDisplay {...defaultProps} comment={customerComment} />);
    const badge = getByText('Customer');
    expect(badge).toBeTruthy();
  });

  it('shows user avatar with correct initials', () => {
    const { getByText } = render(<CommentDisplay {...defaultProps} />);
    expect(getByText('T')).toBeTruthy(); // First letter of "Test User"
  });

  it('displays like count when comment has likes', () => {
    const { getByText } = render(<CommentDisplay {...defaultProps} />);
    expect(getByText('5')).toBeTruthy();
  });

  it('uses amber theme colors for business user avatar', () => {
    const { getByText } = render(<CommentDisplay {...defaultProps} />);
    const avatarText = getByText('T');
    expect(avatarText).toBeTruthy();
    
    // The avatar should use amber background color (#f59e0b)
    const avatar = avatarText.parent;
    expect(avatar).toBeTruthy();
  });

  it('uses green theme colors for customer user avatar', () => {
    const customerComment = {
      ...mockComment,
      user_type: 'customer' as const,
    };
    const { getByText } = render(<CommentDisplay {...defaultProps} comment={customerComment} />);
    const avatarText = getByText('T');
    expect(avatarText).toBeTruthy();
    
    // The avatar should use green background color (#10b981)
    const avatar = avatarText.parent;
    expect(avatar).toBeTruthy();
  });

  it('displays pin icon with amber color when comment is pinned', () => {
    const pinnedComment = {
      ...mockComment,
      is_pinned: true,
    };
    const { getByText } = render(<CommentDisplay {...defaultProps} comment={pinnedComment} />);
    
    // Should show "Pinned" text
    expect(getByText('Pinned')).toBeTruthy();
  });

  it('formats like count correctly', () => {
    const { getByText } = render(<CommentDisplay {...defaultProps} />);
    expect(getByText('5')).toBeTruthy(); // Using mocked formatIndianNumberShort
  });

  it('shows edited indicator when comment is edited', () => {
    const editedComment = {
      ...mockComment,
      is_edited: true,
      edited_at: '2023-01-02T00:00:00.000Z',
    };
    const { getByText } = render(<CommentDisplay {...defaultProps} comment={editedComment} />);
    expect(getByText('(edited)')).toBeTruthy();
  });

  it('handles long comment content', () => {
    const longComment = {
      ...mockComment,
      content: 'This is a very long comment content that should be displayed properly in the mobile interface with proper text wrapping and formatting.',
    };
    const { getByText } = render(<CommentDisplay {...defaultProps} comment={longComment} />);
    expect(getByText(longComment.content)).toBeTruthy();
  });
});