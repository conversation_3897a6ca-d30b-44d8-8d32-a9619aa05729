### User Story 6.1: Implement Structured JSON Logging

*   **User Story:** As a developer, I want all API requests and key events to be logged in a structured JSON format with a unique correlation ID, so that we can easily search, filter, and analyze logs in a centralized logging platform (like Datadog, Sentry, etc.).
*   **Acceptance Criteria:**
    *   Any request to an API endpoint generates a structured JSON log containing request details and a unique `correlationId`.
    *   All log entries related to a single request share the same `correlationId`.
    *   Errors within API routes automatically generate structured error logs.

---

### Development Tasks

-   [ ] **1. Install Logging Libraries**
    *   **Task:** Add `pino` and `pino-http` to the `dukancard` project.
    *   **Command:** `npm install pino pino-http`

-   [ ] **2. Configure Centralized Logger Instance**
    *   **Task:** Create a centralized logger instance that can be imported and used throughout the application.
    *   **File Path:** `dukancard/lib/logger.ts`
    *   **Implementation:**
        *   Initialize `pino` with configurations for production (JSON output) and development (pretty-printed output).
        *   Export the logger instance.

    ```typescript
    // dukancard/lib/logger.ts
    import pino from 'pino';

    const logger = pino({
        level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
        transport: {
            target: process.env.NODE_ENV === 'development' ? 'pino-pretty' : undefined,
            options: {
                colorize: process.env.NODE_ENV === 'development',
            },
        },
    });

    export default logger;
    ```

-   [ ] **3. Integrate `pino-http` into Next.js API Middleware**
    *   **Task:** Use `pino-http` to automatically log incoming requests and outgoing responses, and to inject the logger instance with a `correlationId` into the request context.
    *   **File Path:** `dukancard/middleware.ts` (or where your main API middleware is handled).
    *   **Implementation:**
        *   Import `pino-http` and your `logger` instance.
        *   Create a `pinoHttp` instance and use it as a middleware.
        *   Ensure `req.id` (the correlation ID) is generated and passed to the logger context.

    ```typescript
    // dukancard/middleware.ts (simplified example)
    import { NextRequest, NextResponse } from 'next/server';
    import pinoHttp from 'pino-http';
    import logger from '@/lib/logger';

    const pinoMiddleware = pinoHttp({
        logger: logger,
        genReqId: (req, res) => {
            const existingId = req.headers['x-request-id'];
            if (existingId) return existingId as string;
            const id = crypto.randomUUID();
            res.setHeader('x-request-id', id);
            return id;
        },
        customProps: (req, res) => ({
            correlationId: res.getHeader('x-request-id'),
        }),
        // ... other pino-http options
    });

    export function middleware(req: NextRequest) {
        // Wrap the request with pino-http
        const response = pinoMiddleware(req, new NextResponse());
        // ... rest of your middleware logic
        return response;
    }
    ```

-   [ ] **4. Implement Custom Logging Utility**
    *   **Task:** Ensure developers can easily log custom messages within API routes and other server-side logic, with the `correlationId` automatically attached.
    *   **Action:** The `pino-http` setup should make the logger available on the `req` object (e.g., `req.log`). Developers should use `req.log.info(...)` for contextual logging.

-   [ ] **5. Write Integration Tests for Logging**
    *   **Task:** Add tests to verify the logging setup.
    *   **File Path:** `dukancard/__tests__/middleware.test.ts` (or a dedicated logging test file).
    *   **Test Cases:**
        *   **Test 1: Structured Output:** Mock a request and assert that the logger outputs a valid JSON string (in a production-like environment mock).
        *   **Test 2: Correlation ID Consistency:** Make a request and assert that all log lines generated for that request (including custom ones) contain the same `correlationId`.
        *   **Test 3: Error Logging:** Simulate an error in an API route and assert that a structured error log is generated with the correct level and stack trace.