### User Story 6.6: Build Role-Based Admin Portal Foundation

*   **User Story:** As an administrator, I want to log in to a secure, extensible admin portal to access special administrative tools, so that I can manage the application and respond to security incidents.
*   **Acceptance Criteria:**
    *   A user with the `'admin'` role can successfully navigate to `/admin/security/devices` and use the UI to revoke a user's device.
    *   Normal users are prevented from accessing any `/admin/*` pages or `/api/admin/*` routes.

---

### Development Tasks

-   [ ] **1. Update JWT Generation to Include Roles**
    *   **Task:** Modify the `generateAccessToken` function in `dukancard/lib/auth/jwt.ts` (from Story 2.2).
    *   **Action:** Ensure that the user's roles, sourced from their `raw_user_meta_data` (e.g., `user.raw_user_meta_data.roles || []`), are included in the JWT payload. This is crucial for role-based access control.

-   [ ] **2. Implement Admin Role Middleware**
    *   **Task:** Create a reusable middleware function that checks if the authenticated user has the `'admin'` role.
    *   **File Path:** `dukancard/lib/middleware/admin-auth.ts`
    *   **Logic:** This middleware should extract the user's roles from the JWT payload (after it has been verified). If the `'admin'` role is not present, it should return a `403 Forbidden` response. This middleware should be designed to be extensible for future, more granular roles.

-   [ ] **3. Protect Admin API Routes**
    *   **Task:** Apply the `admin-auth` middleware to all API routes under `/api/admin/*`.
    *   **Action:** Specifically, ensure the `POST /api/admin/security/devices/revoke` endpoint (from Story 6.4) is protected by this middleware.

-   [ ] **4. Create Admin Portal Layout and Routing**
    *   **Task:** Establish the foundational UI for the admin portal.
    *   **Action:**
        *   Create a main layout component for the admin portal at `dukancard/app/admin/layout.tsx`.
        *   Create the first module page at `dukancard/app/admin/security/devices/page.tsx`.

-   [ ] **5. Protect Admin UI Routes**
    *   **Task:** Implement protection for all `/admin/*` pages to ensure only authenticated admins can access them.
    *   **Action:** This can be done in `dukancard/middleware.ts` (by checking the request path and user role) or within the `admin/layout.tsx` component (by redirecting non-admin users).

-   [ ] **6. Implement Admin Device Management UI**
    *   **Task:** Build the user interface for the device management module within the admin portal.
    *   **Action:** In `dukancard/app/admin/security/devices/page.tsx`, create a UI that allows an admin to input a `userId` or `deviceId` and trigger the device revocation API (from Story 6.4).

-   [ ] **7. Write Integration Tests**
    *   **Task:** Add tests to verify the admin role protection and UI functionality.
    *   **File Paths:**
        *   `dukancard/__tests__/lib/middleware/admin-auth.test.ts`
        *   `dukancard/__tests__/app/admin/security/devices/page.test.ts` (for UI tests)
    *   **Test Cases:**
        *   **Test 1: Admin Access:** Mock an admin user. Assert they can access `/admin/security/devices` and call the admin API.
        *   **Test 2: Non-Admin Blocked (API):** Mock a non-admin user. Assert calls to `/api/admin/*` are rejected with `403`.
        *   **Test 3: Non-Admin Blocked (UI):** Mock a non-admin user. Assert attempts to access `/admin/*` pages result in redirection or a permission denied message.