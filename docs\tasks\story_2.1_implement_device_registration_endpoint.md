### User Story 2.1: Implement Device Registration Endpoint

**Description:** This story creates the API endpoint responsible for registering a new device for a user. It generates a unique secret for the device, which is fundamental to the HMAC request signing strategy.

**Acceptance Criteria:**
- A new API endpoint `POST /api/devices/register` is created.
- The endpoint is protected and requires a valid user JWT.
- It correctly generates a `deviceId` and `deviceSecret`, stores a hash of the secret, and returns the plaintext secret only once.

---

### Development Tasks

-   [ ] **1. Create API Route File Structure**
    *   In the `dukancard` project, create the new API route file:
    *   **File Path:** `dukancard/app/api/devices/register/route.ts`

-   [ ] **2. Implement Request Body Validation**
    *   Using the Zod library, define a schema that validates the shape of the incoming request body.
    *   The schema should enforce: `{ deviceName: string, platform: z.enum(['ios', 'android', 'web']), appSignatureHash: z.string().optional() }`.

-   [ ] **3. Implement the Route Handler Logic**
    *   Create and export an async `POST` function in the `route.ts` file.
    *   This handler must perform the following steps in order:

    ```typescript
    // dukancard/app/api/devices/register/route.ts (simplified logic)
    import { NextRequest, NextResponse } from 'next/server';
    import { z } from 'zod';
    import { hashSecret } from '@/lib/security/hashing';
    import { createServiceRoleClient } from '@/lib/supabase/service-role';
    import { getUserIdFromRequest } from '@/lib/auth/utils'; // Assume this utility exists
    import crypto from 'crypto';

    const registerSchema = z.object({ ... });

    export async function POST(req: NextRequest) {
        // 1. Authenticate the request (this will be handled by middleware later)
        const userId = await getUserIdFromRequest(req);
        if (!userId) {
            return new NextResponse('Unauthorized', { status: 401 });
        }

        // 2. Validate request body
        const body = await req.json();
        const validation = registerSchema.safeParse(body);
        if (!validation.success) {
            return new NextResponse(JSON.stringify({ errors: validation.error.issues }), { status: 400 });
        }
        const { deviceName, platform, appSignatureHash } = validation.data;

        // 3. Generate device secret
        const deviceSecret = crypto.randomBytes(32).toString('hex');

        // 4. Hash the secret for storage
        const deviceSecretHash = await hashSecret(deviceSecret);

        // 5. Insert into database
        const supabase = createServiceRoleClient();
        const { data, error } = await supabase
            .from('devices')
            .insert({
                user_id: userId,
                device_name: deviceName,
                platform: platform,
                device_secret_hash: deviceSecretHash,
                app_signature_hash: appSignatureHash,
            })
            .select('device_id')
            .single();

        if (error) {
            // Handle potential db errors
            return new NextResponse('Internal Server Error', { status: 500 });
        }

        // 6. Return the new ID and the PLAINTEXT secret
        return NextResponse.json({ deviceId: data.device_id, deviceSecret: deviceSecret });
    }
    ```

-   [ ] **4. Write Integration Tests**
    *   Create a new test file following the project convention.
    *   **File Path:** `dukancard/__tests__/app/api/devices/register/route.test.ts`
    *   **Test Cases:**
        *   **Test 1: Success Case:** Mock a valid authenticated user. Call the endpoint with a valid body. Assert that the response status is `200`. Assert the response body contains a `deviceId` and `deviceSecret`. Verify that the database `insert` method was called with the correct, hashed data.
        *   **Test 2: Unauthorized Case:** Mock an unauthenticated user. Call the endpoint. Assert that the response status is `401`.
        *   **Test 3: Validation Error Case:** Mock a valid user but call the endpoint with a malformed request body (e.g., missing `deviceName`). Assert that the response status is `400`.

-   [ ] **5. Run Tests**
    *   Run the project's test suite to confirm the new tests pass and that no existing tests have broken.
    *   **Command:** `npm test`