import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { verifyAccessToken, extractBearerToken } from '@/lib/auth/jwt';
import { hashSecret, compareSecret } from '@/lib/security/hashing';
import { createServiceRoleClient } from '@/utils/supabase/service-role';

const logoutSchema = z.object({
  refreshToken: z.string().min(1, "Refresh token is required"),
});

export async function POST(req: NextRequest) {
  try {
    // 1. Validate JWT authentication
    const authHeader = req.headers.get('Authorization');
    const token = extractBearerToken(authHeader);
    
    if (!token) {
      return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), { 
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const payload = verifyAccessToken(token);
    if (!payload) {
      return new NextResponse(JSON.stringify({ error: 'Invalid or expired token' }), { 
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const userId = payload.user_id;

    // 2. Validate request body
    const body = await req.json();
    const validation = logoutSchema.safeParse(body);
    if (!validation.success) {
      return new NextResponse(JSON.stringify({ 
        error: 'Validation failed',
        details: validation.error.issues 
      }), { 
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const { refreshToken } = validation.data;
    const supabase = createServiceRoleClient();

    // 3. Find and revoke the refresh token
    // First, get all non-revoked refresh tokens for this user
    const { data: refreshTokens, error: refreshTokensError } = await supabase
      .from('refresh_tokens')
      .select('token_id, token_hash')
      .eq('user_id', userId)
      .eq('revoked', false);

    if (refreshTokensError) {
      console.error('Error fetching refresh tokens during logout:', refreshTokensError);
      return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 4. Find matching token by comparing hashes
    let matchingTokenId: string | null = null;
    if (refreshTokens) {
      for (const tokenRecord of refreshTokens) {
        const isMatch = await compareSecret(refreshToken, tokenRecord.token_hash);
        if (isMatch) {
          matchingTokenId = tokenRecord.token_id;
          break;
        }
      }
    }

    // 5. Revoke the token if found
    // Note: We return 200 OK even if token is not found to prevent information leakage
    if (matchingTokenId) {
      const { error: revokeError } = await supabase
        .from('refresh_tokens')
        .update({ 
          revoked: true, 
          revoked_at: new Date().toISOString() 
        })
        .eq('token_id', matchingTokenId);

      if (revokeError) {
        console.error('Error revoking refresh token during logout:', revokeError);
        return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), { 
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }

    // 6. Always return success to prevent information leakage
    return NextResponse.json({ 
      message: 'Logout successful' 
    });

  } catch (error) {
    console.error('Unexpected error during logout:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), { 
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}