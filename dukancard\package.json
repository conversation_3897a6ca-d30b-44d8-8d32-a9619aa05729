{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "dep-graph": "depcruise --include-only \"^app|^components|^lib\" --output-type json --output-to dependency-graph.json .", "gen-types": "npx supabase gen types typescript --schema public > types/supabase.ts"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^4.1.3", "@next/swc-win32-x64-msvc": "^15.3.5", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "@tabler/icons-react": "^3.31.0", "@tailwindcss/typography": "^0.5.16", "@testing-library/dom": "^10.4.0", "@types/jsonwebtoken": "^9.0.10", "@types/lodash": "^4.17.16", "@types/uuid": "^10.0.0", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.35.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cookie": "^1.0.2", "csrf-protection": "^0.2.0", "date-fns": "^3.6.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.9.2", "gsap": "^3.13.0", "highlight.js": "^11.11.1", "html5-qrcode": "^2.3.8", "immer": "^10.1.1", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lucide-react": "^0.483.0", "marked": "^15.0.12", "modern-screenshot": "^4.6.0", "next": "^15.2.4", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-easy-crop": "^5.4.1", "react-hook-form": "^7.54.2", "react-intersection-observer": "^9.16.0", "react-markdown": "^10.1.0", "react-qr-code": "^2.0.15", "recharts": "^2.15.3", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "sharp": "^0.33.5", "sonner": "^2.0.2", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "tsconfig-paths": "^4.2.0", "use-count-up": "^3.0.1", "uuid": "^11.1.0", "zod": "^3.24.2", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.54.0", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dependency-cruiser": "^16.10.4", "dotenv": "^16.5.0", "eslint": "^9", "eslint-config-next": "15.2.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "msw": "^2.10.4", "supabase": "^2.31.4", "tailwindcss": "^4", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "typescript": "^5", "vitest": "^3.2.4", "whatwg-fetch": "^3.6.20"}}