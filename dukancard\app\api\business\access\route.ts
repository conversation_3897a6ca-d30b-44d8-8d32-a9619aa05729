import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { verifyAccessToken, extractBearerToken } from '@/lib/auth/jwt';
import { verifyHMACMiddleware } from '@/lib/middleware/hmac';
import { createServiceRoleClient } from '@/utils/supabase/service-role';
import { 
  bruteForceProtectionMiddleware, 
  getClientIP 
} from '@/lib/middleware/bruteForceProtection';

// Validation schema for access check
const accessCheckSchema = z.object({
  business_profile_id: z.string().uuid("Invalid business profile ID"),
});

/**
 * Security middleware wrapper for business access API routes
 */
async function applySecurityMiddleware(req: NextRequest) {
  // 1. Apply rate limiting and brute force protection
  const ipAddress = getClientIP(req);
  const bruteForceCheck = await bruteForceProtectionMiddleware(req, {
    operation: 'business_access_check',
    ipAddress,
  });

  if (bruteForceCheck) {
    return bruteForceCheck;
  }

  // 2. Verify JWT token (required for access checks)
  const token = extractBearerToken(req);
  if (!token) {
    return new NextResponse(JSON.stringify({ error: 'Authorization token required' }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  const jwtPayload = verifyAccessToken(token);
  if (!jwtPayload) {
    return new NextResponse(JSON.stringify({ error: 'Invalid or expired token' }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  // 3. Verify HMAC signature
  const hmacResult = await verifyHMACMiddleware(req, true);
  if (!hmacResult.success) {
    return new NextResponse(JSON.stringify({ error: hmacResult.error }), {
      status: hmacResult.status || 403,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  return { jwtPayload };
}

/**
 * POST /api/business/access - Check if user has access to a business profile
 */
export async function POST(req: NextRequest) {
  try {
    // Apply security middleware
    const securityResult = await applySecurityMiddleware(req);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { jwtPayload } = securityResult;

    // Parse and validate request body
    const body = await req.json();
    const validation = accessCheckSchema.safeParse(body);
    
    if (!validation.success) {
      return new NextResponse(JSON.stringify({
        error: 'Invalid request body',
        details: validation.error.issues,
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const { business_profile_id } = validation.data;
    const supabase = createServiceRoleClient();

    // Check if the user owns the business profile
    const { data, error } = await supabase
      .from('business_profiles')
      .select('id')
      .eq('id', business_profile_id)
      .eq('id', jwtPayload.user_id) // Business profile ID should match user ID
      .maybeSingle();

    if (error) {
      console.error('Access check error:', error);
      return new NextResponse(JSON.stringify({ error: 'Error checking access' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return NextResponse.json({
      has_access: !!data,
    });

  } catch (error) {
    console.error('Unexpected error in POST /api/business/access:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

/**
 * GET /api/business/access/me - Get current user's business profile ID
 */
export async function GET(req: NextRequest) {
  try {
    // Apply security middleware
    const securityResult = await applySecurityMiddleware(req);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { jwtPayload } = securityResult;
    const supabase = createServiceRoleClient();

    // Check if the user has a business profile
    const { data, error } = await supabase
      .from('business_profiles')
      .select('id')
      .eq('id', jwtPayload.user_id)
      .maybeSingle();

    if (error) {
      console.error('Profile ID fetch error:', error);
      return new NextResponse(JSON.stringify({ error: 'Error fetching profile ID' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    if (!data) {
      return new NextResponse(JSON.stringify({ error: 'Business profile not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return NextResponse.json({
      profile_id: data.id,
    });

  } catch (error) {
    console.error('Unexpected error in GET /api/business/access/me:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
