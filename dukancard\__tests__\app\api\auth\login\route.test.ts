import { POST } from '@/app/api/auth/login/route';

// Mock the dependencies
jest.mock('@/lib/auth/jwt', () => ({
  generateAccessToken: jest.fn().mockReturnValue('mock-access-token'),
  generateRefreshToken: jest.fn().mockReturnValue('mock-refresh-token'),
}));

jest.mock('@/lib/security/hashing', () => ({
  hashSecret: jest.fn().mockResolvedValue('hashed-secret'),
}));

jest.mock('@/utils/supabase/service-role', () => ({
  createServiceRoleClient: jest.fn(),
}));

jest.mock('crypto', () => ({
  randomBytes: jest.fn(() => ({
    toString: jest.fn(() => 'generated-device-secret-hex'),
  })),
}));

import { generateAccessToken, generateRefreshToken } from '@/lib/auth/jwt';
import { hashSecret } from '@/lib/security/hashing';
import { createServiceRoleClient } from '@/utils/supabase/service-role';
import crypto from 'crypto';

describe('/api/auth/login', () => {
  let mockSupabase: any;
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock successful auth
    const mockAuthResult = {
      data: { user: { id: 'test-user-id' } },
      error: null,
    };

    // Setup mock Supabase client with properly chained methods
    const mockDeviceSingle = jest.fn();
    const mockDeviceSelect = jest.fn(() => ({ single: mockDeviceSingle }));
    const mockDeviceInsert = jest.fn(() => ({ select: mockDeviceSelect }));
    
    const mockRefreshTokenInsert = jest.fn();
    
    const mockFrom = jest.fn((tableName: string) => {
      if (tableName === 'devices') {
        return { insert: mockDeviceInsert };
      } else if (tableName === 'refresh_tokens') {
        return { insert: mockRefreshTokenInsert };
      }
    });
    
    const mockAuth = {
      signInWithPassword: jest.fn().mockResolvedValue(mockAuthResult),
    };
    
    mockSupabase = {
      from: mockFrom,
      auth: mockAuth,
    };
    
    // Store references for easy access in tests
    mockSupabase.mockFrom = mockFrom;
    mockSupabase.mockDeviceInsert = mockDeviceInsert;
    mockSupabase.mockDeviceSelect = mockDeviceSelect;
    mockSupabase.mockDeviceSingle = mockDeviceSingle;
    mockSupabase.mockRefreshTokenInsert = mockRefreshTokenInsert;
    mockSupabase.mockAuth = mockAuth;
    
    (createServiceRoleClient as jest.Mock).mockReturnValue(mockSupabase);
  });

  describe('POST', () => {
    it('should successfully login user and return tokens', async () => {
      // Mock successful device registration
      const mockDeviceId = 'test-device-id';
      const mockDeviceResult = {
        data: { device_id: mockDeviceId },
        error: null,
      };
      mockSupabase.mockDeviceSingle.mockResolvedValue(mockDeviceResult);
      
      // Mock successful refresh token storage
      const mockRefreshTokenResult = { error: null };
      mockSupabase.mockRefreshTokenInsert.mockResolvedValue(mockRefreshTokenResult);

      const request = {
        json: jest.fn().mockResolvedValue({
          email: '<EMAIL>',
          password: 'password123',
          deviceInfo: {
            deviceName: 'iPhone 15',
            platform: 'ios',
            appSignatureHash: 'test-signature-hash',
          },
        }),
      } as any;

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(200);
      expect(responseData.accessToken).toBe('mock-access-token');
      expect(responseData.refreshToken).toBe('mock-refresh-token');
      expect(responseData.deviceId).toBe(mockDeviceId);
      expect(responseData.deviceSecret).toBe('generated-device-secret-hex');
      
      // Verify authentication was attempted
      expect(mockSupabase.mockAuth.signInWithPassword).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      });
      
      // Verify device registration
      expect(mockSupabase.mockDeviceInsert).toHaveBeenCalledWith({
        user_id: 'test-user-id',
        device_name: 'iPhone 15',
        platform: 'ios',
        device_secret_hash: 'hashed-secret',
        app_signature_hash: 'test-signature-hash',
      });
      
      // Verify refresh token storage
      expect(mockSupabase.mockRefreshTokenInsert).toHaveBeenCalledWith(
        expect.objectContaining({
          user_id: 'test-user-id',
          device_id: mockDeviceId,
          token_hash: 'hashed-secret',
          expires_at: expect.any(String),
        })
      );
      
      // Verify token generation
      expect(generateAccessToken).toHaveBeenCalledWith('test-user-id', []);
      expect(generateRefreshToken).toHaveBeenCalled();
      
      // Verify secrets were hashed
      expect(hashSecret).toHaveBeenCalledWith('generated-device-secret-hex');
      expect(hashSecret).toHaveBeenCalledWith('mock-refresh-token');
    });

    it('should return 401 for invalid credentials', async () => {
      // Mock failed authentication
      mockSupabase.mockAuth.signInWithPassword.mockResolvedValue({
        data: { user: null },
        error: { message: 'Invalid credentials' },
      });

      const request = {
        json: jest.fn().mockResolvedValue({
          email: '<EMAIL>',
          password: 'wrongpassword',
          deviceInfo: {
            deviceName: 'iPhone 15',
            platform: 'ios',
          },
        }),
      } as any;

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(401);
      expect(responseData.error).toBe('Invalid credentials');
      
      // Verify no device registration occurred
      expect(mockSupabase.mockDeviceInsert).not.toHaveBeenCalled();
      expect(mockSupabase.mockRefreshTokenInsert).not.toHaveBeenCalled();
    });

    it('should return 400 for invalid request body', async () => {
      const request = {
        json: jest.fn().mockResolvedValue({
          email: 'invalid-email',
          password: '', // Empty password
          deviceInfo: {
            deviceName: '',
            platform: 'invalid-platform',
          },
        }),
      } as any;

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(400);
      expect(responseData.error).toBe('Validation failed');
      expect(responseData.details).toBeInstanceOf(Array);
      expect(responseData.details.length).toBeGreaterThan(0);
      
      // Verify no authentication was attempted
      expect(mockSupabase.mockAuth.signInWithPassword).not.toHaveBeenCalled();
    });

    it('should return 500 for device registration errors', async () => {
      // Mock successful auth but failed device registration
      mockSupabase.mockDeviceSingle.mockResolvedValue({
        data: null,
        error: { message: 'Device registration failed' },
      });

      const request = {
        json: jest.fn().mockResolvedValue({
          email: '<EMAIL>',
          password: 'password123',
          deviceInfo: {
            deviceName: 'iPhone 15',
            platform: 'ios',
          },
        }),
      } as any;

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(500);
      expect(responseData.error).toBe('Internal Server Error');
    });

    it('should return 500 for refresh token storage errors', async () => {
      // Mock successful device registration but failed refresh token storage
      mockSupabase.mockDeviceSingle.mockResolvedValue({
        data: { device_id: 'test-device-id' },
        error: null,
      });
      
      mockSupabase.mockRefreshTokenInsert.mockResolvedValue({
        error: { message: 'Refresh token storage failed' },
      });

      const request = {
        json: jest.fn().mockResolvedValue({
          email: '<EMAIL>',
          password: 'password123',
          deviceInfo: {
            deviceName: 'iPhone 15',
            platform: 'ios',
          },
        }),
      } as any;

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(500);
      expect(responseData.error).toBe('Internal Server Error');
    });

    it('should handle missing appSignatureHash correctly', async () => {
      // Mock successful device registration
      mockSupabase.mockDeviceSingle.mockResolvedValue({
        data: { device_id: 'test-device-id' },
        error: null,
      });
      
      // Mock successful refresh token storage
      mockSupabase.mockRefreshTokenInsert.mockResolvedValue({ error: null });

      const request = {
        json: jest.fn().mockResolvedValue({
          email: '<EMAIL>',
          password: 'password123',
          deviceInfo: {
            deviceName: 'Android Phone',
            platform: 'android',
            // No appSignatureHash provided
          },
        }),
      } as any;

      const response = await POST(request);

      expect(response.status).toBe(200);
      
      // Verify database was called with null for app_signature_hash
      expect(mockSupabase.mockDeviceInsert).toHaveBeenCalledWith({
        user_id: 'test-user-id',
        device_name: 'Android Phone',
        platform: 'android',
        device_secret_hash: 'hashed-secret',
        app_signature_hash: null,
      });
    });
  });
});