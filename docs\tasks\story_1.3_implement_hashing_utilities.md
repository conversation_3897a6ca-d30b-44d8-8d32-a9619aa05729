### User Story 1.3: Implement Server-Side Hashing Utilities

**Description:** This story involves creating a centralized, reusable module for securely hashing and comparing secrets. This is a critical component for handling device secrets and refresh tokens.

**Acceptance Criteria:**
- A dedicated module for hashing is created.
- The module uses a strong, industry-standard hashing algorithm.
- The functions are covered by unit tests.

---

### Development Tasks

-   [ ] **1. Install Hashing Library**
    *   We will use `bcryptjs` as it's a robust and widely-used library for hashing.
    *   In the `dukancard` project directory, run the following commands:
    *   **Command:** 
        ```bash
        npm install bcryptjs
        npm install -D @types/bcryptjs
        ```

-   [ ] **2. Create Hashing Utility Module**
    *   Create a new file at the following path: `dukancard/lib/security/hashing.ts`.
    *   Add the following functions to the file. This structure ensures we have a single place for all hashing logic.

    ```typescript
    // dukancard/lib/security/hashing.ts
    import bcrypt from 'bcryptjs';

    const SALT_ROUNDS = 10;

    /**
     * Hashes a plaintext secret.
     * @param secret The plaintext secret to hash.
     * @returns A promise that resolves to the hashed secret.
     */
    export async function hashSecret(secret: string): Promise<string> {
        return bcrypt.hash(secret, SALT_ROUNDS);
    }

    /**
     * Compares a plaintext secret against a hash.
     * @param secret The plaintext secret.
     * @param hash The hash to compare against.
     * @returns A promise that resolves to true if the secret matches the hash, false otherwise.
     */
    export async function compareSecret(secret: string, hash: string): Promise<boolean> {
        return bcrypt.compare(secret, hash);
    }
    ```

-   [ ] **3. Write Unit Tests for Hashing Utilities**
    *   Create a new test file that mirrors the path of the utility module, as per the project's testing convention.
    *   **File Path:** `dukancard/__tests__/lib/security/hashing.test.ts`
    *   Add the following tests to ensure the hashing functions work as expected.

    ```typescript
    // dukancard/__tests__/lib/security/hashing.test.ts
    import { hashSecret, compareSecret } from '../../../lib/security/hashing';

    describe('Security - Hashing Utilities', () => {

        it('should correctly hash a secret and verify it', async () => {
            const secret = 'mySuperSecretPassword123';
            const hashedSecret = await hashSecret(secret);

            // Ensure the hash is not the same as the secret
            expect(hashedSecret).not.toBe(secret);

            // Compare the original secret with the hash
            const isMatch = await compareSecret(secret, hashedSecret);
            expect(isMatch).toBe(true);
        });

        it('should fail to verify an incorrect secret', async () => {
            const secret = 'mySuperSecretPassword123';
            const wrongSecret = 'wrongPassword';
            const hashedSecret = await hashSecret(secret);

            // Compare the wrong secret with the hash
            const isMatch = await compareSecret(wrongSecret, hashedSecret);
            expect(isMatch).toBe(false);
        });

    });
    ```

-   [ ] **4. Run Tests**
    *   Run the project's test suite to confirm that the new unit tests for the hashing utility pass.
    *   **Command:** `npm test` (or the project's specific test command).