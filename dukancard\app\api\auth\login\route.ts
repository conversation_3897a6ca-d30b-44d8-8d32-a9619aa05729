import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import crypto from 'crypto';
import { generateAccessToken, generateRefreshToken } from '@/lib/auth/jwt';
import { hashSecret } from '@/lib/security/hashing';
import { createServiceRoleClient } from '@/utils/supabase/service-role';
import { 
  bruteForceProtectionMiddleware, 
  getClientIP 
} from '@/lib/middleware/bruteForceProtection';

const deviceInfoSchema = z.object({
  deviceName: z.string().min(1, "Device name is required"),
  platform: z.enum(['ios', 'android', 'web'], {
    errorMap: () => ({ message: "Platform must be 'ios', 'android', or 'web'" })
  }),
  appSignatureHash: z.string().optional(),
});

const loginSchema = z.object({
  email: z.string().email("Invalid email format"),
  password: z.string().min(1, "Password is required"),
  deviceInfo: deviceInfoSchema,
});

export async function POST(req: NextRequest) {
  try {
    // 1. Parse and validate request body first
    const body = await req.json();
    const validation = loginSchema.safeParse(body);
    if (!validation.success) {
      return new NextResponse(JSON.stringify({ 
        error: 'Validation failed',
        details: validation.error.issues 
      }), { 
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const { email, password, deviceInfo } = validation.data;

    // 2. Apply brute-force protection
    const ipAddress = getClientIP(req);

    const bruteForceCheck = await bruteForceProtectionMiddleware(req, {
      operation: 'login',
      email,
      ipAddress,
    });

    if (bruteForceCheck) {
      return bruteForceCheck; // Rate limited
    }

    // 3. Authenticate user with Supabase
    const supabase = createServiceRoleClient();
    
    // Use Supabase admin auth to validate credentials
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (authError || !authData.user) {
      return new NextResponse(JSON.stringify({ error: 'Invalid credentials' }), { 
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const userId = authData.user.id;

    // 4. Get user roles from the database (default to empty array for now)
    // TODO: Implement roles in Story 6.6
    const userRoles: string[] = [];

    // 5. Generate tokens
    const accessToken = generateAccessToken(userId, userRoles);
    const refreshToken = generateRefreshToken();

    // 6. Register device (using logic from Story 2.1)
    const deviceSecret = crypto.randomBytes(32).toString('hex');
    const hmacKey = crypto.randomBytes(32).toString('hex');
    const deviceSecretHash = await hashSecret(deviceSecret);

    const { data: deviceData, error: deviceError } = await supabase
      .from('devices')
      .insert({
        user_id: userId,
        device_name: deviceInfo.deviceName,
        platform: deviceInfo.platform,
        device_secret_hash: deviceSecretHash,
        hmac_key_hash: hmacKey,
        app_signature_hash: deviceInfo.appSignatureHash || null,
      })
      .select('device_id')
      .single();

    if (deviceError) {
      console.error('Device registration error during login:', deviceError);
      return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const deviceId = deviceData.device_id;

    // 7. Store refresh token in database
    const refreshTokenHash = await hashSecret(refreshToken);
    const refreshTokenExpiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days

    const { error: refreshTokenError } = await supabase
      .from('refresh_tokens')
      .insert({
        user_id: userId,
        device_id: deviceId,
        token_hash: refreshTokenHash,
        expires_at: refreshTokenExpiresAt.toISOString(),
      });

    if (refreshTokenError) {
      console.error('Refresh token storage error during login:', refreshTokenError);
      return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 8. Return tokens and device information
    return NextResponse.json({
      accessToken,
      refreshToken,
      deviceId,
      deviceSecret,
      hmacKey,
    });

  } catch (error) {
    console.error('Unexpected error in login:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), { 
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}