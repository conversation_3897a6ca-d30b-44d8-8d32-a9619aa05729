import { verifyHMACMiddleware } from '@/lib/middleware/hmac';
import { generateHMACSignature } from '@/lib/security/hmac';

// Mock the dependencies
jest.mock('@/utils/supabase/service-role', () => ({
  createServiceRoleClient: jest.fn(),
}));

import { createServiceRoleClient } from '@/utils/supabase/service-role';

describe('HMAC Middleware', () => {
  let mockSupabase: any;
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock current time for timestamp validation
    jest.spyOn(Date, 'now').mockReturnValue(1640995200000);
    
    // Create a mock Supabase client
    mockSupabase = {
      from: jest.fn(),
    };
    
    (createServiceRoleClient as jest.Mock).mockReturnValue(mockSupabase);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  const createMockRequest = (headers: Record<string, string>, body = '') => {
    const request = {
      method: 'POST',
      url: 'https://example.com/api/test',
      headers: {
        get: (name: string) => headers[name.toLowerCase()] || null,
      },
      clone: () => ({
        text: () => Promise.resolve(body),
      }),
    } as any;
    
    return request;
  };

  describe('verifyHMACMiddleware', () => {
    it('should skip verification when requireHMAC is false', async () => {
      const request = createMockRequest({});
      
      const result = await verifyHMACMiddleware(request, false);
      
      expect(result.success).toBe(true);
    });

    it('should return 400 for missing required headers', async () => {
      const request = createMockRequest({
        'x-device-id': 'device-123',
        // Missing x-timestamp and x-signature
      });
      
      const result = await verifyHMACMiddleware(request, true);
      
      expect(result.success).toBe(false);
      expect(result.status).toBe(400);
      expect(result.error).toBe('Missing required headers: X-Device-Id, X-Timestamp, or X-Signature');
    });

    it('should return 408 for expired timestamp', async () => {
      const expiredTimestamp = '1640995000000'; // 200 seconds ago (outside 120s window)
      
      const request = createMockRequest({
        'x-device-id': 'device-123',
        'x-timestamp': expiredTimestamp,
        'x-signature': 'test-signature',
      });
      
      const result = await verifyHMACMiddleware(request, true);
      
      expect(result.success).toBe(false);
      expect(result.status).toBe(408);
      expect(result.error).toBe('Request has expired');
    });

    it('should return 403 for invalid device ID', async () => {
      const currentTimestamp = '1640995200000'; // Current time
      
      // Mock device lookup failure
      mockSupabase.from.mockImplementation((table: string) => {
        if (table === 'devices') {
          return {
            select: () => ({
              eq: () => ({
                single: jest.fn().mockResolvedValue({
                  data: null,
                  error: { message: 'Device not found' },
                })
              })
            })
          };
        }
      });
      
      const request = createMockRequest({
        'x-device-id': 'invalid-device',
        'x-timestamp': currentTimestamp,
        'x-signature': 'test-signature',
      });
      
      const result = await verifyHMACMiddleware(request, true);
      
      expect(result.success).toBe(false);
      expect(result.status).toBe(403);
      expect(result.error).toBe('Invalid device ID');
    });

    it('should return 403 for revoked device', async () => {
      const currentTimestamp = '1640995200000';
      
      // Mock revoked device
      mockSupabase.from.mockImplementation((table: string) => {
        if (table === 'devices') {
          return {
            select: () => ({
              eq: () => ({
                single: jest.fn().mockResolvedValue({
                  data: {
                    device_id: 'device-123',
                    device_secret_hash: 'hashed-secret',
                    hmac_key_hash: 'hmac-key',
                    revoked: true,
                  },
                  error: null,
                })
              })
            })
          };
        }
      });
      
      const request = createMockRequest({
        'x-device-id': 'device-123',
        'x-timestamp': currentTimestamp,
        'x-signature': 'test-signature',
      });
      
      const result = await verifyHMACMiddleware(request, true);
      
      expect(result.success).toBe(false);
      expect(result.status).toBe(403);
      expect(result.error).toBe('Device has been revoked');
    });

    it('should return 403 for invalid HMAC signature', async () => {
      const currentTimestamp = '1640995200000';
      const deviceId = 'device-123';
      const hmacKey = 'test-hmac-key';
      const requestBody = '{"test": "data"}';
      
      // Mock valid device
      mockSupabase.from.mockImplementation((table: string) => {
        if (table === 'devices') {
          return {
            select: () => ({
              eq: () => ({
                single: jest.fn().mockResolvedValue({
                  data: {
                    device_id: deviceId,
                    device_secret_hash: 'hashed-secret',
                    hmac_key_hash: hmacKey,
                    revoked: false,
                  },
                  error: null,
                })
              })
            })
          };
        }
      });
      
      const request = createMockRequest({
        'x-device-id': deviceId,
        'x-timestamp': currentTimestamp,
        'x-signature': 'invalid-signature',
      }, requestBody);
      
      const result = await verifyHMACMiddleware(request, true);
      
      expect(result.success).toBe(false);
      expect(result.status).toBe(403);
      expect(result.error).toBe('Invalid signature');
    });

    it('should succeed with valid HMAC signature', async () => {
      const currentTimestamp = '1640995200000';
      const deviceId = 'device-123';
      const hmacKey = 'test-hmac-key';
      const requestBody = '{"test": "data"}';
      
      // Generate valid signature
      const validSignature = generateHMACSignature(
        'POST',
        '/api/test',
        currentTimestamp,
        requestBody,
        hmacKey
      );
      
      // Mock valid device
      mockSupabase.from.mockImplementation((table: string) => {
        if (table === 'devices') {
          return {
            select: () => ({
              eq: () => ({
                single: jest.fn().mockResolvedValue({
                  data: {
                    device_id: deviceId,
                    device_secret_hash: 'hashed-secret',
                    hmac_key_hash: hmacKey,
                    revoked: false,
                  },
                  error: null,
                })
              })
            })
          };
        }
      });
      
      const request = createMockRequest({
        'x-device-id': deviceId,
        'x-timestamp': currentTimestamp,
        'x-signature': validSignature,
      }, requestBody);
      
      const result = await verifyHMACMiddleware(request, true);
      
      expect(result.success).toBe(true);
      expect(result.deviceId).toBe(deviceId);
    });

    it('should handle empty request body', async () => {
      const currentTimestamp = '1640995200000';
      const deviceId = 'device-123';
      const hmacKey = 'test-hmac-key';
      const requestBody = '';
      
      // Generate valid signature for empty body
      const validSignature = generateHMACSignature(
        'GET',
        '/api/test',
        currentTimestamp,
        requestBody,
        hmacKey
      );
      
      // Mock valid device
      mockSupabase.from.mockImplementation((table: string) => {
        if (table === 'devices') {
          return {
            select: () => ({
              eq: () => ({
                single: jest.fn().mockResolvedValue({
                  data: {
                    device_id: deviceId,
                    device_secret_hash: 'hashed-secret',
                    hmac_key_hash: hmacKey,
                    revoked: false,
                  },
                  error: null,
                })
              })
            })
          };
        }
      });
      
      const request = {
        method: 'GET',
        url: 'https://example.com/api/test',
        headers: {
          get: (name: string) => {
            const headers: Record<string, string> = {
              'x-device-id': deviceId,
              'x-timestamp': currentTimestamp,
              'x-signature': validSignature,
            };
            return headers[name.toLowerCase()] || null;
          },
        },
        clone: () => ({
          text: () => Promise.resolve(''),
        }),
      } as any;
      
      const result = await verifyHMACMiddleware(request, true);
      
      expect(result.success).toBe(true);
      expect(result.deviceId).toBe(deviceId);
    });

    it('should return 500 for database errors', async () => {
      const currentTimestamp = '1640995200000';
      
      // Mock database error
      mockSupabase.from.mockImplementation((table: string) => {
        if (table === 'devices') {
          return {
            select: () => ({
              eq: () => ({
                single: jest.fn().mockResolvedValue({
                  data: null,
                  error: { message: 'Database connection failed' },
                })
              })
            })
          };
        }
      });
      
      const request = createMockRequest({
        'x-device-id': 'device-123',
        'x-timestamp': currentTimestamp,
        'x-signature': 'test-signature',
      });
      
      const result = await verifyHMACMiddleware(request, true);
      
      expect(result.success).toBe(false);
      expect(result.status).toBe(403); // Device not found is treated as 403
      expect(result.error).toBe('Invalid device ID');
    });

    it('should handle request body reading errors gracefully', async () => {
      const currentTimestamp = '1640995200000';
      const deviceId = 'device-123';
      const hmacKey = 'test-hmac-key';
      
      // Generate signature for empty body (fallback when body read fails)
      const validSignature = generateHMACSignature(
        'POST',
        '/api/test',
        currentTimestamp,
        '', // Empty body fallback
        hmacKey
      );
      
      // Mock valid device
      mockSupabase.from.mockImplementation((table: string) => {
        if (table === 'devices') {
          return {
            select: () => ({
              eq: () => ({
                single: jest.fn().mockResolvedValue({
                  data: {
                    device_id: deviceId,
                    device_secret_hash: 'hashed-secret',
                    hmac_key_hash: hmacKey,
                    revoked: false,
                  },
                  error: null,
                })
              })
            })
          };
        }
      });
      
      // Mock request with failing body read
      const request = {
        method: 'POST',
        url: 'https://example.com/api/test',
        headers: {
          get: (name: string) => {
            const headers: Record<string, string> = {
              'x-device-id': deviceId,
              'x-timestamp': currentTimestamp,
              'x-signature': validSignature,
            };
            return headers[name.toLowerCase()] || null;
          },
        },
        clone: () => ({
          text: () => Promise.reject(new Error('Failed to read body')),
        }),
      } as any;
      
      const result = await verifyHMACMiddleware(request, true);
      
      expect(result.success).toBe(true);
      expect(result.deviceId).toBe(deviceId);
    });
  });
});