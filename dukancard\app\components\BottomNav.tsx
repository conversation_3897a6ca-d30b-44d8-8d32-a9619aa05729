"use client";

import React from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";
import { useEffect, useState } from "react";
import { Home, Search, User, Store, QrCode } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import QRScannerModal from "@/components/qr/QRScannerModal";
import { createClient } from "@/utils/supabase/client";

interface BottomNavItemProps {
  href?: string;
  icon: React.ReactNode;
  label: string;
  isActive: boolean;
  isTablet?: boolean;
  badge?: string;
  disabled?: boolean;
  onClick?: () => void;
  isSpecial?: boolean;
}

const BottomNavItem = ({
  href,
  icon,
  label,
  isActive,
  isTablet = false,
  badge,
  disabled = false,
  onClick,
  isSpecial = false
}: BottomNavItemProps) => {
  const content = (
    <>
      <div className={cn(
        "relative mb-1 transition-all duration-200",
        isSpecial && "bg-[var(--brand-gold)] rounded-full p-3 -mt-2 shadow-lg"
      )}>
        <div className={cn(
          isSpecial && "text-white"
        )}>
          {icon}
        </div>
        {badge && (
          <Badge
            variant="outline"
            className="absolute -top-2 -right-3 px-1 py-0 text-[7px] bg-[var(--brand-gold)] text-[var(--brand-gold-foreground)] border-[var(--brand-gold)]"
          >
            {badge}
          </Badge>
        )}
      </div>
      <span className={cn(
        "transition-all",
        isTablet ? "text-[9px]" : "text-[10px]",
        isSpecial && "text-[var(--brand-gold)] font-medium"
      )}>{label}</span>
    </>
  );

  const itemClassName = cn(
    "flex flex-col items-center justify-center flex-1 py-2 text-xs transition-colors cursor-pointer",
    isActive
      ? "text-[var(--brand-gold)]"
      : "text-muted-foreground hover:text-[var(--brand-gold)]",
    disabled && "opacity-70 pointer-events-none",
    isSpecial && "transform hover:scale-105"
  );

  if (disabled) {
    return (
      <div className={itemClassName}>
        {content}
      </div>
    );
  }

  if (onClick) {
    return (
      <button onClick={onClick} className={itemClassName}>
        {content}
      </button>
    );
  }

  return (
    <Link href={href!} className={itemClassName}>
      {content}
    </Link>
  );
};

export default function BottomNav() {
  const pathname = usePathname();
  const router = useRouter();
  const isMobile = useIsMobile();
  const [isTablet, setIsTablet] = useState(false);
  const [isQRScannerOpen, setIsQRScannerOpen] = useState(false);
  const [_businessSlug, setBusinessSlug] = useState<string | null>(null);
  const [userType, setUserType] = useState<'business' | 'customer' | null>(null);

  useEffect(() => {
    if (typeof window === "undefined") return;

    // Check if device is a tablet (between 768px and 1024px)
    const checkTablet = () => {
      setIsTablet(window.innerWidth >= 768 && window.innerWidth < 1024);
    };

    // Initial check
    checkTablet();

    // Add event listener for resize
    window.addEventListener('resize', checkTablet);

    // Cleanup
    return () => window.removeEventListener('resize', checkTablet);
  }, []);

  // Fetch user type and business slug for proper navigation
  useEffect(() => {
    const fetchUserInfo = async () => {
      const supabase = createClient();
      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) return;

      try {
        // Check if user has business profile using API
        const response = await fetch('/api/business/me', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,
          },
        });

        if (response.ok) {
          const result = await response.json();
          if (result.business) {
            setUserType('business');
            setBusinessSlug(result.business.business_slug);
            return;
          }
        }

        // Check for customer profile (still using direct call as customer API not implemented yet)
        const { data: customerProfile } = await supabase
          .from('customer_profiles')
          .select('id')
          .eq('user_id', user.id)
          .single();

        if (customerProfile) {
          setUserType('customer');
        }
      } catch (error) {
        console.error('Error fetching user info:', error);
      }
    };

    fetchUserInfo();
  }, []);

  // QR Scanner handlers
  const handleQRScanPress = () => {
    setIsQRScannerOpen(true);
  };

  const handleQRScanSuccess = (businessSlug: string) => {
    setIsQRScannerOpen(false);
    // Navigate to business card page
    router.push(`/${businessSlug}`);
  };

  const handleQRScannerClose = () => {
    setIsQRScannerOpen(false);
  };

  // Don't render on desktop
  if (!isMobile && !isTablet) {
    return null;
  }

  // Determine navigation links based on current path and user context
  let accountLink = "/login";
  let accountIsActive = false;
  let homeLink = "/";
  let homeIsActive = false;

  // If user is in business dashboard
  if (pathname.startsWith("/dashboard/business")) {
    // Business users: Account button goes to their dashboard card page, Home goes to business feed
    accountLink = "/dashboard/business/card";
    accountIsActive = pathname === "/dashboard/business/card";
    homeLink = "/dashboard/business";
    homeIsActive = pathname === "/dashboard/business";
  }
  // If user is in customer dashboard
  else if (pathname.startsWith("/dashboard/customer")) {
    // Customer users: Account button goes to profile, Home goes to customer feed
    accountLink = "/dashboard/customer/profile";
    accountIsActive = pathname.includes("/dashboard/customer/profile");
    homeLink = "/dashboard/customer";
    homeIsActive = pathname === "/dashboard/customer";
  }
  // If user is in auth or onboarding flow
  else if (pathname.startsWith("/login") ||
           pathname.startsWith("/choose-role") || pathname.startsWith("/onboarding")) {
    accountLink = pathname; // Keep current page
    accountIsActive = true;
    homeLink = "/";
    homeIsActive = pathname === "/";
  }
  // For public pages, determine based on user type
  else {
    if (userType === 'business') {
      accountLink = "/dashboard/business/card";
      accountIsActive = false;
      homeLink = "/dashboard/business";
      homeIsActive = false;
    } else if (userType === 'customer') {
      accountLink = "/dashboard/customer/profile";
      accountIsActive = false;
      homeLink = "/dashboard/customer";
      homeIsActive = false;
    } else {
      accountLink = "/login";
      accountIsActive = pathname === "/login";
      homeLink = "/";
      homeIsActive = pathname === "/";
    }
  }

  // QR Scanner is always available (no authentication required for scanning)

  // Unified navigation items
  const navItems = [
    {
      key: "home",
      href: homeLink,
      icon: <Home size={20} />,
      label: "Home",
      isActive: homeIsActive
    },
    {
      key: "discover",
      href: "/discover",
      icon: <Search size={20} />,
      label: "Discover",
      isActive: pathname === "/discover"
    },
    {
      key: "scan",
      icon: <QrCode size={20} />,
      label: "Scan",
      isActive: false,
      onClick: handleQRScanPress,
      isSpecial: true
    },
    {
      key: "dukan-ai",
      href: "#",
      icon: <Store size={20} />,
      label: "Dukan AI",
      isActive: false,
      badge: "Soon",
      disabled: true
    },
    {
      key: "account",
      href: accountLink,
      icon: <User size={20} />,
      label: "Account",
      isActive: accountIsActive
    }
  ];

  return (
    <>
      <motion.div
        initial={{ y: 100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.3 }}
        className={cn(
          "fixed bottom-0 left-0 right-0 z-50 flex items-center justify-around bg-background/95 backdrop-blur-lg border-t border-border/80 px-2",
          isTablet ? "h-14" : "h-16"
        )}
      >
        {navItems.map((item) => (
          <BottomNavItem
            key={item.key}
            href={item.href}
            icon={item.icon}
            label={item.label}
            isActive={item.isActive}
            isTablet={isTablet}
            badge={item.badge}
            disabled={item.disabled}
            onClick={item.onClick}
            isSpecial={item.isSpecial}
          />
        ))}
      </motion.div>

      {/* QR Scanner Modal */}
      <QRScannerModal
        isOpen={isQRScannerOpen}
        onClose={handleQRScannerClose}
        onScanSuccess={handleQRScanSuccess}
      />
    </>
  );
}
