import React from 'react';
import { render, screen } from '@testing-library/react';
import AnimatedAuthCard from '@/app/(main)/components/auth/AnimatedAuthCard';

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, className, ...props }: any) => (
      <div className={className} {...props}>
        {children}
      </div>
    ),
  },
}));

describe('AnimatedAuthCard', () => {
  it('renders children correctly', () => {
    render(
      <AnimatedAuthCard>
        <div>Test content</div>
      </AnimatedAuthCard>
    );
    
    expect(screen.getByText('Test content')).toBeInTheDocument();
  });

  it('applies default styling classes to the card', () => {
    const { container } = render(
      <AnimatedAuthCard>
        <div>Test content</div>
      </AnimatedAuthCard>
    );
    
    const card = container.querySelector('[class*="bg-card"]');
    expect(card).toBeInTheDocument();
    expect(card).toHaveClass('border', 'border-border', 'p-4', 'sm:p-6', 'md:p-8');
  });

  it('applies custom className when provided', () => {
    const customClass = 'custom-test-class';
    const { container } = render(
      <AnimatedAuthCard className={customClass}>
        <div>Test content</div>
      </AnimatedAuthCard>
    );
    
    const card = container.querySelector('[class*="bg-card"]');
    expect(card).toHaveClass(customClass);
  });

  it('renders without custom className', () => {
    expect(() => {
      render(
        <AnimatedAuthCard>
          <div>Test content</div>
        </AnimatedAuthCard>
      );
    }).not.toThrow();
  });

  it('has correct card styling classes', () => {
    const { container } = render(
      <AnimatedAuthCard>
        <div>Test content</div>
      </AnimatedAuthCard>
    );
    
    const card = container.querySelector('[class*="bg-card"]');
    expect(card).toHaveClass(
      'rounded-xl',
      'sm:rounded-2xl',
      'shadow-lg',
      'relative',
      'overflow-hidden'
    );
  });

  it('renders the outer motion div with relative class', () => {
    const { container } = render(
      <AnimatedAuthCard>
        <div>Test content</div>
      </AnimatedAuthCard>
    );
    
    const outerDiv = container.firstChild as Element;
    expect(outerDiv).toHaveClass('relative');
  });

  it('handles multiple children correctly', () => {
    render(
      <AnimatedAuthCard>
        <div>First child</div>
        <div>Second child</div>
        <span>Third child</span>
      </AnimatedAuthCard>
    );
    
    expect(screen.getByText('First child')).toBeInTheDocument();
    expect(screen.getByText('Second child')).toBeInTheDocument();
    expect(screen.getByText('Third child')).toBeInTheDocument();
  });

  it('renders with complex children structure', () => {
    render(
      <AnimatedAuthCard>
        <div>
          <h1>Title</h1>
          <p>Description</p>
          <button>Action</button>
        </div>
      </AnimatedAuthCard>
    );
    
    expect(screen.getByRole('heading', { name: 'Title' })).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Action' })).toBeInTheDocument();
  });

  it('merges custom className with default classes', () => {
    const customClass = 'my-custom-class';
    const { container } = render(
      <AnimatedAuthCard className={customClass}>
        <div>Test content</div>
      </AnimatedAuthCard>
    );
    
    const card = container.querySelector('[class*="bg-card"]');
    expect(card).toHaveClass(customClass);
    expect(card).toHaveClass('bg-card', 'border', 'border-border');
  });

  it('preserves accessibility when rendering children', () => {
    render(
      <AnimatedAuthCard>
        <button aria-label="Close dialog">×</button>
        <input aria-describedby="help-text" />
        <div id="help-text">Help text</div>
      </AnimatedAuthCard>
    );
    
    expect(screen.getByLabelText('Close dialog')).toBeInTheDocument();
    expect(screen.getByRole('textbox')).toHaveAttribute('aria-describedby', 'help-text');
    expect(screen.getByText('Help text')).toBeInTheDocument();
  });
});