import React from 'react';
import { render, screen, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import AddProductClient from '@/app/(dashboard)/dashboard/business/products/add/AddProductClient';
import * as productActions from '@/app/(dashboard)/dashboard/business/products/actions';
import { addProductVariant } from '@/app/(dashboard)/dashboard/business/products/actions/addVariant';
import { toast } from 'sonner';

// Mock dependencies
const mockRouterPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockRouterPush,
  }),
  // Add other exports from next/navigation if needed
}));

jest.mock('sonner');

let mockOnSubmit: (values: any, imageFiles?: any[], featuredImageIndex?: number) => void;

jest.mock(
  '@/app/(dashboard)/dashboard/business/products/components/StandaloneProductForm',
  () => {
    return function DummyStandaloneProductForm(props: any) {
      mockOnSubmit = props.onSubmit;
      return (
        <form data-testid="standalone-product-form" onSubmit={(e) => e.preventDefault()}>
          <button type="button" onClick={() => props.onSubmit({ name: 'Test Submit' })}>
            Submit
          </button>
        </form>
      );
    };
  }
);

jest.mock('@/app/(dashboard)/dashboard/business/products/actions', () => ({
    ...jest.requireActual('@/app/(dashboard)/dashboard/business/products/actions'),
    addProductService: jest.fn(),
}));

jest.mock('@/app/(dashboard)/dashboard/business/products/actions/addVariant', () => ({
    addProductVariant: jest.fn(),
}));

describe('AddProductClient', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render the add product form with correct props', () => {
    render(
      <AddProductClient
        // Removed planLimit as products are now unlimited
        currentCount={5}
        currentAvailableCount={4}
      />
    );

    expect(screen.getByText('Add New Product')).toBeInTheDocument();
    expect(screen.getByTestId('standalone-product-form')).toBeInTheDocument();
    expect(screen.getByText('Available:')).toBeInTheDocument();
    expect(screen.getByText('4')).toBeInTheDocument();
    expect(screen.getByText('Unlimited')).toBeInTheDocument();
  });

  it('should call addProductService on form submission and redirect on success', async () => {
    const mockAddProductService = productActions.addProductService as jest.Mock;
    mockAddProductService.mockResolvedValue({ success: true, data: { id: 'new_prod_1' } });

    render(
      <AddProductClient
        // Removed planLimit as products are now unlimited
        currentCount={5}
        currentAvailableCount={4}
      />
    );

    const productData = {
      name: 'New Awesome Product',
      base_price: 199,
      product_type: 'physical',
    };

    await act(async () => {
        mockOnSubmit(productData);
    });

    expect(mockAddProductService).toHaveBeenCalled();
    expect(toast.success).toHaveBeenCalledWith('Product added successfully!');
    expect(mockRouterPush).toHaveBeenCalledWith('/dashboard/business/products');
  });

  it('should show an error toast if addProductService fails', async () => {
    const mockAddProductService = productActions.addProductService as jest.Mock;
    mockAddProductService.mockResolvedValue({ success: false, error: 'Database error' });

    render(
      <AddProductClient
        // Removed planLimit as products are now unlimited
        currentCount={5}
        currentAvailableCount={4}
      />
    );

    await act(async () => {
        mockOnSubmit({ name: 'Failed Product' });
    });

    expect(toast.error).toHaveBeenCalledWith('Failed to add product', {
      description: 'Database error',
    });
  });

  it('should handle product creation with variants', async () => {
    const mockAddProductService = productActions.addProductService as jest.Mock;
    const mockAddProductVariant = addProductVariant as jest.Mock;
    mockAddProductService.mockResolvedValue({ success: true, data: { id: 'new_prod_with_variants' } });
    mockAddProductVariant.mockResolvedValue({ success: true });

    render(
      <AddProductClient
        // Removed planLimit as products are now unlimited
        currentCount={5}
        currentAvailableCount={4}
      />
    );

    const productDataWithVariants = {
      name: 'Product With Variants',
      base_price: 500,
      variants: [
        { variant_name: 'Red', variant_values: { color: 'Red' }, base_price: 500, is_available: true },
        { variant_name: 'Blue', variant_values: { color: 'Blue' }, base_price: 510, is_available: true },
      ],
    };

    await act(async () => {
        mockOnSubmit(productDataWithVariants);
    });

    expect(mockAddProductService).toHaveBeenCalled();
    expect(toast.success).toHaveBeenCalledWith('Product created! Adding variants...');
    expect(mockAddProductVariant).toHaveBeenCalledTimes(2);
    expect(toast.success).toHaveBeenCalledWith('Product and 2 variants created successfully!');
    expect(mockRouterPush).toHaveBeenCalledWith('/dashboard/business/products');
  });
});