import {
  generateHMACSignature,
  verifyHMACSignature,
  validateTimestamp,
  extractHMACHeaders
} from '@/lib/security/hmac';

describe('HMAC Security Utilities', () => {
  describe('generateHMACSignature', () => {
    it('should generate consistent signatures for same inputs', () => {
      const method = 'POST';
      const path = '/api/auth/login';
      const timestamp = '1640995200000'; // Fixed timestamp
      const body = '{"email":"<EMAIL>","password":"test123"}';
      const deviceSecret = 'test-device-secret-key';

      const signature1 = generateHMACSignature(method, path, timestamp, body, deviceSecret);
      const signature2 = generateHMACSignature(method, path, timestamp, body, deviceSecret);

      expect(signature1).toBe(signature2);
      expect(signature1).toBeTruthy();
      expect(signature1.length).toBeGreaterThan(0);
    });

    it('should generate different signatures for different inputs', () => {
      const baseParams = {
        method: 'POST',
        path: '/api/auth/login',
        timestamp: '1640995200000',
        body: '{"email":"<EMAIL>"}',
        deviceSecret: 'test-secret'
      };

      const signature1 = generateHMACSignature(
        baseParams.method,
        baseParams.path,
        baseParams.timestamp,
        baseParams.body,
        baseParams.deviceSecret
      );

      // Different method
      const signature2 = generateHMACSignature(
        'GET',
        baseParams.path,
        baseParams.timestamp,
        baseParams.body,
        baseParams.deviceSecret
      );

      // Different path
      const signature3 = generateHMACSignature(
        baseParams.method,
        '/api/auth/logout',
        baseParams.timestamp,
        baseParams.body,
        baseParams.deviceSecret
      );

      // Different timestamp
      const signature4 = generateHMACSignature(
        baseParams.method,
        baseParams.path,
        '1640995300000',
        baseParams.body,
        baseParams.deviceSecret
      );

      // Different body
      const signature5 = generateHMACSignature(
        baseParams.method,
        baseParams.path,
        baseParams.timestamp,
        '{"email":"<EMAIL>"}',
        baseParams.deviceSecret
      );

      // Different secret
      const signature6 = generateHMACSignature(
        baseParams.method,
        baseParams.path,
        baseParams.timestamp,
        baseParams.body,
        'different-secret'
      );

      expect(signature1).not.toBe(signature2);
      expect(signature1).not.toBe(signature3);
      expect(signature1).not.toBe(signature4);
      expect(signature1).not.toBe(signature5);
      expect(signature1).not.toBe(signature6);
    });

    it('should handle empty request body', () => {
      const signature = generateHMACSignature(
        'GET',
        '/api/user/profile',
        '1640995200000',
        '',
        'test-secret'
      );

      expect(signature).toBeTruthy();
      expect(signature.length).toBeGreaterThan(0);
    });
  });

  describe('verifyHMACSignature', () => {
    it('should return true for matching signatures', () => {
      const signature = generateHMACSignature(
        'POST',
        '/api/test',
        '1640995200000',
        'test-body',
        'test-secret'
      );

      const isValid = verifyHMACSignature(signature, signature);
      expect(isValid).toBe(true);
    });

    it('should return false for different signatures', () => {
      const signature1 = generateHMACSignature(
        'POST',
        '/api/test',
        '1640995200000',
        'test-body',
        'secret1'
      );

      const signature2 = generateHMACSignature(
        'POST',
        '/api/test',
        '1640995200000',
        'test-body',
        'secret2'
      );

      const isValid = verifyHMACSignature(signature1, signature2);
      expect(isValid).toBe(false);
    });

    it('should return false for signatures of different lengths', () => {
      const signature1 = 'short';
      const signature2 = 'much-longer-signature';

      const isValid = verifyHMACSignature(signature1, signature2);
      expect(isValid).toBe(false);
    });

    it('should handle invalid base64 signatures gracefully', () => {
      const validSignature = generateHMACSignature(
        'POST',
        '/api/test',
        '1640995200000',
        'test',
        'secret'
      );
      const invalidSignature = 'not-valid-base64!@#$%';

      // This should not throw an error
      const isValid = verifyHMACSignature(invalidSignature, validSignature);
      expect(isValid).toBe(false);
    });
  });

  describe('validateTimestamp', () => {
    beforeAll(() => {
      // Mock Date.now() for consistent testing
      jest.spyOn(Date, 'now').mockImplementation(() => 1640995200000); // Fixed time
    });

    afterAll(() => {
      jest.restoreAllMocks();
    });

    it('should return true for current timestamp', () => {
      const currentTime = '1640995200000';
      const isValid = validateTimestamp(currentTime);
      expect(isValid).toBe(true);
    });

    it('should return true for timestamp within allowed window', () => {
      const recentTime = '1640995140000'; // 60 seconds ago
      const isValid = validateTimestamp(recentTime, 120); // 120 second window
      expect(isValid).toBe(true);
    });

    it('should return false for timestamp outside allowed window', () => {
      const oldTime = '1640995000000'; // 200 seconds ago
      const isValid = validateTimestamp(oldTime, 120); // 120 second window
      expect(isValid).toBe(false);
    });

    it('should return false for future timestamp outside window', () => {
      const futureTime = '1640995400000'; // 200 seconds in future
      const isValid = validateTimestamp(futureTime, 120); // 120 second window
      expect(isValid).toBe(false);
    });

    it('should return false for invalid timestamp', () => {
      const invalidTime = 'not-a-number';
      const isValid = validateTimestamp(invalidTime);
      expect(isValid).toBe(false);
    });

    it('should use default window of 120 seconds', () => {
      const recentTime = '1640995080000'; // 120 seconds ago
      const isValid = validateTimestamp(recentTime);
      expect(isValid).toBe(true);

      const oldTime = '1640995079000'; // 121 seconds ago
      const isValidOld = validateTimestamp(oldTime);
      expect(isValidOld).toBe(false);
    });
  });

  describe('extractHMACHeaders', () => {
    it('should extract headers from Headers object', () => {
      const headers = new Headers({
        'x-device-id': 'device-123',
        'x-timestamp': '1640995200000',
        'x-signature': 'test-signature'
      });

      const result = extractHMACHeaders(headers);
      expect(result).toEqual({
        deviceId: 'device-123',
        timestamp: '1640995200000',
        signature: 'test-signature'
      });
    });

    it('should extract headers from plain object', () => {
      const headers = {
        'x-device-id': 'device-456',
        'x-timestamp': '1640995300000',
        'x-signature': 'another-signature'
      };

      const result = extractHMACHeaders(headers);
      expect(result).toEqual({
        deviceId: 'device-456',
        timestamp: '1640995300000',
        signature: 'another-signature'
      });
    });

    it('should return null when required headers are missing', () => {
      const incompleteHeaders = {
        'x-device-id': 'device-123',
        'x-timestamp': '1640995200000'
        // Missing x-signature
      };

      const result = extractHMACHeaders(incompleteHeaders);
      expect(result).toBeNull();
    });

    it('should return null when headers object is empty', () => {
      const emptyHeaders = {};

      const result = extractHMACHeaders(emptyHeaders);
      expect(result).toBeNull();
    });

    it('should handle case-insensitive header names', () => {
      const headers = new Headers({
        'X-Device-Id': 'device-789', // Capital letters
        'X-Timestamp': '1640995400000',
        'X-Signature': 'case-test-signature'
      });

      const result = extractHMACHeaders(headers);
      expect(result).toEqual({
        deviceId: 'device-789',
        timestamp: '1640995400000',
        signature: 'case-test-signature'
      });
    });
  });
});