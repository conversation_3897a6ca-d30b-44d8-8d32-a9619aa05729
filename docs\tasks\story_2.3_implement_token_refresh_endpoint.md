### User Story 2.3: Implement Token Refresh Endpoint

**Description:** This story creates the endpoint responsible for session persistence. It allows a client to exchange a long-lived refresh token for a new, short-lived access token, without requiring the user to log in again. It implements token rotation and reuse detection for security.

**Acceptance Criteria:**
- A new API endpoint `POST /api/auth/refresh` is created.
- It successfully validates a device and its refresh token.
- It implements token rotation: the used refresh token is revoked and a new one is issued.
- It implements reuse detection: if a revoked token is used, the entire device session is invalidated.

---

### Development Tasks

-   [ ] **1. Create API Route File Structure**
    *   In the `dukancard` project, create the new API route file:
    *   **File Path:** `dukancard/app/api/auth/refresh/route.ts`

-   [ ] **2. Implement Request Body Validation**
    *   Using Zod, define a schema that validates the shape of the incoming request body.
    *   The schema should enforce: `{ refreshToken: z.string(), deviceId: z.string().uuid() }`.

-   [ ] **3. Implement the Refresh Route Handler**
    *   In `dukancard/app/api/auth/refresh/route.ts`, implement the `POST` handler.
    *   **Note:** This endpoint will eventually be protected by HMAC signature middleware (from Epic 3). For now, proceed with the core logic.
    *   **Logic Steps:**
        1.  **Validate Request Body:** Parse and validate the incoming payload.
        2.  **Find Existing Device and Token:**
            *   Use the service role Supabase client to query the `refresh_tokens` table.
            *   The query should find a token where `device_id` matches the one from the request body.
            *   The query cannot directly find the token by its hash. You must retrieve all non-revoked tokens for the device and then iterate through them, using the `compareSecret` utility to find a match.
        3.  **Handle Token Not Found:** If no matching, non-revoked token is found, return `401 Unauthorized`.
        4.  **Implement Reuse Detection (Security Critical):**
            *   If a token match is found, but its `revoked` flag is `true`, this is a token theft attempt.
            *   **Action:** Immediately update the associated `device` record, setting its `revoked` flag to `true`. This invalidates the entire device session.
            *   Return `409 Conflict` with an error message.
        5.  **Perform Token Rotation:**
            *   In a single database transaction, perform the following:
                a.  Update the just-used refresh token record, setting `revoked = true`.
                b.  Generate a new `accessToken` using the JWT utility.
                c.  Generate a new, secure `refreshToken` string.
                d.  Hash the new refresh token.
                e.  Insert a new record into the `refresh_tokens` table with the new hash, linked to the same `deviceId`.
        6.  **Return New Tokens:** Respond with `200 OK` and a body containing the new `{ accessToken, refreshToken }`.

-   [ ] **4. Write Integration Tests**
    *   Create a new test file.
    *   **File Path:** `dukancard/__tests__/app/api/auth/refresh/route.test.ts`
    *   **Test Cases:**
        *   **Test 1: Successful Refresh:** Seed the database with a valid device and a valid, non-revoked refresh token. Call the endpoint with the correct data. Assert a `200 OK` response containing a new access and refresh token. Assert that the original token is now marked `revoked` in the database and a new token record has been created.
        *   **Test 2: Invalid/Non-existent Token:** Call the endpoint with a refresh token that does not exist in the database. Assert a `401 Unauthorized` response.
        *   **Test 3: Token Reuse Detection:** Seed the database with a valid device and a refresh token that is already marked as `revoked`. Call the endpoint with this revoked token. Assert a `409 Conflict` response. Assert that the associated `device` record in the database is now also marked as `revoked`.