import { POST } from '@/app/api/devices/register/route';

// Mock the dependencies
jest.mock('@/lib/security/hashing', () => ({
  hashSecret: jest.fn().mockResolvedValue('hashed-secret'),
}));

jest.mock('@/utils/supabase/service-role', () => ({
  createServiceRoleClient: jest.fn(),
}));

jest.mock('@/lib/auth/utils', () => ({
  getUserIdFromRequest: jest.fn(),
}));

jest.mock('crypto', () => ({
  randomBytes: jest.fn(() => ({
    toString: jest.fn(() => 'generated-device-secret-hex'),
  })),
}));

import { hashSecret } from '@/lib/security/hashing';
import { createServiceRoleClient } from '@/utils/supabase/service-role';
import { getUserIdFromRequest } from '@/lib/auth/utils';
import crypto from 'crypto';

describe('/api/devices/register', () => {
  let mockSupabase: any;
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock environment variables
    process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co';
    process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-role-key';
    
    // Setup mock Supabase client with properly chained methods
    const mockSingle = jest.fn();
    const mockSelect = jest.fn(() => ({ single: mockSingle }));
    const mockInsert = jest.fn(() => ({ select: mockSelect }));
    const mockFrom = jest.fn(() => ({ insert: mockInsert }));
    
    mockSupabase = {
      from: mockFrom,
    };
    
    // Store references for easy access in tests
    mockSupabase.mockFrom = mockFrom;
    mockSupabase.mockInsert = mockInsert;
    mockSupabase.mockSelect = mockSelect;
    mockSupabase.mockSingle = mockSingle;
    (createServiceRoleClient as jest.Mock).mockReturnValue(mockSupabase);
  });

  describe('POST', () => {
    it('should successfully register a device for authenticated user', async () => {
      // Mock authenticated user
      (getUserIdFromRequest as jest.Mock).mockResolvedValue('test-user-id');
      
      // Mock successful database insertion
      const mockDeviceId = 'test-device-id';
      const mockInsertResult = {
        data: { device_id: mockDeviceId },
        error: null,
      };
      mockSupabase.mockSingle.mockResolvedValue(mockInsertResult);

      const request = {
        json: jest.fn().mockResolvedValue({
          deviceName: 'iPhone 15',
          platform: 'ios',
          appSignatureHash: 'test-signature-hash',
        }),
      } as any;

      const response = await POST(request);
      const responseData = await response.json();
      
      // Debug logging
      if (response.status !== 200) {
        console.log('Response status:', response.status);
        console.log('Response data:', responseData);
      }

      expect(response.status).toBe(200);
      expect(responseData.deviceId).toBe(mockDeviceId);
      expect(responseData.deviceSecret).toBe('generated-device-secret-hex');
      
      // Verify database interaction
      expect(mockSupabase.mockFrom).toHaveBeenCalledWith('devices');
      expect(mockSupabase.mockInsert).toHaveBeenCalledWith({
        user_id: 'test-user-id',
        device_name: 'iPhone 15',
        platform: 'ios',
        device_secret_hash: 'hashed-secret',
        app_signature_hash: 'test-signature-hash',
      });
      
      // Verify secret was hashed
      expect(hashSecret).toHaveBeenCalledWith('generated-device-secret-hex');
      
      // Verify device secret was generated
      expect(crypto.randomBytes).toHaveBeenCalledWith(32);
    });

    it('should return 401 for unauthenticated user', async () => {
      // Mock unauthenticated user
      (getUserIdFromRequest as jest.Mock).mockResolvedValue(null);

      const request = {
        json: jest.fn().mockResolvedValue({
          deviceName: 'iPhone 15',
          platform: 'ios',
        }),
      } as any;

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(401);
      expect(responseData.error).toBe('Unauthorized');
      
      // Verify no database interaction occurred
      expect(mockSupabase.mockFrom).not.toHaveBeenCalled();
    });

    it('should return 400 for invalid request body', async () => {
      // Mock authenticated user
      (getUserIdFromRequest as jest.Mock).mockResolvedValue('test-user-id');

      const request = {
        json: jest.fn().mockResolvedValue({
          deviceName: 'iPhone 15',
          platform: 'invalid-platform', // Invalid platform value
          appSignatureHash: 'test-signature-hash',
        }),
      } as any;

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(400);
      expect(responseData.error).toBe('Validation failed');
      expect(responseData.details).toBeInstanceOf(Array);
      expect(responseData.details.length).toBeGreaterThan(0);
      
      // Verify no database interaction occurred
      expect(mockSupabase.mockFrom).not.toHaveBeenCalled();
    });

    it('should return 500 for database errors', async () => {
      // Mock authenticated user
      (getUserIdFromRequest as jest.Mock).mockResolvedValue('test-user-id');
      
      // Mock database error
      mockSupabase.mockSingle.mockResolvedValue({
        data: null,
        error: { message: 'Database connection failed' },
      });

      const request = {
        json: jest.fn().mockResolvedValue({
          deviceName: 'iPhone 15',
          platform: 'ios',
          appSignatureHash: 'test-signature-hash',
        }),
      } as any;

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(500);
      expect(responseData.error).toBe('Internal Server Error');
    });

    it('should handle optional appSignatureHash correctly', async () => {
      // Mock authenticated user
      (getUserIdFromRequest as jest.Mock).mockResolvedValue('test-user-id');
      
      // Mock successful database insertion
      mockSupabase.mockSingle.mockResolvedValue({
        data: { device_id: 'test-device-id' },
        error: null,
      });

      const request = {
        json: jest.fn().mockResolvedValue({
          deviceName: 'Android Phone',
          platform: 'android',
          // No appSignatureHash provided
        }),
      } as any;

      const response = await POST(request);

      expect(response.status).toBe(200);
      
      // Verify database was called with null for app_signature_hash
      expect(mockSupabase.mockInsert).toHaveBeenCalledWith({
        user_id: 'test-user-id',
        device_name: 'Android Phone',
        platform: 'android',
        device_secret_hash: 'hashed-secret',
        app_signature_hash: null,
      });
    });
  });
});