import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import crypto from 'crypto';
import { generateAccessToken, generateRefreshToken } from '@/lib/auth/jwt';
import { hashSecret } from '@/lib/security/hashing';
import { createServiceRoleClient } from '@/utils/supabase/service-role';
import { 
  bruteForceProtectionMiddleware, 
  getClientIP 
} from '@/lib/middleware/bruteForceProtection';

const verifyOtpSchema = z.object({
  email: z.string().email("Invalid email format"),
  otp: z.string().length(6, "OTP must be 6 digits"),
  deviceInfo: z.object({
    deviceName: z.string().min(1, "Device name is required"),
    platform: z.enum(['ios', 'android', 'web']),
    appSignatureHash: z.string().optional(),
  }),
});

export async function POST(req: NextRequest) {
  try {
    // 1. Parse and validate request body
    const body = await req.json();
    const validation = verifyOtpSchema.safeParse(body);
    if (!validation.success) {
      return new NextResponse(JSON.stringify({ 
        error: 'Validation failed',
        details: validation.error.issues 
      }), { 
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const { email, otp, deviceInfo } = validation.data;

    // 2. Apply brute-force protection
    const ipAddress = getClientIP(req);

    const bruteForceCheck = await bruteForceProtectionMiddleware(req, {
      operation: 'verify_otp',
      email,
      ipAddress,
    });

    if (bruteForceCheck) {
      return bruteForceCheck; // Rate limited
    }

    // 3. Verify OTP with Supabase
    const supabase = createServiceRoleClient();
    
    const { data: authData, error: authError } = await supabase.auth.verifyOtp({
      email: email,
      token: otp,
      type: 'email',
    });

    if (authError || !authData.user) {
      return new NextResponse(JSON.stringify({ 
        error: authError?.message || 'Invalid OTP'
      }), { 
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const userId = authData.user.id;

    // 4. Get user roles from the database (default to empty array for now)
    // TODO: Implement roles in Story 6.6
    const userRoles: string[] = [];

    // 5. Generate tokens
    const accessToken = generateAccessToken(userId, userRoles);
    const refreshToken = generateRefreshToken();

    // 6. Register device (using logic from Story 2.1)
    const deviceSecret = crypto.randomBytes(32).toString('hex');
    const hmacKey = crypto.randomBytes(32).toString('hex');
    const deviceSecretHash = await hashSecret(deviceSecret);

    const { data: deviceData, error: deviceError } = await supabase
      .from('devices')
      .insert({
        user_id: userId,
        device_name: deviceInfo.deviceName,
        platform: deviceInfo.platform,
        device_secret_hash: deviceSecretHash,
        hmac_key_hash: hmacKey,
        app_signature_hash: deviceInfo.appSignatureHash || null,
      })
      .select('device_id')
      .single();

    if (deviceError) {
      console.error('Device registration error during OTP verification:', deviceError);
      return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const deviceId = deviceData.device_id;

    // 7. Store refresh token in database
    const refreshTokenHash = await hashSecret(refreshToken);
    const refreshTokenExpiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days

    const { error: refreshTokenError } = await supabase
      .from('refresh_tokens')
      .insert({
        user_id: userId,
        device_id: deviceId,
        token_hash: refreshTokenHash,
        expires_at: refreshTokenExpiresAt.toISOString(),
      });

    if (refreshTokenError) {
      console.error('Refresh token storage error during OTP verification:', refreshTokenError);
      return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 8. Return tokens and device information
    return NextResponse.json({
      success: true,
      accessToken,
      refreshToken,
      deviceId,
      deviceSecret,
      hmacKey,
      message: "Successfully signed in!",
    });

  } catch (error) {
    console.error('Unexpected error in verify-otp:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), { 
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}