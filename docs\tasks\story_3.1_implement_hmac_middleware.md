### User Story 3.1: Implement HMAC Signature Verification Middleware

**Description:** This story creates the core security middleware that ensures every protected API request is authentic and has not been tampered with. It validates a signature sent from the client that was created using their unique `deviceSecret`.

**Acceptance Criteria:**
- A middleware is created that runs on protected API routes.
- It correctly validates the HMAC signature for incoming requests.
- It rejects any request with a missing, malformed, or invalid signature.
- The signature comparison is done in a way that is secure against timing attacks.

---

### Development Tasks

-   [ ] **1. Update Database Schema for Encrypted Secrets (Decision)**
    *   **Task:** To verify an HMAC signature, the server must have access to the original secret. Storing it in plaintext is not secure. Storing only a hash (like with a password) is not sufficient as the original secret is needed for the HMAC calculation.
    *   **Decision:** We will store the `device_secret` in the database, but it must be **encrypted**. Use Supabase's [column encryption](https://supabase.com/docs/guides/database/encrypting-data) or a similar method. The encryption key must be stored securely as an environment variable, not in the database.
    *   **Action:** Modify the `devices` table migration (from Story 1.1) if necessary to handle encrypted data. *This is a prerequisite before proceeding.* 

-   [ ] **2. Create HMAC Utility Module**
    *   Create a new file for HMAC-specific logic.
    *   **File Path:** `dukancard/lib/security/hmac.ts`
    *   **Implementation:** Add functions for creating and verifying the signature. The verification function will be used in the middleware.

    ```typescript
    // dukancard/lib/security/hmac.ts
    import crypto from 'crypto';

    const HMAC_ALGORITHM = 'sha256';

    // This function might be used more in testing and on the client-side
    export function createSignature(secret: string, message: string): string {
        return crypto.createHmac(HMAC_ALGORITHM, secret).update(message).digest('hex');
    }

    export function verifySignature(signature: string, secret: string, message: string): boolean {
        const expectedSignature = createSignature(secret, message);
        
        // Use timingSafeEqual for security
        try {
            return crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(expectedSignature));
        } catch {
            return false; // Buffers have different lengths
        }
    }
    ```

-   [ ] **3. Implement HMAC Middleware Logic**
    *   **Task:** In your main security middleware (`middleware.ts`), add the logic to perform the HMAC check.
    *   **Logic Steps:**
        1.  **Extract Headers:** Get `X-Device-Id`, `X-Timestamp`, and `X-Signature` from the request headers. If any are missing, reject with a `400 Bad Request`.
        2.  **Fetch and Decrypt Secret:** Use the `X-Device-Id` to query the `devices` table for the corresponding record. Retrieve the encrypted `device_secret`. Decrypt it using the server-side encryption key.
        3.  **Handle Device Not Found:** If the device is not found or has been revoked, reject with `403 Forbidden`.
        4.  **Construct Message:** Recreate the exact message string that the client signed. This is `(httpMethod + apiPath + timestamp + sha256(requestBody))`. Note: Reading the request body in middleware can be complex as it's a stream; it may need to be cloned.
        5.  **Verify Signature:** Call the `verifySignature` utility from Step 2, passing the signature from the header, the decrypted secret, and the constructed message.
        6.  **Handle Invalid Signature:** If `verifySignature` returns `false`, reject the request with `403 Forbidden` and an "Invalid Signature" error.
        7.  If verification passes, allow the request to proceed.

-   [ ] **4. Write Integration Tests**
    *   **Task:** Test the middleware logic thoroughly.
    *   **File Path:** `dukancard/__tests__/middleware.test.ts` (or a dedicated test file for HMAC middleware).
    *   **Test Cases:**
        *   **Test 1: Valid Signature:** Mock a database call that returns a valid (encrypted) secret. Mock the decryption. Provide a valid set of headers and body. Assert that the middleware allows the request to pass.
        *   **Test 2: Invalid Signature:** Use the same setup as Test 1 but provide a signature that has been tampered with. Assert the middleware rejects with `403`.
        *   **Test 3: Missing Headers:** Call the middleware with one or more of the required `X-` headers missing. Assert it rejects with `400`.
        *   **Test 4: Unknown Device:** Provide a valid-looking `X-Device-Id` but have the database mock return `null`. Assert the middleware rejects with `403`.