/**
 * Utility wrapper for making authenticated API calls using the AuthenticatedApiClient
 * This provides a simple interface for making authenticated requests throughout the app
 */

import { AuthenticatedApiClient } from '@/lib/stores/authStore';

// Helper function for authenticated fetch calls
export async function authenticatedFetch(
  url: string, 
  options: RequestInit = {}
): Promise<Response> {
  const apiClient = AuthenticatedApiClient.getInstance();
  return apiClient.fetch(url, options);
}

// Helper function for authenticated JSON API calls
export async function authenticatedApiCall<T = any>(
  url: string,
  options: RequestInit = {}
): Promise<{ data: T | null; error: string | null; success: boolean }> {
  try {
    const response = await authenticatedFetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        data: null,
        error: data.error || `Request failed with status ${response.status}`,
        success: false,
      };
    }

    return {
      data,
      error: null,
      success: true,
    };
  } catch (error) {
    return {
      data: null,
      error: error instanceof Error ? error.message : 'An unexpected error occurred',
      success: false,
    };
  }
}

// Example usage:
// const { data, error, success } = await authenticatedApiCall('/api/user/profile');
// if (success) {
//   console.log('Profile data:', data);
// } else {
//   console.error('Error fetching profile:', error);
// }