module.exports = {

"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createClient": (()=>createClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createServerClient.js [app-rsc] (ecmascript)");
;
async function createClient() {
    const supabaseUrl = ("TURBOPACK compile-time value", "https://rnjolcoecogzgglnblqn.supabase.co");
    const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o");
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    // Check if we're in a test environment
    let headersList = null;
    let cookieStore = null;
    try {
        // Dynamically import next/headers to avoid issues in edge runtime
        const { headers, cookies } = await __turbopack_context__.r("[project]/node_modules/next/headers.js [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
        headersList = await headers();
        cookieStore = await cookies();
    } catch (error) {
        // If next/headers is not available (e.g., in edge runtime), continue without it
        console.warn('next/headers not available in this context, using fallback');
    }
    const isTestEnvironment = ("TURBOPACK compile-time value", "development") === 'test' || process.env.PLAYWRIGHT_TESTING === 'true' || headersList && headersList.get('x-playwright-testing') === 'true';
    if (isTestEnvironment && headersList) {
        // Return a mocked Supabase client for testing
        return createMockSupabaseClient(headersList);
    }
    // If cookies are not available, create a basic server client
    if (!cookieStore) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createServerClient"])(supabaseUrl, supabaseAnonKey, {
            cookies: {
                getAll () {
                    return [];
                },
                setAll () {
                // No-op when cookies are not available
                }
            }
        });
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createServerClient"])(supabaseUrl, supabaseAnonKey, {
        cookies: {
            async getAll () {
                return await cookieStore.getAll();
            },
            async setAll (cookiesToSet) {
                try {
                    for (const { name, value, options } of cookiesToSet){
                        await cookieStore.set(name, value, options);
                    }
                } catch  {
                // The `setAll` method was called from a Server Component.
                // This can be ignored if you have middleware refreshing
                // user sessions.
                }
            }
        }
    });
}
function createMockSupabaseClient(headersList) {
    const testAuthState = headersList.get('x-test-auth-state');
    const testUserType = headersList.get('x-test-user-type');
    const testHasProfile = testUserType === 'customer' || testUserType === 'business';
    const testBusinessSlug = headersList.get('x-test-business-slug');
    const testPlanId = headersList.get('x-test-plan-id') || 'free';
    return {
        auth: {
            getUser: async ()=>{
                if (testAuthState === 'authenticated') {
                    return {
                        data: {
                            user: {
                                id: 'test-user-id',
                                email: '<EMAIL>'
                            }
                        },
                        error: null
                    };
                }
                return {
                    data: {
                        user: null
                    },
                    error: {
                        message: 'Unauthorized',
                        name: 'AuthApiError',
                        status: 401
                    }
                };
            },
            getSession: async ()=>{
                if (testAuthState === 'authenticated') {
                    return {
                        data: {
                            session: {
                                user: {
                                    id: 'test-user-id',
                                    email: '<EMAIL>'
                                }
                            }
                        },
                        error: null
                    };
                }
                return {
                    data: {
                        session: null
                    },
                    error: {
                        message: 'Unauthorized',
                        name: 'AuthApiError',
                        status: 401
                    }
                };
            },
            signInWithOtp: async ()=>({
                    data: {
                        user: null,
                        session: null
                    },
                    error: null
                }),
            signOut: async ()=>({
                    error: null
                })
        },
        from: (table)=>createMockQueryBuilder(table, testUserType, testHasProfile, testBusinessSlug, testPlanId)
    };
}
function createMockQueryBuilder(table, testUserType, testHasProfile, testBusinessSlug, testPlanId) {
    const getMockData = ()=>getMockTableData(table, testUserType, testHasProfile, testBusinessSlug, testPlanId);
    const createChainableMock = (data)=>({
            select: (_columns)=>createChainableMock(data),
            eq: (_column, _value)=>createChainableMock(data),
            neq: (_column, _value)=>createChainableMock(data),
            gt: (_column, _value)=>createChainableMock(data),
            gte: (_column, _value)=>createChainableMock(data),
            lt: (_column, _value)=>createChainableMock(data),
            lte: (_column, _value)=>createChainableMock(data),
            like: (_column, _pattern)=>createChainableMock(data),
            ilike: (_column, _pattern)=>createChainableMock(data),
            is: (_column, _value)=>createChainableMock(data),
            in: (_column, _values)=>createChainableMock(data),
            contains: (_column, _value)=>createChainableMock(data),
            containedBy: (_column, _value)=>createChainableMock(data),
            rangeGt: (_column, _value)=>createChainableMock(data),
            rangeGte: (_column, _value)=>createChainableMock(data),
            rangeLt: (_column, _value)=>createChainableMock(data),
            rangeLte: (_column, _value)=>createChainableMock(data),
            rangeAdjacent: (_column, _value)=>createChainableMock(data),
            overlaps: (_column, _value)=>createChainableMock(data),
            textSearch: (_column, _query)=>createChainableMock(data),
            match: (_query)=>createChainableMock(data),
            not: (_column, _operator, _value)=>createChainableMock(data),
            or: (_filters)=>createChainableMock(data),
            filter: (_column, _operator, _value)=>createChainableMock(data),
            order: (_column, _options)=>createChainableMock(data),
            limit: (_count, _options)=>createChainableMock(data),
            range: (_from, _to, _options)=>createChainableMock(data),
            abortSignal: (_signal)=>createChainableMock(data),
            single: async ()=>getMockData(),
            maybeSingle: async ()=>getMockData(),
            then: async (callback)=>{
                const result = getMockData();
                return callback ? callback(result) : result;
            },
            data: data || [],
            error: null,
            count: data ? data.length : 0,
            status: 200,
            statusText: 'OK'
        });
    return {
        select: (_columns)=>createChainableMock(),
        insert: (data)=>({
                select: (_columns)=>({
                        single: async ()=>({
                                data: Array.isArray(data) ? data[0] : data,
                                error: null
                            }),
                        maybeSingle: async ()=>({
                                data: Array.isArray(data) ? data[0] : data,
                                error: null
                            }),
                        then: async (_callback)=>{
                            const result = {
                                data: Array.isArray(data) ? data : [
                                    data
                                ],
                                error: null
                            };
                            return _callback ? _callback(result) : result;
                        }
                    }),
                then: async (_callback)=>{
                    const result = {
                        data: Array.isArray(data) ? data : [
                            data
                        ],
                        error: null
                    };
                    return _callback ? _callback(result) : result;
                }
            }),
        update: (data)=>createChainableMock(data),
        upsert: (data)=>createChainableMock(data),
        delete: ()=>createChainableMock(),
        rpc: (_functionName, _params)=>createChainableMock()
    };
}
/**
 * Helper function to get mock table data based on test state
 */ function getMockTableData(table, testUserType, testHasProfile, testBusinessSlug, testPlanId) {
    if (table === 'customer_profiles') {
        const hasCustomerProfile = testHasProfile && testUserType === 'customer';
        return {
            data: hasCustomerProfile ? {
                id: 'test-user-id',
                name: 'Test Customer',
                avatar_url: null,
                phone: '+1234567890',
                email: '<EMAIL>',
                address: 'Test Address',
                city: 'Test City',
                state: 'Test State',
                pincode: '123456'
            } : null,
            error: null
        };
    }
    if (table === 'business_profiles') {
        const hasBusinessProfile = testHasProfile && testUserType === 'business';
        return {
            data: hasBusinessProfile ? {
                id: 'test-user-id',
                business_slug: testBusinessSlug || null,
                trial_end_date: null,
                has_active_subscription: true,
                business_name: 'Test Business',
                city_slug: 'test-city',
                state_slug: 'test-state',
                locality_slug: 'test-locality',
                pincode: '123456',
                business_description: 'Test business description',
                business_category: 'retail',
                phone: '+1234567890',
                email: '<EMAIL>',
                website: 'https://testbusiness.com'
            } : null,
            error: null
        };
    }
    if (table === 'payment_subscriptions') {
        return {
            data: testUserType === 'business' ? {
                id: 'test-subscription-id',
                plan_id: testPlanId,
                business_profile_id: 'test-user-id',
                status: 'active',
                created_at: '2024-01-01T00:00:00Z'
            } : null,
            error: null
        };
    }
    if (table === 'products') {
        return {
            data: testUserType === 'business' ? [
                {
                    id: 'test-product-1',
                    name: 'Test Product 1',
                    price: 100,
                    business_profile_id: 'test-user-id',
                    available: true
                },
                {
                    id: 'test-product-2',
                    name: 'Test Product 2',
                    price: 200,
                    business_profile_id: 'test-user-id',
                    available: false
                }
            ] : [],
            error: null
        };
    }
    // Default return for unknown tables
    return {
        data: null,
        error: null
    };
}
}}),
"[project]/lib/actions/businessProfiles/types.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
;
}}),
"[project]/lib/actions/businessProfiles/utils.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "applySorting": (()=>applySorting),
    "createSubscriptionMap": (()=>createSubscriptionMap),
    "getCurrentISOTimestamp": (()=>getCurrentISOTimestamp)
});
function applySorting(query, sortBy) {
    switch(sortBy){
        case "name_asc":
            return query.order("business_name", {
                ascending: true
            });
        case "name_desc":
            return query.order("business_name", {
                ascending: false
            });
        case "created_asc":
            return query.order("created_at", {
                ascending: true
            });
        case "created_desc":
            return query.order("created_at", {
                ascending: false
            });
        case "likes_asc":
            return query.order("total_likes", {
                ascending: true
            });
        case "likes_desc":
            return query.order("total_likes", {
                ascending: false
            });
        case "subscriptions_asc":
            return query.order("total_subscriptions", {
                ascending: true
            });
        case "subscriptions_desc":
            return query.order("total_subscriptions", {
                ascending: false
            });
        case "rating_asc":
            return query.order("average_rating", {
                ascending: true
            });
        case "rating_desc":
            return query.order("average_rating", {
                ascending: false
            });
        default:
            return query.order("created_at", {
                ascending: false
            });
    }
}
function getCurrentISOTimestamp() {
    return new Date().toISOString();
}
function createSubscriptionMap(subscriptionsData) {
    const subscriptionMap = new Map();
    if (subscriptionsData) {
        // Group by business_profile_id and take the most recent one
        subscriptionsData.forEach((sub)=>{
            if (!subscriptionMap.has(sub.business_profile_id)) {
                subscriptionMap.set(sub.business_profile_id, {
                    subscription_status: sub.subscription_status,
                    plan_id: sub.plan_id
                });
            }
        });
    }
    return subscriptionMap;
}
}}),
"[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"407591979af61a7d463be0dd2c1b3975f1e37c6d4d":"getSecureBusinessProfileWithProductsBySlug","40e464278039bdd26313983f57189fe5ad16207de5":"getSecureBusinessProfileBySlug"},"",""] */ __turbopack_context__.s({
    "getSecureBusinessProfileBySlug": (()=>getSecureBusinessProfileBySlug),
    "getSecureBusinessProfileWithProductsBySlug": (()=>getSecureBusinessProfileWithProductsBySlug)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
async function getSecureBusinessProfileBySlug(slug) {
    if (!slug) {
        return {
            error: "Business slug is required."
        };
    }
    try {
        // Use the business profile API endpoint for public access by slug
        const response = await fetch(`${("TURBOPACK compile-time value", "http://localhost:3000")}/api/business/slug/${slug}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        if (!response.ok) {
            console.error("API Fetch Error:", result);
            return {
                error: result.error || "Failed to fetch business profile"
            };
        }
        const profileData = result.business;
        if (!profileData) {
            return {
                error: "Profile not found."
            };
        }
        return {
            data: profileData
        };
    } catch (e) {
        console.error("Exception in getSecureBusinessProfileBySlug:", e);
        return {
            error: "An unexpected error occurred."
        };
    }
}
async function getSecureBusinessProfileWithProductsBySlug(slug) {
    if (!slug) {
        return {
            error: "Business slug is required."
        };
    }
    try {
        // Use the business profile API endpoint for public access by slug
        const response = await fetch(`${("TURBOPACK compile-time value", "http://localhost:3000")}/api/business/slug/${slug}?include_products=true`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        if (!response.ok) {
            console.error("API Fetch Error:", result);
            return {
                error: result.error || "Failed to fetch business profile"
            };
        }
        const profileData = result.business;
        if (!profileData) {
            return {
                error: "Profile not found."
            };
        }
        const safeData = {
            ...profileData,
            products_services: profileData.products_services || []
        };
        return {
            data: safeData
        };
    } catch (e) {
        console.error("Exception in getSecureBusinessProfileWithProductsBySlug:", e);
        return {
            error: "An unexpected error occurred."
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getSecureBusinessProfileBySlug,
    getSecureBusinessProfileWithProductsBySlug
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getSecureBusinessProfileBySlug, "40e464278039bdd26313983f57189fe5ad16207de5", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getSecureBusinessProfileWithProductsBySlug, "407591979af61a7d463be0dd2c1b3975f1e37c6d4d", null);
}}),
"[project]/lib/actions/businessProfiles/discovery.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"706494db815230f39fde096a9039730918baa8e0b5":"getSecureBusinessProfileIdsForDiscover","7cc9f62ccbdcd1bc4ed4f879f91c918806e0e8ca33":"getSecureBusinessProfilesForDiscover"},"",""] */ __turbopack_context__.s({
    "getSecureBusinessProfileIdsForDiscover": (()=>getSecureBusinessProfileIdsForDiscover),
    "getSecureBusinessProfilesForDiscover": (()=>getSecureBusinessProfilesForDiscover)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
async function getSecureBusinessProfilesForDiscover(pincodes, locality, page = 1, limit = 10, sortBy = "created_desc") {
    if (!pincodes || Array.isArray(pincodes) && pincodes.length === 0) {
        return {
            error: "At least one pincode is required."
        };
    }
    // Convert single pincode to array for consistent handling
    const pincodeArray = Array.isArray(pincodes) ? pincodes : [
        pincodes
    ];
    try {
        // Build query parameters
        const queryParams = new URLSearchParams();
        queryParams.set('pincodes', pincodeArray.join(','));
        queryParams.set('page', page.toString());
        queryParams.set('limit', limit.toString());
        queryParams.set('status', 'online');
        // Convert sortBy to API format
        let apiSortBy = 'created_desc';
        switch(sortBy){
            case 'name_asc':
                apiSortBy = 'name_asc';
                break;
            case 'name_desc':
                apiSortBy = 'name_desc';
                break;
            case 'created_asc':
                apiSortBy = 'created_asc';
                break;
            case 'created_desc':
                apiSortBy = 'created_desc';
                break;
            case 'likes_asc':
                apiSortBy = 'likes_asc';
                break;
            case 'likes_desc':
                apiSortBy = 'likes_desc';
                break;
            case 'subscriptions_asc':
                apiSortBy = 'subscriptions_asc';
                break;
            case 'subscriptions_desc':
                apiSortBy = 'subscriptions_desc';
                break;
            case 'rating_asc':
                apiSortBy = 'rating_asc';
                break;
            case 'rating_desc':
                apiSortBy = 'rating_desc';
                break;
        }
        queryParams.set('sort_by', apiSortBy);
        if (locality) {
            queryParams.set('locality', locality);
        }
        // Use the business profile API endpoint
        const response = await fetch(`${("TURBOPACK compile-time value", "http://localhost:3000")}/api/business?${queryParams.toString()}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        if (!response.ok) {
            console.error("API Fetch Error:", result);
            return {
                error: result.error || "Failed to fetch business profiles"
            };
        }
        const businesses = result.businesses || [];
        const count = result.pagination?.total || 0;
        // Transform data to match expected format
        const safeData = businesses.map((profile)=>{
            return {
                ...profile,
                // Add missing fields with default values if not present
                total_visits: profile.total_visits || 0,
                today_visits: profile.today_visits || 0,
                yesterday_visits: profile.yesterday_visits || 0,
                visits_7_days: profile.visits_7_days || 0,
                visits_30_days: profile.visits_30_days || 0,
                city_slug: profile.city_slug || null,
                state_slug: profile.state_slug || null,
                locality_slug: profile.locality_slug || null,
                gallery: profile.gallery || null,
                latitude: profile.latitude || null,
                longitude: profile.longitude || null
            };
        });
        return {
            data: safeData,
            count
        };
    } catch (e) {
        console.error("Exception in getSecureBusinessProfilesForDiscover:", e);
        return {
            error: "An unexpected error occurred."
        };
    }
}
async function getSecureBusinessProfileIdsForDiscover(pincodes, locality, sortBy = "created_desc") {
    if (!pincodes || Array.isArray(pincodes) && pincodes.length === 0) {
        return {
            error: "At least one pincode is required."
        };
    }
    // Convert single pincode to array for consistent handling
    const pincodeArray = Array.isArray(pincodes) ? pincodes : [
        pincodes
    ];
    try {
        // Build query parameters
        const queryParams = new URLSearchParams();
        queryParams.set('pincodes', pincodeArray.join(','));
        queryParams.set('status', 'online');
        queryParams.set('ids_only', 'true');
        // Convert sortBy to API format
        let apiSortBy = 'created_desc';
        switch(sortBy){
            case 'name_asc':
                apiSortBy = 'name_asc';
                break;
            case 'name_desc':
                apiSortBy = 'name_desc';
                break;
            case 'created_asc':
                apiSortBy = 'created_asc';
                break;
            case 'created_desc':
                apiSortBy = 'created_desc';
                break;
            case 'likes_asc':
                apiSortBy = 'likes_asc';
                break;
            case 'likes_desc':
                apiSortBy = 'likes_desc';
                break;
            case 'subscriptions_asc':
                apiSortBy = 'subscriptions_asc';
                break;
            case 'subscriptions_desc':
                apiSortBy = 'subscriptions_desc';
                break;
            case 'rating_asc':
                apiSortBy = 'rating_asc';
                break;
            case 'rating_desc':
                apiSortBy = 'rating_desc';
                break;
        }
        queryParams.set('sort_by', apiSortBy);
        if (locality) {
            queryParams.set('locality', locality);
        }
        // Use the business profile API endpoint
        const response = await fetch(`${("TURBOPACK compile-time value", "http://localhost:3000")}/api/business?${queryParams.toString()}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        if (!response.ok) {
            console.error("API Fetch Error:", result);
            return {
                error: result.error || "Failed to fetch business profile IDs"
            };
        }
        const businessIds = result.business_ids || [];
        return {
            data: businessIds
        };
    } catch (e) {
        console.error("Exception in getSecureBusinessProfileIdsForDiscover:", e);
        return {
            error: "An unexpected error occurred."
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getSecureBusinessProfilesForDiscover,
    getSecureBusinessProfileIdsForDiscover
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getSecureBusinessProfilesForDiscover, "7cc9f62ccbdcd1bc4ed4f879f91c918806e0e8ca33", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getSecureBusinessProfileIdsForDiscover, "706494db815230f39fde096a9039730918baa8e0b5", null);
}}),
"[project]/lib/actions/businessProfiles/search.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7f19088479bd4247b361efa63e1f08c59cbae5a424":"getSecureBusinessProfiles"},"",""] */ __turbopack_context__.s({
    "getSecureBusinessProfiles": (()=>getSecureBusinessProfiles)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
async function getSecureBusinessProfiles(searchTerm, pincode, locality, page = 1, limit = 20, sortBy = "created_desc", category) {
    try {
        // Build query parameters
        const queryParams = new URLSearchParams();
        queryParams.set('page', page.toString());
        queryParams.set('limit', limit.toString());
        queryParams.set('status', 'online');
        if (searchTerm) {
            queryParams.set('search', searchTerm.trim());
        }
        if (pincode) {
            queryParams.set('pincode', pincode);
        }
        if (locality) {
            queryParams.set('locality', locality);
        }
        if (category && category.trim()) {
            queryParams.set('category', category.trim());
        }
        // Convert sortBy to API format
        let apiSortBy = 'created_desc';
        switch(sortBy){
            case 'name_asc':
                apiSortBy = 'name_asc';
                break;
            case 'name_desc':
                apiSortBy = 'name_desc';
                break;
            case 'created_asc':
                apiSortBy = 'created_asc';
                break;
            case 'created_desc':
                apiSortBy = 'created_desc';
                break;
            case 'likes_asc':
                apiSortBy = 'likes_asc';
                break;
            case 'likes_desc':
                apiSortBy = 'likes_desc';
                break;
            case 'subscriptions_asc':
                apiSortBy = 'subscriptions_asc';
                break;
            case 'subscriptions_desc':
                apiSortBy = 'subscriptions_desc';
                break;
            case 'rating_asc':
                apiSortBy = 'rating_asc';
                break;
            case 'rating_desc':
                apiSortBy = 'rating_desc';
                break;
        }
        queryParams.set('sort_by', apiSortBy);
        // Use the business profile API endpoint
        const response = await fetch(`${("TURBOPACK compile-time value", "http://localhost:3000")}/api/business?${queryParams.toString()}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        if (!response.ok) {
            console.error("API Fetch Error:", result);
            return {
                error: result.error || "Failed to fetch business profiles"
            };
        }
        const businesses = result.businesses || [];
        const count = result.pagination?.total || 0;
        // Transform data to match expected format
        const safeData = businesses.map((profile)=>{
            return {
                ...profile,
                // Add missing fields with default values if not present
                total_visits: profile.total_visits || 0,
                today_visits: profile.today_visits || 0,
                yesterday_visits: profile.yesterday_visits || 0,
                visits_7_days: profile.visits_7_days || 0,
                visits_30_days: profile.visits_30_days || 0,
                city_slug: profile.city_slug || null,
                state_slug: profile.state_slug || null,
                locality_slug: profile.locality_slug || null,
                gallery: profile.gallery || null,
                latitude: profile.latitude || null,
                longitude: profile.longitude || null
            };
        });
        return {
            data: safeData,
            count
        };
    } catch (e) {
        console.error("Exception in getSecureBusinessProfiles:", e);
        return {
            error: "An unexpected error occurred."
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getSecureBusinessProfiles
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getSecureBusinessProfiles, "7f19088479bd4247b361efa63e1f08c59cbae5a424", null);
}}),
"[project]/lib/actions/businessProfiles/sitemap.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"00be04a805bc46661f0f4971fc802a16df9b7de3d1":"getSecureBusinessProfilesForSitemap"},"",""] */ __turbopack_context__.s({
    "getSecureBusinessProfilesForSitemap": (()=>getSecureBusinessProfilesForSitemap)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
async function getSecureBusinessProfilesForSitemap() {
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Fetch all business profiles with status "online"
        const { data: profiles, error: profilesError } = await supabase.from("business_profiles").select("business_slug, updated_at").eq("status", "online") // Only fetch online profiles
        .not("business_slug", "is", null); // Ensure business_slug is not null
        if (profilesError) {
            return {
                error: "Database error fetching profiles."
            };
        }
        // If there are no profiles, return empty array
        if (!profiles || profiles.length === 0) {
            return {
                data: []
            };
        }
        // Create a map to deduplicate by business_slug
        const uniqueProfiles = new Map();
        // Add all profiles to the map (this automatically deduplicates by business_slug)
        profiles.forEach((profile)=>{
            if (profile.business_slug) {
                uniqueProfiles.set(profile.business_slug, {
                    business_slug: profile.business_slug,
                    updated_at: profile.updated_at
                });
            }
        });
        // Convert map values to array
        const combinedProfiles = Array.from(uniqueProfiles.values());
        // Return the deduplicated profiles
        return {
            data: combinedProfiles
        };
    } catch (_e) {
        return {
            error: "An unexpected error occurred."
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getSecureBusinessProfilesForSitemap
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getSecureBusinessProfilesForSitemap, "00be04a805bc46661f0f4971fc802a16df9b7de3d1", null);
}}),
"[project]/lib/actions/businessProfiles/access.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"00bcb7fbb12e893f3ec72c82ad13ff18ddb851bede":"getCurrentUserBusinessProfileId","407666f47811af7f53e12500216410b8cf9c72486c":"checkBusinessProfileAccess"},"",""] */ __turbopack_context__.s({
    "checkBusinessProfileAccess": (()=>checkBusinessProfileAccess),
    "getCurrentUserBusinessProfileId": (()=>getCurrentUserBusinessProfileId)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
async function checkBusinessProfileAccess(businessProfileId) {
    try {
        // Get the current user
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError || !user) {
            return {
                hasAccess: false,
                error: "Authentication required"
            };
        }
        // Check if the user owns the business profile
        const { data, error } = await supabase.from("business_profiles").select("id").eq("id", businessProfileId).eq("id", user.id) // Business profile ID should match user ID
        .maybeSingle();
        if (error) {
            console.error("Access check error:", error);
            return {
                hasAccess: false,
                error: "Error checking access"
            };
        }
        return {
            hasAccess: !!data
        };
    } catch (e) {
        console.error("Exception in checkBusinessProfileAccess:", e);
        return {
            hasAccess: false,
            error: "An unexpected error occurred"
        };
    }
}
async function getCurrentUserBusinessProfileId() {
    try {
        // Get the current user
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError || !user) {
            return {
                error: "Authentication required"
            };
        }
        // Check if the user has a business profile
        const { data, error } = await supabase.from("business_profiles").select("id").eq("id", user.id).maybeSingle();
        if (error) {
            console.error("Profile ID fetch error:", error);
            return {
                error: "Error fetching profile ID"
            };
        }
        if (!data) {
            return {
                error: "Business profile not found"
            };
        }
        return {
            profileId: data.id
        };
    } catch (e) {
        console.error("Exception in getCurrentUserBusinessProfileId:", e);
        return {
            error: "An unexpected error occurred"
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    checkBusinessProfileAccess,
    getCurrentUserBusinessProfileId
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(checkBusinessProfileAccess, "407666f47811af7f53e12500216410b8cf9c72486c", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getCurrentUserBusinessProfileId, "00bcb7fbb12e893f3ec72c82ad13ff18ddb851bede", null);
}}),
"[project]/lib/actions/businessProfiles/location.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"78aef494570fba887a7b263a064fef1e8968bf098e":"getSecureBusinessProfilesByLocation"},"",""] */ __turbopack_context__.s({
    "getSecureBusinessProfilesByLocation": (()=>getSecureBusinessProfilesByLocation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
async function getSecureBusinessProfilesByLocation(location, page = 1, limit = 20, sortBy = "created_desc") {
    try {
        // Build query parameters
        const queryParams = new URLSearchParams();
        queryParams.set('page', page.toString());
        queryParams.set('limit', limit.toString());
        queryParams.set('status', 'online');
        // Add location filters if provided
        if (location.pincode) {
            queryParams.set('pincode', location.pincode);
        }
        if (location.city) {
            queryParams.set('city', location.city);
        }
        if (location.state) {
            queryParams.set('state', location.state);
        }
        if (location.locality) {
            queryParams.set('locality', location.locality);
        }
        // Convert sortBy to API format
        let apiSortBy = 'created_desc';
        switch(sortBy){
            case 'name_asc':
                apiSortBy = 'name_asc';
                break;
            case 'name_desc':
                apiSortBy = 'name_desc';
                break;
            case 'created_asc':
                apiSortBy = 'created_asc';
                break;
            case 'created_desc':
                apiSortBy = 'created_desc';
                break;
            case 'likes_asc':
                apiSortBy = 'likes_asc';
                break;
            case 'likes_desc':
                apiSortBy = 'likes_desc';
                break;
            case 'subscriptions_asc':
                apiSortBy = 'subscriptions_asc';
                break;
            case 'subscriptions_desc':
                apiSortBy = 'subscriptions_desc';
                break;
            case 'rating_asc':
                apiSortBy = 'rating_asc';
                break;
            case 'rating_desc':
                apiSortBy = 'rating_desc';
                break;
        }
        queryParams.set('sort_by', apiSortBy);
        // Use the business profile API endpoint
        const response = await fetch(`${("TURBOPACK compile-time value", "http://localhost:3000")}/api/business?${queryParams.toString()}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        if (!response.ok) {
            console.error("API Fetch Error:", result);
            return {
                error: result.error || "Failed to fetch business profiles by location"
            };
        }
        const businesses = result.businesses || [];
        const count = result.pagination?.total || 0;
        // Transform data to match expected format
        const safeData = businesses.map((profile)=>{
            return {
                ...profile,
                // Add missing fields with default values if not present
                total_visits: profile.total_visits || 0,
                today_visits: profile.today_visits || 0,
                yesterday_visits: profile.yesterday_visits || 0,
                visits_7_days: profile.visits_7_days || 0,
                visits_30_days: profile.visits_30_days || 0,
                city_slug: profile.city_slug || null,
                state_slug: profile.state_slug || null,
                locality_slug: profile.locality_slug || null,
                gallery: profile.gallery || null,
                latitude: profile.latitude || null,
                longitude: profile.longitude || null
            };
        });
        return {
            data: safeData,
            count
        };
    } catch (error) {
        console.error("Unexpected error in getSecureBusinessProfilesByLocation:", error);
        return {
            error: "An unexpected error occurred."
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getSecureBusinessProfilesByLocation
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getSecureBusinessProfilesByLocation, "78aef494570fba887a7b263a064fef1e8968bf098e", null);
}}),
"[project]/lib/actions/businessProfiles/index.ts [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Re-export types and utility functions
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$types$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/types.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/utils.ts [app-rsc] (ecmascript)");
// Re-export server actions
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/discovery.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/search.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/sitemap.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/access.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/location.ts [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
}}),
"[project]/lib/actions/businessProfiles/index.ts [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$types$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/types.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/utils.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/discovery.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/search.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/sitemap.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/access.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/location.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/index.ts [app-rsc] (ecmascript) <locals>");
}}),
"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"4059dac56ec2d4fa83cae240560bfebb118fd5640f":"fetchMoreBusinessCardsCombined","40c76fcf00cbeff9df8975c5b6c7b4d802b3ba9578":"fetchBusinessesBySearch"},"",""] */ __turbopack_context__.s({
    "fetchBusinessesBySearch": (()=>fetchBusinessesBySearch),
    "fetchMoreBusinessCardsCombined": (()=>fetchMoreBusinessCardsCombined)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/index.ts [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/search.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$combinedActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/discover/actions/combinedActions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
async function fetchMoreBusinessCardsCombined(params) {
    // Reuse the searchDiscoverCombined function with viewType set to "cards"
    const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$combinedActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["searchDiscoverCombined"])({
        ...params,
        viewType: "cards"
    });
    if (result.error) {
        return {
            error: result.error
        };
    }
    if (!result.data?.businesses) {
        return {
            error: "No business data found"
        };
    }
    return {
        data: {
            businesses: result.data.businesses,
            totalCount: result.data.totalCount,
            hasMore: result.data.hasMore,
            nextPage: result.data.nextPage
        }
    };
}
async function fetchBusinessesBySearch(params) {
    const { businessName, pincode, locality, page = 1, limit = 20, sortBy = "created_desc", category = null } = params;
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    // Check Authentication
    const { data: { user } } = await supabase.auth.getUser();
    const isAuthenticated = !!user;
    try {
        // Use the secure method to fetch business profiles
        const { data: businessesData, count, error: businessesError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSecureBusinessProfiles"])(businessName, pincode, locality, page, limit, sortBy, category);
        if (businessesError) {
            console.error("Search Businesses By Name Error:", businessesError);
            return {
                error: businessesError
            };
        }
        const totalCount = count || 0;
        // Calculate if there are more pages
        const hasMore = totalCount > (page - 1) * limit + (businessesData?.length || 0);
        const nextPage = hasMore ? page + 1 : null;
        // Map raw data to BusinessCardData, handling potential nulls
        const businesses = businessesData?.map((data)=>{
            // Use the actual data from the database
            return {
                id: data.id,
                business_name: data.business_name ?? "",
                contact_email: "",
                // Subscription fields removed - all users now have access to all features
                created_at: data.created_at ?? undefined,
                updated_at: data.updated_at ?? undefined,
                logo_url: data.logo_url ?? "",
                member_name: data.member_name ?? "",
                title: data.title ?? "",
                address_line: data.address_line ?? "",
                city: data.city ?? "",
                state: data.state ?? "",
                pincode: data.pincode ?? "",
                locality: data.locality ?? "",
                phone: data.phone ?? "",
                business_category: data.business_category ?? "",
                instagram_url: data.instagram_url ?? "",
                facebook_url: data.facebook_url ?? "",
                whatsapp_number: data.whatsapp_number ?? "",
                about_bio: data.about_bio ?? "",
                status: data.status === "online" ? "online" : "offline",
                business_slug: data.business_slug ?? "",
                // Include metrics data
                total_likes: data.total_likes ?? 0,
                total_subscriptions: data.total_subscriptions ?? 0,
                average_rating: data.average_rating ?? 0,
                // Use actual data if available, otherwise use defaults
                // theme_color field removed - using default styling
                delivery_info: data.delivery_info ?? "",
                business_hours: data.business_hours,
                established_year: data.established_year ?? null,
                // Add default values for fields required by BusinessCardData but not in our query
                website_url: "",
                linkedin_url: "",
                twitter_url: "",
                youtube_url: "",
                call_number: ""
            };
        }) ?? [];
        return {
            data: {
                businesses,
                isAuthenticated,
                totalCount,
                hasMore,
                nextPage
            }
        };
    } catch (e) {
        console.error("Search Businesses Exception:", e);
        return {
            error: "An unexpected error occurred during the search."
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    fetchMoreBusinessCardsCombined,
    fetchBusinessesBySearch
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(fetchMoreBusinessCardsCombined, "4059dac56ec2d4fa83cae240560bfebb118fd5640f", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(fetchBusinessesBySearch, "40c76fcf00cbeff9df8975c5b6c7b4d802b3ba9578", null);
}}),
"[project]/app/(main)/discover/actions/types.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getSortingColumn": (()=>getSortingColumn),
    "getSortingDirection": (()=>getSortingDirection)
});
function getSortingColumn(sortBy, isProductView = false) {
    // For product view, we need to handle sorting differently
    if (isProductView) {
        switch(sortBy){
            case "name_asc":
            case "name_desc":
                return "name";
            case "price_asc":
            case "price_desc":
                // We'll handle price sorting with a custom approach in the query
                return "price";
            case "newest":
                // Handle 'newest' as a special case - sort by created_at descending
                return "created_at";
            case "created_asc":
            case "created_desc":
            default:
                return "created_at";
        }
    } else {
        // For business view
        switch(sortBy){
            case "name_asc":
            case "name_desc":
                return "name";
            case "price_asc":
            case "price_desc":
                return "base_price";
            case "likes_desc":
                return "likes_count";
            case "subscriptions_desc":
                return "subscriptions_count";
            case "rating_desc":
                return "average_rating";
            case "created_asc":
            case "created_desc":
            default:
                return "created_at";
        }
    }
}
function getSortingDirection(sortBy) {
    switch(sortBy){
        case "name_asc":
        case "price_asc":
        case "created_asc":
            return true; // ascending
        case "name_desc":
        case "price_desc":
        case "likes_desc":
        case "subscriptions_desc":
        case "rating_desc":
        case "created_desc":
        case "newest":
        default:
            return false; // descending
    }
}
}}),
"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"407927b1266213834749c44990bf37df557f7f8bdd":"fetchProductsByBusinessIds","408078c315b7b0094b642ab831ed7eb03d4fd98823":"fetchAllProducts","40cf076a0ff37f204cc8708187f4777e00ee8b9608":"fetchMoreProductsCombined"},"",""] */ __turbopack_context__.s({
    "fetchAllProducts": (()=>fetchAllProducts),
    "fetchMoreProductsCombined": (()=>fetchMoreProductsCombined),
    "fetchProductsByBusinessIds": (()=>fetchProductsByBusinessIds)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$types$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/discover/actions/types.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$combinedActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/discover/actions/combinedActions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
// Define a type for the product result from Supabase
// Unused type kept for reference
/* type ProductResult = {
  id: string;
  business_id: string | null;
  name: string | null;
  description: string | null;
  base_price: number | null;
  discounted_price: number | null;
  product_type: "physical" | "service" | null;
  is_available: boolean | null;
  image_url: string | null;
  created_at: string | null;
  updated_at: string | null;
  business_profiles: {
    business_slug: string | null;
  } | null;
}; */ // Helper function to convert any product result to NearbyProduct
function convertToNearbyProduct(product) {
    // Extract business_slug from the joined business_profiles
    let business_slug = null;
    if (product.business_profiles) {
        // Handle both object and array formats
        if (Array.isArray(product.business_profiles)) {
            business_slug = product.business_profiles[0]?.business_slug || null;
        } else if (product.business_profiles && typeof product.business_profiles === "object") {
            business_slug = product.business_profiles.business_slug || null;
        }
    }
    return {
        id: product.id,
        business_id: product.business_id,
        name: product.name || "",
        description: product.description || "",
        base_price: Number(product.base_price) || 0,
        discounted_price: product.discounted_price ? Number(product.discounted_price) : undefined,
        product_type: product.product_type || "physical",
        is_available: Boolean(product.is_available) || false,
        image_url: product.image_url,
        created_at: product.created_at,
        updated_at: product.updated_at,
        slug: product.slug,
        business_slug: business_slug,
        featured_image_index: 0,
        images: []
    };
}
async function fetchMoreProductsCombined(params) {
    // Reuse the searchDiscoverCombined function with viewType set to "products"
    const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$combinedActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["searchDiscoverCombined"])({
        ...params,
        viewType: "products"
    });
    if (result.error) {
        return {
            error: result.error
        };
    }
    if (!result.data?.products) {
        return {
            error: "No product data found"
        };
    }
    return {
        data: {
            products: result.data.products,
            totalCount: result.data.totalCount,
            hasMore: result.data.hasMore,
            nextPage: result.data.nextPage
        }
    };
}
async function fetchAllProducts(params) {
    const { page = 1, limit = 20, sortBy = "created_desc", productType = null, pincode = null, locality = null, productName = null, category = null } = params;
    // Check Authentication
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { user } } = await supabase.auth.getUser();
    const isAuthenticated = !!user;
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        const offset = (page - 1) * limit;
        // Get all online business IDs without checking subscription status
        let businessQuery = supabase.from("business_profiles").select("id").eq("status", "online");
        // Add pincode filter if provided
        if (pincode) {
            businessQuery = businessQuery.eq("pincode", pincode);
        }
        // Add locality filter if provided
        if (locality) {
            businessQuery = businessQuery.eq("locality", locality);
        }
        // Add category filter if provided
        if (category && category.trim()) {
            businessQuery = businessQuery.eq("business_category", category.trim());
        }
        const { data: validBusinesses, error: businessError } = await businessQuery;
        if (businessError) {
            console.error("Error fetching valid businesses:", businessError);
            return {
                error: "Failed to fetch valid businesses"
            };
        }
        if (!validBusinesses || validBusinesses.length === 0) {
            return {
                data: {
                    products: [],
                    isAuthenticated,
                    totalCount: 0,
                    hasMore: false,
                    nextPage: null
                }
            };
        }
        const validBusinessIds = validBusinesses.map((b)=>b.id);
        // Build the query for counting products - count products from valid businesses
        let countQuery = supabase.from("products_services").select("id", {
            count: "exact"
        }).in("business_id", validBusinessIds).eq("is_available", true);
        // Add product type filter if provided
        if (productType) {
            countQuery = countQuery.eq("product_type", productType);
        }
        // Add product name filter if provided
        if (productName && productName.trim().length > 0) {
            countQuery = countQuery.ilike("name", `%${productName.trim()}%`);
        }
        // Get total count
        const { count, error: countError } = await countQuery;
        if (countError) {
            console.error("Error counting products:", countError);
            return {
                error: "Failed to count products"
            };
        }
        // Build the query for fetching products from valid businesses
        let productsQuery = supabase.from("products_services").select(`
        id, business_id, name, description, base_price, discounted_price, product_type,
        is_available, image_url, created_at, updated_at, slug,
        business_profiles!business_id(business_slug)
      `).in("business_id", validBusinessIds).eq("is_available", true);
        // Add product type filter if provided
        if (productType) {
            productsQuery = productsQuery.eq("product_type", productType);
        }
        // Add product name filter if provided
        if (productName && productName.trim().length > 0) {
            productsQuery = productsQuery.ilike("name", `%${productName.trim()}%`);
        }
        // Add sorting
        const sortColumn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$types$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSortingColumn"])(sortBy, true); // true indicates product view
        const sortAscending = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$types$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSortingDirection"])(sortBy);
        // Special handling for price sorting to use discounted_price when available, otherwise base_price
        if (sortColumn === "price") {
            if (sortAscending) {
                productsQuery = productsQuery.order("discounted_price", {
                    ascending: true,
                    nullsFirst: false
                }).order("base_price", {
                    ascending: true,
                    nullsFirst: false
                });
            } else {
                productsQuery = productsQuery.order("discounted_price", {
                    ascending: false,
                    nullsFirst: false
                }).order("base_price", {
                    ascending: false,
                    nullsFirst: false
                });
            }
        } else {
            productsQuery = productsQuery.order(sortColumn, {
                ascending: sortAscending
            });
        }
        // Add pagination
        productsQuery = productsQuery.range(offset, offset + limit - 1);
        // Execute the query
        const { data: productsData, error: productsError } = await productsQuery;
        if (productsError) {
            console.error("Error fetching products:", productsError);
            return {
                error: "Failed to fetch products"
            };
        }
        // Process the products data to include business_slug
        const products = productsData.map(convertToNearbyProduct);
        // Calculate pagination info
        const totalCount = count || 0;
        const hasMore = totalCount > offset + products.length;
        const nextPage = hasMore ? page + 1 : null;
        return {
            data: {
                products,
                isAuthenticated,
                totalCount,
                hasMore,
                nextPage
            }
        };
    } catch (error) {
        console.error("Unexpected error in fetchAllProducts:", error);
        return {
            error: "An unexpected error occurred"
        };
    }
}
async function fetchProductsByBusinessIds(params) {
    const { businessIds, page = 1, limit = 20, sortBy = "created_desc", productType = null } = params;
    if (!businessIds || businessIds.length === 0) {
        return {
            error: "No business IDs provided"
        };
    }
    // Check Authentication
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { user } } = await supabase.auth.getUser();
    const isAuthenticated = !!user;
    try {
        const offset = (page - 1) * limit;
        // Filter the business IDs to only include online ones
        const { data: validBusinesses, error: businessError } = await supabase.from("business_profiles").select("id").in("id", businessIds).eq("status", "online");
        if (businessError) {
            console.error("Error filtering valid businesses:", businessError);
            return {
                error: "Failed to filter valid businesses"
            };
        }
        // If no valid businesses found, return empty result
        if (!validBusinesses || validBusinesses.length === 0) {
            return {
                data: {
                    products: [],
                    isAuthenticated,
                    totalCount: 0,
                    hasMore: false,
                    nextPage: null
                }
            };
        }
        // Get the IDs of valid businesses
        const validBusinessIds = validBusinesses.map((b)=>b.id);
        // Build the query for counting products
        let countQuery = supabase.from("products_services").select("id", {
            count: "exact"
        }).in("business_id", validBusinessIds).eq("is_available", true);
        // Add product type filter if provided
        if (productType) {
            countQuery = countQuery.eq("product_type", productType);
        }
        // Get total count
        const { count, error: countError } = await countQuery;
        if (countError) {
            console.error("Error counting products:", countError);
            return {
                error: "Failed to count products"
            };
        }
        // Build the query for fetching products
        let productsQuery = supabase.from("products_services").select(`
        id, business_id, name, description, base_price, discounted_price, product_type,
        is_available, image_url, created_at, updated_at, slug,
        business_profiles!business_id(business_slug)
      `).in("business_id", validBusinessIds).eq("is_available", true);
        // Add product type filter if provided
        if (productType) {
            productsQuery = productsQuery.eq("product_type", productType);
        }
        // Add sorting
        const sortColumn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$types$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSortingColumn"])(sortBy, true); // true indicates product view
        const sortAscending = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$types$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSortingDirection"])(sortBy);
        // Special handling for price sorting to use discounted_price when available, otherwise base_price
        if (sortColumn === "price") {
            if (sortAscending) {
                productsQuery = productsQuery.order("discounted_price", {
                    ascending: true,
                    nullsFirst: false
                }).order("base_price", {
                    ascending: true,
                    nullsFirst: false
                });
            } else {
                productsQuery = productsQuery.order("discounted_price", {
                    ascending: false,
                    nullsFirst: false
                }).order("base_price", {
                    ascending: false,
                    nullsFirst: false
                });
            }
        } else {
            productsQuery = productsQuery.order(sortColumn, {
                ascending: sortAscending
            });
        }
        // Add pagination
        productsQuery = productsQuery.range(offset, offset + limit - 1);
        // Execute the query
        const { data: productsData, error: productsError } = await productsQuery;
        if (productsError) {
            console.error("Error fetching products:", productsError);
            return {
                error: "Failed to fetch products"
            };
        }
        // Process the products data to include business_slug
        const products = productsData.map(convertToNearbyProduct);
        // Calculate pagination info
        const totalCount = count || 0;
        const hasMore = totalCount > offset + products.length;
        const nextPage = hasMore ? page + 1 : null;
        return {
            data: {
                products,
                isAuthenticated,
                totalCount,
                hasMore,
                nextPage
            }
        };
    } catch (error) {
        console.error("Unexpected error in fetchProductsByBusinessIds:", error);
        return {
            error: "An unexpected error occurred"
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    fetchMoreProductsCombined,
    fetchAllProducts,
    fetchProductsByBusinessIds
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(fetchMoreProductsCombined, "40cf076a0ff37f204cc8708187f4777e00ee8b9608", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(fetchAllProducts, "408078c315b7b0094b642ab831ed7eb03d4fd98823", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(fetchProductsByBusinessIds, "407927b1266213834749c44990bf37df557f7f8bdd", null);
}}),
"[project]/lib/schemas/locationSchemas.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "businessNameSchema": (()=>businessNameSchema),
    "businessNameSearchSchema": (()=>businessNameSearchSchema),
    "citySchema": (()=>citySchema),
    "combinedSearchParamsSchema": (()=>combinedSearchParamsSchema),
    "combinedSearchSchema": (()=>combinedSearchSchema),
    "discoverySearchSchema": (()=>discoverySearchSchema),
    "paginationSchema": (()=>paginationSchema),
    "pincodeSchema": (()=>pincodeSchema),
    "sortingSchema": (()=>sortingSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
;
const pincodeSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    pincode: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])().regex(/^\d{6}$/, {
        message: "Pincode must be exactly 6 digits."
    }),
    locality: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])().optional().nullable()
});
const citySchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    city: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])().min(2, {
        message: "City name must be at least 2 characters."
    }),
    locality: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])().optional().nullable()
});
const businessNameSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    businessName: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])().min(1, {
        message: "Business name is required."
    })
});
const combinedSearchSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    businessName: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])().optional().nullable(),
    pincode: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])().regex(/^\d{6}$/, {
        message: "Pincode must be exactly 6 digits."
    }).optional().nullable(),
    city: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])().optional().nullable(),
    locality: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])().optional().nullable(),
    category: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])().optional().nullable()
});
const paginationSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    page: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["number"])().int().positive().default(1),
    limit: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["number"])().int().positive().max(50).default(20)
});
const sortingSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    sortBy: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["enum"])([
        "name_asc",
        "name_desc",
        "created_asc",
        "created_desc",
        "likes_asc",
        "likes_desc",
        "subscriptions_asc",
        "subscriptions_desc",
        "rating_asc",
        "rating_desc"
    ]).default("created_desc")
});
const discoverySearchSchema = pincodeSchema.extend({
    viewType: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["enum"])([
        "cards",
        "products"
    ]),
    ...paginationSchema.shape,
    ...sortingSchema.shape
});
const businessNameSearchSchema = businessNameSchema.extend({
    viewType: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["enum"])([
        "cards",
        "products"
    ]),
    ...paginationSchema.shape,
    ...sortingSchema.shape
});
const combinedSearchParamsSchema = combinedSearchSchema.extend({
    viewType: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["enum"])([
        "cards",
        "products"
    ]),
    ...paginationSchema.shape,
    ...sortingSchema.shape
});
}}),
"[project]/lib/actions/location.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"401d1fd9f2f483c3d17b0c79ea53a43a19793352e1":"getPincodeDetails","403f4656d49c43a015e25f42887ab1ce4e06ea3a53":"getCitySuggestions","40bb735e4a188117e42a6ba3dc5d21bf53fa28abb9":"getCityDetails"},"",""] */ __turbopack_context__.s({
    "getCityDetails": (()=>getCityDetails),
    "getCitySuggestions": (()=>getCitySuggestions),
    "getPincodeDetails": (()=>getPincodeDetails)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
async function getPincodeDetails(pincode) {
    if (!pincode || !/^\d{6}$/.test(pincode)) {
        return {
            error: "Invalid Pincode format."
        };
    }
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        // First get city and state from pincodes table
        const { data: pincodeData, error: pincodeError } = await supabase.from("pincodes").select("OfficeName, DivisionName, StateName").eq("Pincode", pincode) // Updated column name to match database
        .order("OfficeName");
        if (pincodeError) {
            console.error("Pincode Fetch Error:", pincodeError);
            return {
                error: "Database error fetching pincode details."
            };
        }
        if (!pincodeData || pincodeData.length === 0) {
            return {
                error: "Pincode not found."
            };
        }
        // State names are already in title case format in the database
        const state = pincodeData[0].StateName;
        // Use DivisionName as the city (already cleaned)
        const city = pincodeData[0].DivisionName;
        // Get unique localities from post office names
        const localities = [
            ...new Set(pincodeData.map((item)=>item.OfficeName))
        ];
        return {
            data: {
                city,
                state,
                localities
            },
            city,
            state,
            localities
        };
    } catch (e) {
        console.error("Pincode Lookup Exception:", e);
        return {
            error: "An unexpected error occurred during pincode lookup."
        };
    }
}
async function getCityDetails(city) {
    if (!city || city.length < 2) {
        return {
            error: "City name must be at least 2 characters."
        };
    }
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        // Get pincodes and state for the city - DivisionName is the city column
        const { data: cityData, error: cityError } = await supabase.from("pincodes").select("Pincode, OfficeName, StateName, DivisionName").ilike("DivisionName", `%${city}%`).order("Pincode");
        if (cityError) {
            console.error("City Fetch Error:", cityError);
            return {
                error: "Database error fetching city details."
            };
        }
        if (!cityData || cityData.length === 0) {
            return {
                error: "City not found."
            };
        }
        // State names are already in title case format in the database
        const state = cityData[0].StateName;
        // Get unique pincodes
        const pincodes = [
            ...new Set(cityData.map((item)=>item.Pincode))
        ];
        // Get unique localities from post office names
        const localities = [
            ...new Set(cityData.map((item)=>item.OfficeName))
        ];
        return {
            data: {
                pincodes,
                state,
                localities
            },
            pincodes,
            state,
            localities
        };
    } catch (e) {
        console.error("City Lookup Exception:", e);
        return {
            error: "An unexpected error occurred during city lookup."
        };
    }
}
async function getCitySuggestions(query) {
    if (!query || query.length < 2) {
        return {
            error: "Query must be at least 2 characters."
        };
    }
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        // Use the PostgreSQL function to get distinct cities (up to 5)
        const { data: cityData, error: cityError } = await supabase.rpc('get_distinct_cities', {
            search_query: `%${query}%`,
            result_limit: 5
        });
        if (cityError) {
            console.error("City Suggestions Error:", cityError);
            // Fallback to regular query if RPC fails
            try {
                // Use a regular query as fallback
                const { data: fallbackData, error: fallbackError } = await supabase.from("pincodes").select("DivisionName").ilike("DivisionName", `%${query}%`).order("DivisionName").limit(100);
                if (fallbackError) {
                    throw fallbackError;
                }
                if (!fallbackData || fallbackData.length === 0) {
                    return {
                        data: {
                            cities: []
                        },
                        cities: []
                    };
                }
                // Get unique cities and format them
                const cities = [
                    ...new Set(fallbackData.map((item)=>item.DivisionName.toLowerCase().replace(/\b\w/g, (char)=>char.toUpperCase())))
                ];
                const topCities = cities.slice(0, 5);
                return {
                    data: {
                        cities: topCities
                    },
                    cities: topCities
                };
            } catch (fallbackErr) {
                console.error("Fallback City Query Error:", fallbackErr);
                return {
                    error: "Database error fetching city suggestions."
                };
            }
        }
        if (!cityData || cityData.length === 0) {
            return {
                data: {
                    cities: []
                },
                cities: []
            };
        }
        // Format the city names to Title Case
        const cities = cityData.map((item)=>item.city.toLowerCase().replace(/\b\w/g, (char)=>char.toUpperCase()));
        return {
            data: {
                cities
            },
            cities
        };
    } catch (e) {
        console.error("City Suggestions Exception:", e);
        return {
            error: "An unexpected error occurred while fetching city suggestions."
        };
    }
} // --- End City Autocomplete ---
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getPincodeDetails,
    getCityDetails,
    getCitySuggestions
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getPincodeDetails, "401d1fd9f2f483c3d17b0c79ea53a43a19793352e1", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getCityDetails, "40bb735e4a188117e42a6ba3dc5d21bf53fa28abb9", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getCitySuggestions, "403f4656d49c43a015e25f42887ab1ce4e06ea3a53", null);
}}),
"[project]/app/(main)/discover/actions/locationActions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"407cef5b191e86fbfe33be0a363e12afa4ddd8e2c3":"searchDiscoverData"},"",""] */ __turbopack_context__.s({
    "searchDiscoverData": (()=>searchDiscoverData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$schemas$2f$locationSchemas$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/schemas/locationSchemas.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/location.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/index.ts [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/discovery.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$types$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/discover/actions/types.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
async function searchDiscoverData(params) {
    const { pincode, city, locality, viewType, page = 1, limit = 20, sortBy = "created_desc", productType = null, category = null } = params;
    // Check if we have either pincode or city
    if (!pincode && !city) {
        return {
            error: "Either pincode or city is required."
        };
    }
    // Initialize Supabase clients early
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    let locationCity;
    let locationState;
    let validPincodes = [];
    // Handle pincode-based search
    if (pincode) {
        // 1. Validate Pincode
        const validatedPincode = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$schemas$2f$locationSchemas$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pincodeSchema"].safeParse({
            pincode
        });
        if (!validatedPincode.success) {
            return {
                error: "Invalid Pincode format. Must be 6 digits."
            };
        }
        const validPincode = validatedPincode.data.pincode;
        // 2. Get Location Details
        const locationDetails = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getPincodeDetails"])(validPincode);
        if (locationDetails.error || !locationDetails.city || !locationDetails.state) {
            return {
                error: locationDetails.error || "Pincode not found."
            };
        }
        locationCity = locationDetails.city;
        locationState = locationDetails.state;
        validPincodes = [
            validPincode
        ];
    } else if (city) {
        // 1. Validate City
        const validatedCity = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$schemas$2f$locationSchemas$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["citySchema"].safeParse({
            city
        });
        if (!validatedCity.success) {
            return {
                error: "Invalid city name. Must be at least 2 characters."
            };
        }
        const validCity = validatedCity.data.city;
        // For city-based search, we'll directly filter by the city column
        // No need to get pincodes or other location details
        locationCity = validCity;
        // Set empty pincodes array to indicate we're doing a direct city search
        validPincodes = [];
        // Try to get the state for display purposes only
        try {
            const { data } = await supabase.from("pincodes").select("StateName").ilike("DivisionName", `%${validCity}%`).limit(1);
            if (data && data.length > 0) {
                locationState = data[0].StateName;
            } else {
                locationState = ""; // Default empty state if not found
            }
        } catch (error) {
            console.error("Error getting state for city:", error);
            locationState = ""; // Default empty state on error
        }
    } else {
        return {
            error: "Either pincode or city is required."
        };
    }
    // 3. Check Authentication
    const { data: { user } } = await supabase.auth.getUser();
    const isAuthenticated = !!user;
    try {
        // Add a small delay to prevent infinite loops
        await new Promise((resolve)=>setTimeout(resolve, 100));
        // 4. Build Base Query for Valid Businesses
        // Reference code removed
        // 5. Fetch Data Based on viewType
        if (viewType === "cards") {
            // Check if we're searching by city directly
            if (city && validPincodes.length === 0) {
                // Direct city-based search using Supabase
                const offset = (page - 1) * limit;
                // Define fields to select
                const businessFields = `
          id, business_name, logo_url, member_name, title,
          address_line, city, state, pincode, locality, phone, business_category, instagram_url,
          facebook_url, whatsapp_number, about_bio, status, business_slug,
          delivery_info, total_likes, total_subscriptions, average_rating, business_hours,
          created_at, updated_at, contact_email
        `;
                // Get count of businesses matching the city
                let countQuery = supabase.from("business_profiles").select("id", {
                    count: "exact"
                }).eq("city", city) // Use exact matching for city
                .eq("status", "online");
                // Add category filter if provided
                if (category && category.trim()) {
                    countQuery = countQuery.eq("business_category", category.trim());
                }
                const { count, error: countError } = await countQuery;
                if (countError) {
                    console.error("City Business Count Error:", countError);
                    return {
                        error: "Database error counting businesses by city."
                    };
                }
                // Fetch businesses matching the city
                let businessQuery = supabase.from("business_profiles").select(businessFields).eq("city", city) // Use exact matching for city
                .eq("status", "online").range(offset, offset + limit - 1).order((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$types$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSortingColumn"])(sortBy), {
                    ascending: (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$types$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSortingDirection"])(sortBy)
                });
                // Add category filter if provided
                if (category && category.trim()) {
                    businessQuery = businessQuery.eq("business_category", category.trim());
                }
                const { data, error } = await businessQuery;
                if (error) {
                    console.error("City Business Query Error:", error);
                    return {
                        error: "Database error fetching businesses by city."
                    };
                }
                // Map the data to the expected format (no subscription data needed)
                const businessesData = data;
                return {
                    data: {
                        location: {
                            city: locationCity,
                            state: locationState
                        },
                        businesses: businessesData.map((data)=>({
                                id: data.id,
                                business_name: data.business_name ?? "",
                                contact_email: "",
                                created_at: data.created_at ?? undefined,
                                updated_at: data.updated_at ?? undefined,
                                logo_url: data.logo_url ?? "",
                                member_name: data.member_name ?? "",
                                title: data.title ?? "",
                                address_line: data.address_line ?? "",
                                city: data.city ?? "",
                                state: data.state ?? "",
                                pincode: data.pincode ?? "",
                                locality: data.locality ?? "",
                                phone: data.phone ?? "",
                                business_category: data.business_category ?? "",
                                instagram_url: data.instagram_url ?? "",
                                facebook_url: data.facebook_url ?? "",
                                whatsapp_number: data.whatsapp_number ?? "",
                                about_bio: data.about_bio ?? "",
                                status: data.status === "online" ? "online" : "offline",
                                business_slug: data.business_slug ?? "",
                                total_likes: data.total_likes ?? 0,
                                total_subscriptions: data.total_subscriptions ?? 0,
                                average_rating: data.average_rating ?? 0,
                                theme_color: data.theme_color ?? "#D4AF37",
                                delivery_info: data.delivery_info ?? "",
                                business_hours: data.business_hours,
                                established_year: null,
                                website_url: "",
                                linkedin_url: "",
                                twitter_url: "",
                                youtube_url: "",
                                call_number: ""
                            })),
                        isAuthenticated: isAuthenticated,
                        totalCount: count || 0,
                        hasMore: (count || 0) > page * limit,
                        nextPage: (count || 0) > page * limit ? page + 1 : null
                    }
                };
            }
            // Use the secure method to fetch business profiles by pincode
            const { data: businessesData, count, error: businessesError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSecureBusinessProfilesForDiscover"])(validPincodes, locality, page, limit, sortBy);
            if (businessesError) {
                console.error("Search Discover (Cards) Error:", businessesError);
                return {
                    error: businessesError
                };
            }
            const totalCount = count || 0;
            // Calculate if there are more pages
            const hasMore = totalCount > page * limit;
            const nextPage = hasMore ? page + 1 : null;
            // Map raw data to BusinessCardData, handling potential nulls
            const businesses = businessesData?.map((data)=>{
                // Use the actual data from the database
                return {
                    id: data.id,
                    business_name: data.business_name ?? "",
                    contact_email: "",
                    // Subscription fields removed - all users now have access to all features
                    created_at: data.created_at ?? undefined,
                    updated_at: data.updated_at ?? undefined,
                    logo_url: data.logo_url ?? "",
                    member_name: data.member_name ?? "",
                    title: data.title ?? "",
                    address_line: data.address_line ?? "",
                    city: data.city ?? "",
                    state: data.state ?? "",
                    pincode: data.pincode ?? "",
                    locality: data.locality ?? "",
                    phone: data.phone ?? "",
                    business_category: data.business_category ?? "",
                    instagram_url: data.instagram_url ?? "",
                    facebook_url: data.facebook_url ?? "",
                    whatsapp_number: data.whatsapp_number ?? "",
                    about_bio: data.about_bio ?? "",
                    status: data.status === "online" ? "online" : "offline",
                    business_slug: data.business_slug ?? "",
                    // Include metrics data
                    total_likes: data.total_likes ?? 0,
                    total_subscriptions: data.total_subscriptions ?? 0,
                    average_rating: data.average_rating ?? 0,
                    // Use actual data if available, otherwise use defaults
                    // theme_color field removed - using default styling
                    delivery_info: data.delivery_info ?? "",
                    business_hours: data.business_hours,
                    established_year: data.established_year ?? null,
                    // Add default values for fields required by BusinessCardData but not in our query
                    website_url: "",
                    linkedin_url: "",
                    twitter_url: "",
                    youtube_url: "",
                    call_number: ""
                };
            }) ?? [];
            return {
                data: {
                    location: {
                        city: locationCity,
                        state: locationState
                    },
                    businesses: businesses,
                    isAuthenticated: isAuthenticated,
                    totalCount,
                    hasMore,
                    nextPage
                }
            };
        } else {
            // viewType === 'products'
            let validBusinessIds = [];
            // Check if we're searching by city directly
            if (city && validPincodes.length === 0) {
                // Direct city-based search using Supabase
                // Get business IDs matching the city
                let businessIdsQuery = supabase.from("business_profiles").select("id").eq("city", city) // Use exact matching for city
                .eq("status", "online");
                // Add category filter if provided
                if (category && category.trim()) {
                    businessIdsQuery = businessIdsQuery.eq("business_category", category.trim());
                }
                const { data, error } = await businessIdsQuery;
                if (error) {
                    console.error("City Business IDs Error:", error);
                    return {
                        error: "Database error fetching business IDs by city."
                    };
                }
                validBusinessIds = data.map((item)=>item.id);
            } else {
                // First, get IDs of valid businesses using the secure method
                const { data: ids, error: validBusinessesError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSecureBusinessProfileIdsForDiscover"])(validPincodes, locality, sortBy);
                if (validBusinessesError) {
                    console.error("Search Discover (Product IDs) Error:", validBusinessesError);
                    return {
                        error: validBusinessesError
                    };
                }
                validBusinessIds = ids || [];
            }
            // This check is now handled inside the else block above
            if (!validBusinessIds || validBusinessIds.length === 0) {
                // No valid businesses found, return empty results
                return {
                    data: {
                        location: {
                            city: locationCity,
                            state: locationState
                        },
                        products: [],
                        isAuthenticated: isAuthenticated,
                        totalCount: 0,
                        hasMore: false,
                        nextPage: null
                    }
                };
            }
            // Get total count of products first
            let countQuery = supabase.from("products_services").select("id", {
                count: "exact"
            }).in("business_id", validBusinessIds || []).eq("is_available", true);
            // Add product type filter if provided
            if (productType) {
                countQuery = countQuery.eq("product_type", productType);
            }
            const { count: totalProductCount, error: productCountError } = await countQuery;
            if (productCountError) {
                console.error("Search Discover (Product Count) Error:", productCountError);
                return {
                    error: "Database error counting products."
                };
            }
            // Calculate pagination
            const from = (page - 1) * limit;
            const to = from + limit - 1;
            const totalCount = totalProductCount || 0;
            const hasMore = totalCount > page * limit;
            const nextPage = hasMore ? page + 1 : null;
            // Fetch Products belonging to valid businesses with pagination
            // Build the query for products
            let productsQuery = supabase.from("products_services").select(// Select required fields + business_slug
            `
          id, business_id, name, description, base_price, discounted_price, product_type,
          is_available, image_url, created_at, updated_at, slug,
          business_profiles!business_id(business_slug)
        `).in("business_id", validBusinessIds || []) // Filter products by valid business IDs
            .eq("is_available", true);
            // Add product type filter if provided
            if (productType) {
                productsQuery = productsQuery.eq("product_type", productType);
            }
            // Add pagination
            productsQuery = productsQuery.range(from, to);
            // Apply sorting based on the sortBy parameter
            const sortColumn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$types$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSortingColumn"])(sortBy, true); // true indicates product view
            const sortAscending = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$types$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSortingDirection"])(sortBy);
            // Special handling for price sorting to use discounted_price when available, otherwise base_price
            if (sortColumn === "price") {
                if (sortAscending) {
                    productsQuery = productsQuery.order("discounted_price", {
                        ascending: true,
                        nullsFirst: false
                    }).order("base_price", {
                        ascending: true,
                        nullsFirst: false
                    });
                } else {
                    productsQuery = productsQuery.order("discounted_price", {
                        ascending: false,
                        nullsFirst: false
                    }).order("base_price", {
                        ascending: false,
                        nullsFirst: false
                    });
                }
            } else {
                productsQuery = productsQuery.order(sortColumn, {
                    ascending: sortAscending
                });
            }
            const { data: productsData, error: productsError } = await productsQuery;
            if (productsError) {
                console.error("Search Discover (Products) Error:", productsError);
                return {
                    error: "Database error fetching nearby products."
                };
            }
            // Process products to match the full ProductServiceData structure + business_slug
            const products = productsData?.map((p)=>{
                // Extract business_slug from the joined business_profiles
                let business_slug = null;
                if (p.business_profiles) {
                    // Check if it's an array or an object
                    if (Array.isArray(p.business_profiles) && p.business_profiles.length > 0) {
                        business_slug = p.business_profiles[0].business_slug;
                    } else if (typeof p.business_profiles === "object" && p.business_profiles !== null) {
                        // Cast to a more specific type to handle different response formats
                        business_slug = p.business_profiles.business_slug;
                    }
                }
                // Ensure we have a valid business_slug
                const product = {
                    id: p.id,
                    business_id: p.business_id ?? undefined,
                    name: p.name ?? "",
                    description: p.description ?? "",
                    base_price: p.base_price ?? 0,
                    discounted_price: p.discounted_price ?? null,
                    product_type: p.product_type ?? "physical",
                    is_available: p.is_available ?? true,
                    image_url: p.image_url,
                    created_at: p.created_at || undefined,
                    updated_at: p.updated_at || undefined,
                    business_slug: business_slug,
                    featured_image_index: 0,
                    images: [],
                    slug: p.slug || undefined
                };
                return product;
            }) ?? [];
            return {
                data: {
                    location: {
                        city: locationCity,
                        state: locationState
                    },
                    products: products,
                    isAuthenticated: isAuthenticated,
                    totalCount,
                    hasMore,
                    nextPage
                }
            };
        }
    } catch (e) {
        console.error("Search Discover Exception:", e);
        return {
            error: "An unexpected error occurred during the search."
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    searchDiscoverData
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(searchDiscoverData, "407cef5b191e86fbfe33be0a363e12afa4ddd8e2c3", null);
}}),
"[project]/app/(main)/discover/actions/combinedActions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"40233faece1b387c15157ff9feaa5d282a77135da8":"searchDiscoverCombined"},"",""] */ __turbopack_context__.s({
    "searchDiscoverCombined": (()=>searchDiscoverCombined)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$businessActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$locationActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/discover/actions/locationActions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
async function searchDiscoverCombined(params) {
    const { businessName, productName, pincode, city, locality, category, viewType, page = 1, limit = viewType === "products" ? 20 : 5, businessSort = "created_desc", productSort = "created_desc", productType = null } = params;
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    // Check Authentication
    const { data: { user } } = await supabase.auth.getUser();
    const isAuthenticated = !!user;
    try {
        // Case 1: Search by product name (only for products view)
        if (viewType === "products" && productName && productName.trim().length > 0) {
            // Fetch products by name
            const productsResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchAllProducts"])({
                page,
                limit,
                sortBy: productSort,
                productType,
                pincode,
                locality,
                productName,
                category
            });
            if (productsResult.error) {
                return {
                    error: productsResult.error
                };
            }
            return {
                data: {
                    products: productsResult.data?.products || [],
                    isAuthenticated,
                    totalCount: productsResult.data?.totalCount || 0,
                    hasMore: productsResult.data?.hasMore || false,
                    nextPage: productsResult.data?.nextPage || null
                }
            };
        } else if (businessName && businessName.trim().length > 0) {
            // First, search for businesses by name
            const businessResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$businessActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchBusinessesBySearch"])({
                businessName,
                pincode,
                locality,
                page,
                limit,
                sortBy: businessSort,
                category
            });
            if (businessResult.error) {
                return {
                    error: businessResult.error
                };
            }
            if (viewType === "cards") {
                // Return businesses directly
                return {
                    data: {
                        businesses: businessResult.data?.businesses || [],
                        isAuthenticated,
                        totalCount: businessResult.data?.totalCount || 0,
                        hasMore: businessResult.data?.hasMore || false,
                        nextPage: businessResult.data?.nextPage || null
                    }
                };
            } else {
                // viewType === "products"
                // Get business IDs from the search results
                const businessIds = businessResult.data?.businesses.map((business)=>business.id);
                if (!businessIds || businessIds.length === 0) {
                    return {
                        data: {
                            products: [],
                            isAuthenticated,
                            totalCount: 0,
                            hasMore: false,
                            nextPage: null
                        }
                    };
                }
                // Fetch products for these businesses
                const productsResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchProductsByBusinessIds"])({
                    businessIds: businessIds,
                    page,
                    limit,
                    sortBy: productSort,
                    productType
                });
                if (productsResult.error) {
                    return {
                        error: productsResult.error
                    };
                }
                return {
                    data: {
                        products: productsResult.data?.products || [],
                        isAuthenticated,
                        totalCount: productsResult.data?.totalCount || 0,
                        hasMore: productsResult.data?.hasMore || false,
                        nextPage: productsResult.data?.nextPage || null
                    }
                };
            }
        } else if (pincode || city) {
            return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$locationActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["searchDiscoverData"])({
                pincode: pincode || undefined,
                city: city || undefined,
                locality,
                viewType,
                page,
                limit,
                sortBy: viewType === "products" ? productSort : businessSort,
                productType: viewType === "products" ? productType : null,
                category
            });
        } else {
            if (viewType === "cards") {
                // Fetch all businesses
                const businessResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$businessActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchBusinessesBySearch"])({
                    page,
                    limit,
                    sortBy: businessSort,
                    category
                });
                if (businessResult.error) {
                    return {
                        error: businessResult.error
                    };
                }
                return {
                    data: {
                        businesses: businessResult.data?.businesses || [],
                        isAuthenticated,
                        totalCount: businessResult.data?.totalCount || 0,
                        hasMore: businessResult.data?.hasMore || false,
                        nextPage: businessResult.data?.nextPage || null
                    }
                };
            } else {
                // viewType === "products"
                // Fetch all products
                const productsResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchAllProducts"])({
                    page,
                    limit,
                    sortBy: productSort,
                    productType,
                    pincode: pincode || undefined,
                    locality,
                    category
                });
                if (productsResult.error) {
                    return {
                        error: productsResult.error
                    };
                }
                return {
                    data: {
                        products: productsResult.data?.products || [],
                        isAuthenticated,
                        totalCount: productsResult.data?.totalCount || 0,
                        hasMore: productsResult.data?.hasMore || false,
                        nextPage: productsResult.data?.nextPage || null
                    }
                };
            }
        }
    } catch (e) {
        console.error("Search Discover Combined Exception:", e);
        return {
            error: "An unexpected error occurred during the search."
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    searchDiscoverCombined
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(searchDiscoverCombined, "40233faece1b387c15157ff9feaa5d282a77135da8", null);
}}),
"[project]/.next-internal/server/app/(main)/discover/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/discover/actions/combinedActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/location.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$combinedActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/discover/actions/combinedActions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/location.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$businessActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)");
;
;
;
;
}}),
"[project]/.next-internal/server/app/(main)/discover/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/discover/actions/combinedActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/location.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$combinedActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/discover/actions/combinedActions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/location.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$businessActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$main$292f$discover$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$combinedActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$businessActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(main)/discover/page/actions.js { ACTIONS_MODULE0 => "[project]/app/(main)/discover/actions/combinedActions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/lib/actions/location.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE3 => "[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/(main)/discover/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/discover/actions/combinedActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/location.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "401d1fd9f2f483c3d17b0c79ea53a43a19793352e1": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getPincodeDetails"]),
    "40233faece1b387c15157ff9feaa5d282a77135da8": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$combinedActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["searchDiscoverCombined"]),
    "4059dac56ec2d4fa83cae240560bfebb118fd5640f": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$businessActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchMoreBusinessCardsCombined"]),
    "40cf076a0ff37f204cc8708187f4777e00ee8b9608": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchMoreProductsCombined"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$combinedActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/discover/actions/combinedActions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/location.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$businessActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$main$292f$discover$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$combinedActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$businessActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(main)/discover/page/actions.js { ACTIONS_MODULE0 => "[project]/app/(main)/discover/actions/combinedActions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/lib/actions/location.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE3 => "[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/(main)/discover/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/discover/actions/combinedActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/location.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "401d1fd9f2f483c3d17b0c79ea53a43a19793352e1": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$main$292f$discover$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$combinedActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$businessActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["401d1fd9f2f483c3d17b0c79ea53a43a19793352e1"]),
    "40233faece1b387c15157ff9feaa5d282a77135da8": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$main$292f$discover$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$combinedActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$businessActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40233faece1b387c15157ff9feaa5d282a77135da8"]),
    "4059dac56ec2d4fa83cae240560bfebb118fd5640f": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$main$292f$discover$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$combinedActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$businessActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["4059dac56ec2d4fa83cae240560bfebb118fd5640f"]),
    "40cf076a0ff37f204cc8708187f4777e00ee8b9608": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$main$292f$discover$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$combinedActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$businessActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40cf076a0ff37f204cc8708187f4777e00ee8b9608"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$main$292f$discover$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$combinedActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$businessActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(main)/discover/page/actions.js { ACTIONS_MODULE0 => "[project]/app/(main)/discover/actions/combinedActions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/lib/actions/location.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE3 => "[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <module evaluation>');
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$main$292f$discover$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$combinedActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$businessActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$discover$2f$actions$2f$productActions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(main)/discover/page/actions.js { ACTIONS_MODULE0 => "[project]/app/(main)/discover/actions/combinedActions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/lib/actions/location.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE3 => "[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <exports>');
}}),
"[project]/app/favicon.ico.mjs { IMAGE => \"[project]/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/favicon.ico.mjs { IMAGE => \"[project]/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/app/opengraph-image.png.mjs { IMAGE => \"[project]/app/opengraph-image.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/opengraph-image.png.mjs { IMAGE => \"[project]/app/opengraph-image.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/app/(main)/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/(main)/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/app/(main)/discover/ModernDiscoverClient.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/(main)/discover/ModernDiscoverClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/(main)/discover/ModernDiscoverClient.tsx <module evaluation>", "default");
}}),
"[project]/app/(main)/discover/ModernDiscoverClient.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/(main)/discover/ModernDiscoverClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/(main)/discover/ModernDiscoverClient.tsx", "default");
}}),
"[project]/app/(main)/discover/ModernDiscoverClient.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$ModernDiscoverClient$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/app/(main)/discover/ModernDiscoverClient.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$ModernDiscoverClient$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/app/(main)/discover/ModernDiscoverClient.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$ModernDiscoverClient$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/app/(main)/discover/ModernResultsSkeleton.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/(main)/discover/ModernResultsSkeleton.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/(main)/discover/ModernResultsSkeleton.tsx <module evaluation>", "default");
}}),
"[project]/app/(main)/discover/ModernResultsSkeleton.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/(main)/discover/ModernResultsSkeleton.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/(main)/discover/ModernResultsSkeleton.tsx", "default");
}}),
"[project]/app/(main)/discover/ModernResultsSkeleton.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$ModernResultsSkeleton$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/app/(main)/discover/ModernResultsSkeleton.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$ModernResultsSkeleton$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/app/(main)/discover/ModernResultsSkeleton.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$ModernResultsSkeleton$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/app/(main)/discover/constants/urlParamConstants.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Constants for URL parameters in the discover page
 */ // Business tab URL parameters
__turbopack_context__.s({
    "BUSINESS_FILTER_PARAM": (()=>BUSINESS_FILTER_PARAM),
    "BUSINESS_NAME_PARAM": (()=>BUSINESS_NAME_PARAM),
    "BUSINESS_SORT_PARAM": (()=>BUSINESS_SORT_PARAM),
    "CATEGORY_PARAM": (()=>CATEGORY_PARAM),
    "CITY_PARAM": (()=>CITY_PARAM),
    "LOCALITY_PARAM": (()=>LOCALITY_PARAM),
    "PINCODE_PARAM": (()=>PINCODE_PARAM),
    "PRODUCT_NAME_PARAM": (()=>PRODUCT_NAME_PARAM),
    "PRODUCT_SORT_PARAM": (()=>PRODUCT_SORT_PARAM),
    "PRODUCT_TYPE_PARAM": (()=>PRODUCT_TYPE_PARAM),
    "VIEW_TYPE_PARAM": (()=>VIEW_TYPE_PARAM)
});
const BUSINESS_NAME_PARAM = "businessName";
const BUSINESS_SORT_PARAM = "businessSort";
const BUSINESS_FILTER_PARAM = "businessFilter";
const PRODUCT_NAME_PARAM = "productName";
const PRODUCT_SORT_PARAM = "productSort";
const PRODUCT_TYPE_PARAM = "productType";
const PINCODE_PARAM = "pincode";
const CITY_PARAM = "city";
const LOCALITY_PARAM = "locality";
const VIEW_TYPE_PARAM = "view";
const CATEGORY_PARAM = "category";
}}),
"[project]/app/(main)/discover/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>DiscoverPage),
    "generateMetadata": (()=>generateMetadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$ModernDiscoverClient$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/discover/ModernDiscoverClient.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$ModernResultsSkeleton$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/discover/ModernResultsSkeleton.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$constants$2f$urlParamConstants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/discover/constants/urlParamConstants.ts [app-rsc] (ecmascript)");
;
;
;
;
;
async function generateMetadata() {
    const title = "Discover Local Businesses & Products";
    const description = "Find and explore local businesses and products using Dukancard. Search by pincode, locality, or business name to discover shops, services, products, and professionals near you in India.";
    const siteUrl = ("TURBOPACK compile-time value", "http://localhost:3000") || "https://dukancard.in";
    const pageUrl = `${siteUrl}/discover`;
    const ogImage = `${siteUrl}/opengraph-image.png`; // Default OG image
    return {
        title,
        description,
        keywords: [
            "discover local business",
            "find shops near me",
            "search business by pincode",
            "search business by name",
            "local business directory India",
            "Dukancard discover",
            "nearby services",
            "local products",
            "search by locality",
            "business cards",
            "products and services",
            "infinite scroll"
        ],
        alternates: {
            canonical: "/discover"
        },
        openGraph: {
            title: title,
            description: description,
            url: pageUrl,
            siteName: "Dukancard",
            type: "website",
            locale: "en_IN",
            images: [
                {
                    url: ogImage,
                    width: 1200,
                    height: 630,
                    alt: "Discover Local Businesses on Dukancard"
                }
            ]
        },
        twitter: {
            card: "summary_large_image",
            title: title,
            description: description,
            images: [
                ogImage
            ]
        },
        // Add WebPage Schema with SearchAction
        other: {
            "application-ld+json": JSON.stringify({
                "@context": "https://schema.org",
                "@type": "WebPage",
                name: title,
                description: description,
                url: pageUrl,
                isPartOf: {
                    "@type": "WebSite",
                    name: "Dukancard",
                    url: siteUrl
                },
                // Add SearchAction for structured data
                potentialAction: [
                    {
                        "@type": "SearchAction",
                        target: {
                            "@type": "EntryPoint",
                            urlTemplate: `${siteUrl}/discover?${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$constants$2f$urlParamConstants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PINCODE_PARAM"]}={pincode}`
                        },
                        "query-input": "required name=pincode",
                        description: "Search for businesses and products by pincode"
                    },
                    {
                        "@type": "SearchAction",
                        target: {
                            "@type": "EntryPoint",
                            urlTemplate: `${siteUrl}/discover?${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$constants$2f$urlParamConstants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BUSINESS_NAME_PARAM"]}={businessName}`
                        },
                        "query-input": "required name=businessName",
                        description: "Search for businesses by name"
                    }
                ],
                // Add breadcrumb for better navigation structure
                breadcrumb: {
                    "@type": "BreadcrumbList",
                    itemListElement: [
                        {
                            "@type": "ListItem",
                            position: 1,
                            name: "Home",
                            item: siteUrl
                        },
                        {
                            "@type": "ListItem",
                            position: 2,
                            name: "Discover",
                            item: pageUrl
                        }
                    ]
                }
            })
        }
    };
}
// Server Component - Handles initial rendering
async function DiscoverPageContent() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$ModernDiscoverClient$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
        fileName: "[project]/app/(main)/discover/page.tsx",
        lineNumber: 120,
        columnNumber: 10
    }, this);
}
function DiscoverPage() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-white dark:bg-black",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Suspense"], {
            fallback: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$discover$2f$ModernResultsSkeleton$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/app/(main)/discover/page.tsx",
                lineNumber: 127,
                columnNumber: 27
            }, void 0),
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(DiscoverPageContent, {}, void 0, false, {
                fileName: "[project]/app/(main)/discover/page.tsx",
                lineNumber: 128,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/app/(main)/discover/page.tsx",
            lineNumber: 127,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/(main)/discover/page.tsx",
        lineNumber: 126,
        columnNumber: 5
    }, this);
}
}}),
"[project]/app/(main)/discover/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/(main)/discover/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__396b5509._.js.map