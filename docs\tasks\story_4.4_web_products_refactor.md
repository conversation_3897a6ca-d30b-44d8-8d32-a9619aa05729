### User Story 4.4: Refactor Product & Service Features to Use the API

**User Story:** As a business owner using the web app, I want to manage my products and services, knowing that all operations are securely processed through the backend API.

**Acceptance Criteria:**
- All product and variant management features are fully functional.
- The features now rely exclusively on the new secure API for all data operations.
- All direct Supabase client calls for `products_services` and `product_variants` have been removed.

---

### Development Tasks

-   [ ] **1. Design and Create Product & Service API Routes**
    *   **Developer's Task:** Design and implement a comprehensive set of API routes for managing products and their variants. This will likely involve nested routing (e.g., `/api/business/[businessId]/products`) and specific routes for individual products (`/api/products/[productId]`). The routes must handle all CRUD (Create, Read, Update, Delete) operations.

-   [ ] **2. Implement Backend Logic with Ownership Checks**
    *   **Developer's Task:** Implement the logic for the new product-related API routes. A critical requirement for these routes is **authorization**. The backend logic must verify that the authenticated user making the request is the owner of the business for which they are trying to manage products. This prevents one user from editing another user's products.

-   [ ] **3. Refactor Client-Side Product Management UI**
    *   **Developer's Task:** Identify all pages, forms, and components in the web application that are used to list, view, create, edit, or delete products and variants. Refactor this entire feature set to use the new, secure API endpoints instead of direct Supabase SDK calls. Ensure all client-side state is managed correctly during and after these operations.