import React from 'react';
import { render, screen } from '@testing-library/react';
import AuthCallbackClient from '@/app/(main)/auth/callback/AuthCallbackClient';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClient } from '@/utils/supabase/client';

// Mock Next.js navigation hooks
const mockPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(() => ({
    push: mockPush,
  })),
  useSearchParams: jest.fn(),
}));

// Mock Supabase client
const mockGetUser = jest.fn();
const mockCreateClient = jest.fn(() => ({
  auth: {
    getUser: mockGetUser,
  },
}));

jest.mock('@/utils/supabase/client', () => ({
  createClient: mockCreateClient,
}));

// Mock getPostLoginRedirectPath
jest.mock('@/lib/actions/redirectAfterLogin', () => ({
  getPostLoginRedirectPath: jest.fn(),
}));

// Mock window methods
Object.defineProperty(window, 'close', {
  writable: true,
  value: jest.fn(),
});

Object.defineProperty(window, 'opener', {
  writable: true,
  value: null,
});

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

describe('AuthCallbackClient', () => {
  const mockUseRouter = useRouter as jest.Mock;
  const mockUseSearchParams = useSearchParams as jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseRouter.mockReturnValue({ push: mockPush });
    mockUseSearchParams.mockReturnValue(new URLSearchParams());
    mockCreateClient.mockReturnValue({
      auth: {
        getUser: mockGetUser,
      },
    });
    
    // Reset window.opener
    Object.defineProperty(window, 'opener', {
      writable: true,
      value: null,
    });

    // Default to successful auth response to prevent infinite loading
    mockGetUser.mockResolvedValue({
      data: { user: { id: 'user-123' } },
      error: null,
    });
  });

  it('renders loading state correctly', () => {
    render(<AuthCallbackClient />);
    
    expect(screen.getByText('Authenticating...')).toBeInTheDocument();
    
    // Check for the loader by looking for the specific SVG with animate-spin class
    const loader = document.querySelector('.animate-spin');
    expect(loader).toBeInTheDocument();
  });

  it('shows popup message when closeWindow is true', () => {
    mockUseSearchParams.mockReturnValue(new URLSearchParams('?closeWindow=true'));

    render(<AuthCallbackClient />);
    
    expect(screen.getByText('This window will close automatically after sign-in is complete.')).toBeInTheDocument();
  });

  it('does not show popup message when closeWindow is false', () => {
    mockUseSearchParams.mockReturnValue(new URLSearchParams('?closeWindow=false'));

    render(<AuthCallbackClient />);
    
    expect(screen.queryByText('This window will close automatically after sign-in is complete.')).not.toBeInTheDocument();
  });

  it('renders the main container correctly', () => {
    const { container } = render(<AuthCallbackClient />);
    
    const mainDiv = container.querySelector('.min-h-screen.flex.items-center.justify-center');
    expect(mainDiv).toBeInTheDocument();
  });

  it('renders the text center div correctly', () => {
    const { container } = render(<AuthCallbackClient />);
    
    const textCenterDiv = container.querySelector('.text-center');
    expect(textCenterDiv).toBeInTheDocument();
  });

  it('displays the loader with correct styling classes', () => {
    render(<AuthCallbackClient />);
    
    const loader = document.querySelector('.animate-spin');
    expect(loader).toHaveClass('mx-auto', 'h-8', 'w-8', 'text-[var(--brand-gold)]');
  });

  it('displays authenticating text with correct styling', () => {
    render(<AuthCallbackClient />);
    
    const text = screen.getByText('Authenticating...');
    expect(text).toHaveClass('mt-4', 'text-lg', 'font-medium');
  });

  it('initially does not display any dialog', () => {
    render(<AuthCallbackClient />);
    
    // Dialog should not be visible initially
    expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
  });

  it('renders without crashing when all props are defaults', () => {
    expect(() => {
      render(<AuthCallbackClient />);
    }).not.toThrow();
  });

  it('initializes without error', () => {
    expect(() => {
      render(<AuthCallbackClient />);
    }).not.toThrow();
    
    // Component should render and create the Supabase client via the hook
    expect(screen.getByText('Authenticating...')).toBeInTheDocument();
  });

  it('uses useRouter hook', () => {
    render(<AuthCallbackClient />);
    
    expect(mockUseRouter).toHaveBeenCalled();
  });

  it('uses useSearchParams hook', () => {
    render(<AuthCallbackClient />);
    
    expect(mockUseSearchParams).toHaveBeenCalled();
  });

  it('renders the correct JSX structure', () => {
    const { container } = render(<AuthCallbackClient />);
    
    // Check that we have the main div
    const mainContainer = container.querySelector('div.min-h-screen');
    expect(mainContainer).toBeInTheDocument();
    
    // Check that we have the text container
    const textContainer = container.querySelector('div.text-center');
    expect(textContainer).toBeInTheDocument();
    
    // Check that we have the paragraph
    const paragraph = textContainer?.querySelector('p');
    expect(paragraph).toBeInTheDocument();
    expect(paragraph).toHaveTextContent('Authenticating...');
  });
});