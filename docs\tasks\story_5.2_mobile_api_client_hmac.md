### User Story 5.2: Create a Centralized API Client with HMAC Signing

*   **User Story:** As a mobile developer, I want a single, reusable API client module that automatically handles HMAC signing for all outgoing requests, so that I can easily and securely interact with the backend from anywhere in the app.
*   **Acceptance Criteria:**
    *   A single, reusable API client module exists.
    *   All protected API calls made using the client are automatically signed with the correct HMAC signature.
    *   The client correctly handles access token injection and automatic refresh logic.

---

### Development Tasks

-   [ ] **1. Choose HTTP Client Library**
    *   **Developer's Task:** Select an appropriate HTTP client library for React Native (e.g., Axios, or use the native Fetch API directly). This will be the base for your centralized API client.

-   [ ] **2. Implement Mobile-Side HMAC Signing Utility**
    *   **Develo<PERSON>'s Task:** Create a utility function that mirrors the server-side HMAC generation logic. This function will take the `deviceSecret` (retrieved from secure storage), the HTTP method, the API path, the `X-Timestamp`, and the request body (hashed) as input.
    *   **Action:** This utility should return the HMAC-SHA256 signature string that will be used in the `X-Signature` header.

-   [ ] **3. Create Centralized API Client Module**
    *   **Developer's Task:** Implement the main API client module. This module will be responsible for all network requests to your `dukancard` API.
    *   **Action:**
        *   It must retrieve the `deviceId` and `deviceSecret` from secure storage (from Story 5.1).
        *   For every outgoing request, it must automatically generate the `X-Timestamp` and `X-Signature` headers using the utility from Step 2.
        *   It must attach the JWT `accessToken` to the `Authorization` header.
        *   It must integrate the token refreshing logic (from Story 5.1) to transparently handle expired access tokens.

-   [ ] **4. Implement Standardized Error Handling and Response Parsing**
    *   **Developer's Task:** Within the centralized API client, implement consistent error handling and response parsing. This ensures that all parts of the mobile application receive API responses and errors in a predictable format, simplifying development and debugging.