# Security Refactor - Detailed User Stories

This document contains the detailed user stories for the epics defined in the main PRD for the Unified API Layer & Security Refactor project.

---

## Epic 1: Foundational Backend & Database Architecture

### User Story 1.1: Create `devices` Table Schema
*   **As a developer,** I want to create a database migration for the `devices` table
*   **So that** we can securely store and manage information for each user's registered device.
*   **Acceptance Criteria:**
    *   A new SQL migration file is created in the `dukancard/supabase/migrations` directory.
    *   The migration script defines the `devices` table with all columns as specified in the architecture document (e.g., `device_id`, `user_id`, `device_secret_hash`, `platform`, `revoked`, etc.).
    *   A foreign key relationship is established to `users.id`.
    *   Appropriate indexes are created on `user_id` for efficient lookups.
    *   The migration can be applied and reverted successfully against a local development database.

### User Story 1.2: Create `refresh_tokens` Table Schema
*   **As a developer,** I want to create a database migration for the `refresh_tokens` table
*   **So that** we can securely store, rotate, and manage refresh tokens for persistent user sessions.
*   **Acceptance Criteria:**
    *   A new SQL migration file is created in the `dukancard/supabase/migrations` directory.
    *   The migration script defines the `refresh_tokens` table with all specified columns (e.g., `id`, `device_id`, `refresh_token_hash`, `expires_at`, `revoked`).
    *   A foreign key relationship is established to `devices.device_id` with `ON DELETE CASCADE`.
    *   Indexes are created on `device_id` and `refresh_token_hash` for fast lookups.
    *   The migration can be applied and reverted successfully.

### User Story 1.3: Implement Server-Side Hashing Utilities
*   **As a developer,** I want to implement and test server-side utility functions for hashing and verifying secrets
*   **So that** we can securely handle device secrets and refresh tokens without storing them in plaintext, following security best practices.
*   **Acceptance Criteria:**
    *   A new utility module (e.g., `dukancard/lib/security/hashing.ts`) is created.
    *   The module includes a function to hash a value using a strong, salted algorithm (bcrypt or argon2id is recommended).
    *   The module includes a function to compare a plaintext value against an existing hash.
    *   The utility functions are covered by unit tests to ensure correctness.

### User Story 1.4: Implement Type Synchronization Process
*   **As a developer,** I want a clear and documented process for keeping database types synchronized between the web and mobile projects.
*   **So that** we can leverage compile-time type safety and prevent data contract bugs.
*   **Acceptance Criteria:**
    *   A new document `SHARED_TYPES_GUIDE.md` is created at the project root.
    *   The guide documents the official process: 
        1. Run `supabase gen types typescript` in the `dukancard` project after any schema change.
        2. Manually copy the generated `supabase.ts` file from `dukancard` to `dukancard-app`.
    *   This process is added as a required checklist item to the team's "Definition of Done" for any database-related task.

---

## Epic 2: Core Authentication & Device Registration API

### User Story 2.1: Implement Device Registration Endpoint
*   **User Story:** As a developer, I want to create a secure API endpoint for registering a new device, so that we can generate and store a unique secret for each client instance, forming the basis of our HMAC security.
*   **Narrative:** This is a foundational security endpoint. After a user logs in, the client application will call this endpoint to get a unique `deviceId` and a one-time `deviceSecret`. This secret will be stored securely on the client and used to sign all subsequent API requests. The server will store a hash of this secret.
*   **Technical Requirements:**
    *   **Endpoint:** `POST /api/devices/register`
    *   **Authentication:** This endpoint must be protected by a valid JWT access token.
    *   **Request Body:** Should accept `{ deviceName: string, platform: 'ios' | 'android' | 'web', appSignatureHash?: string }`. Use Zod for validation.
    *   **Secret Generation:** Generate a cryptographically secure random string (at least 32 bytes) for the `deviceSecret`.
    *   **Database:**
        *   Create a new row in the `devices` table.
        *   The `user_id` should be extracted from the JWT token.
        *   The `device_secret` must be hashed using bcrypt/argon2id before being stored in the `device_secret_hash` column. **Never store the plaintext secret.**
*   **Acceptance Criteria:**
    *   **Given** a user is authenticated with a valid JWT:
        *   **When** a `POST` request is made to `/api/devices/register` with a valid request body.
        *   **Then** the API should respond with a `200 OK` status.
        *   **And** the response body must contain the new `deviceId` (uuid) and the plaintext `deviceSecret`. This is the **only time** the plaintext secret is ever sent.
        *   **And** a new record must exist in the `devices` table corresponding to the user and the new device.
        *   **And** the `device_secret_hash` column in the new record must contain a valid hash, not the plaintext secret.
    *   **Given** a request is made without a valid JWT:
        *   **Then** the API must respond with a `401 Unauthorized` status.
    *   **Given** the request body is malformed (e.g., missing `deviceName`):
        *   **Then** the API must respond with a `400 Bad Request` status.

### User Story 2.2: Implement User Login Endpoint
*   **User Story:** As a user, I want to submit my credentials to a login endpoint, so that I can receive a set of secure tokens to authenticate my session and my device.
*   **Narrative:** This is the primary entry point for users. It handles credential validation and, if successful, orchestrates the generation of all necessary tokens (access, refresh) and triggers the device registration flow.
*   **Technical Requirements:**
    *   **Endpoint:** `POST /api/auth/login`
    *   **Dependencies:** Relies on the logic from **Story 2.1 (Device Registration)** being available to be called internally.
    *   **Request Body:** Accepts `{ email: string, password: string, deviceInfo: object }`. `deviceInfo` contains the payload needed for device registration.
    *   **Credential Validation:** Use the Supabase Admin SDK (`supabase.auth.signInWithPassword`) server-side to verify the user's email and password.
    *   **Token Generation:**
        *   If credentials are valid, generate a short-lived (15 min) JWT **access token**. Its payload must include `user_id` and `roles`.
        *   Generate a long-lived (30 day), cryptographically secure **refresh token**.
    *   **Database:**
        *   After successful login, trigger the device registration logic from Story 2.1.
        *   Create a new row in the `refresh_tokens` table, storing a **hash** of the refresh token. The record must be linked to the newly created `deviceId`.
*   **Acceptance Criteria:**
    *   **Given** a valid email and password:
        *   **When** a `POST` request is made to `/api/auth/login`.
        *   **Then** the API responds with `200 OK`.
        *   **And** the response body contains `accessToken`, `refreshToken`, `deviceId`, and `deviceSecret`.
        *   **And** a new record is created in the `devices` table.
        *   **And** a new record is created in the `refresh_tokens` table, linked to the new device.
    *   **Given** invalid credentials are provided:
        *   **Then** the API responds with `401 Unauthorized`.
    *   **Given** the request is missing required fields:
        *   **Then** the API responds with `400 Bad Request`.

### User Story 2.3: Implement Token Refresh Endpoint
*   **User Story:** As an authenticated user, I want the application to be able to silently use a refresh token to get a new access token, so that I can stay logged in without having to enter my password repeatedly.
*   **Narrative:** This endpoint is crucial for session persistence. It allows the client to exchange a valid, non-expired refresh token for a new pair of access and refresh tokens. It implements refresh token rotation for security.
*   **Technical Requirements:**
    *   **Endpoint:** `POST /api/auth/refresh`
    *   **Authentication:** This endpoint is not protected by a JWT. It is protected by the **HMAC signature** (which will be built in Epic 3, but we can scaffold the logic here).
    *   **Request Body:** Accepts `{ refreshToken: string, deviceId: string }`.
    *   **Database Lookup:**
        *   Find the `device` record using the provided `deviceId`.
        *   Find the `refresh_token` record by **hashing** the provided `refreshToken` and looking up the hash in the `refresh_tokens` table.
    *   **Security (Token Rotation):**
        *   Upon successful validation, the used `refreshToken` must be immediately marked as `revoked` in the database.
        *   A new access token and a new refresh token must be generated.
        *   The new refresh token's hash is stored in a new row in the `refresh_tokens` table.
    *   **Security (Reuse Detection):** If a `refreshToken` is provided that is already marked as `revoked`, it's a sign of token theft. The system must immediately revoke the entire device and all associated refresh tokens, effectively logging the user out everywhere.
*   **Acceptance Criteria:**
    *   **Given** a valid, non-revoked `refreshToken` and `deviceId`:
        *   **When** a `POST` request is made to `/api/auth/refresh`.
        *   **Then** the API responds with `200 OK`.
        *   **And** the response body contains a **new** `accessToken` and a **new** `refreshToken`.
        *   **And** the old refresh token is marked as `revoked` in the database.
        *   **And** a new refresh token record is created.
    *   **Given** an invalid or expired `refreshToken` is provided:
        *   **Then** the API responds with `401 Unauthorized`.
    *   **Given** a `refreshToken` that has already been used (is marked as `revoked`) is provided:
        *   **Then** the API responds with `409 Conflict` (or similar error).
        *   **And** the associated `device` and all of its tokens are immediately revoked as a security measure.

### User Story 2.4: Implement User Logout Endpoint
*   **User Story:** As a logged-in user, I want to be able to log out, so that my current session is securely terminated and my tokens are invalidated.
*   **Narrative:** This endpoint provides a clean way for a user to end their session. It invalidates the current refresh token, preventing it from being used again.
*   **Technical Requirements:**
    *   **Endpoint:** `POST /api/auth/logout`
    *   **Authentication:** Protected by a valid JWT and HMAC signature.
    *   **Request Body:** Accepts `{ refreshToken: string }`.
    *   **Database:** Find the refresh token record by hashing the provided token and mark it as `revoked`.
*   **Acceptance Criteria:**
    *   **Given** a user is authenticated with a valid JWT:
        *   **When** a `POST` request is made to `/api/auth/logout` with the current `refreshToken`.
        *   **Then** the API responds with `200 OK`.
        *   **And** the corresponding `refresh_token` record in the database is marked as `revoked`.
    *   **Given** an invalid `refreshToken` is provided:
        *   **Then** the API should still respond with `200 OK` (to prevent leaking information about token validity) but perform no database action.

---

## Epic 3: API Security Middleware & Request Hardening

### User Story 3.1: Implement HMAC Signature Verification Middleware
*   **User Story:** As a developer, I want to create a middleware layer that verifies the HMAC signature on protected API requests, so that we can ensure the request is authentic, has not been tampered with, and originates from a registered device.
*   **Narrative:** This middleware is a critical security control. For every incoming request to a protected endpoint, it will extract the device ID, timestamp, and signature from the headers. It will then fetch the corresponding hashed device secret from the database, reconstruct the signature string, and perform a constant-time comparison to validate the signature. This proves the request was sent by a client who possesses the correct `deviceSecret`.
*   **Technical Requirements:**
    *   **Middleware Logic:** This logic should be integrated into the primary security middleware for the API.
    *   **Headers:** The middleware must read and validate the presence of `X-Device-Id`, `X-Timestamp`, and `X-Signature` headers.
    *   **Database Interaction:** It must perform a fast, indexed lookup on the `devices` table using the `device_id` from the header to retrieve the `device_secret_hash`.
    *   **HMAC Generation:** It must precisely replicate the client-side signature generation logic. The string to be signed is a concatenation of `(httpMethod + apiPath + timestamp + sha256(requestBody))`. Use the standard `crypto` module in Node.js for HMAC-SHA256.
    *   **Security:** The comparison between the received signature from the client and the server-calculated signature **must** be performed using a constant-time comparison function (e.g., `crypto.timingSafeEqual`) to mitigate timing attacks.
*   **Acceptance Criteria:**
    *   **Given** a request to a protected endpoint with a valid and correctly calculated HMAC signature:
        *   **When** the middleware executes.
        *   **Then** the request is allowed to proceed to the next handler.
    *   **Given** a request with an invalid, incorrect, or tampered HMAC signature:
        *   **When** the middleware executes.
        *   **Then** the request is immediately rejected with a `403 Forbidden` status and a clear "Invalid Signature" error message.
    *   **Given** a request with a `deviceId` that does not exist or has been revoked:
        *   **Then** the request is rejected with a `403 Forbidden` status.
    *   **Given** a request missing any of the required `X-Device-Id`, `X-Timestamp`, or `X-Signature` headers:
        *   **Then** the request is rejected with a `400 Bad Request` status.
*   **Dependencies:** Story 2.1 (Device Registration), as this middleware needs to fetch the `device_secret_hash`.

### User Story 3.2: Implement Timestamp Freshness & Replay Attack Middleware
*   **User Story:** As a developer, I want to add a timestamp validation check to the security middleware, so that we can prevent replay attacks where an attacker re-sends a valid, intercepted request.
*   **Narrative:** This middleware works in tandem with the HMAC signature. Since the timestamp is part of the signed payload, an attacker cannot change it. This middleware simply ensures that the timestamp of the request is within a very short window of the server's current time, making stolen request signatures useless after a few moments.
*   **Technical Requirements:**
    *   **Middleware Logic:** This should be one of the first checks in the security middleware, executed immediately after parsing the `X-Timestamp` header.
    *   **Time Window:** The server should reject any request where the `X-Timestamp` value is older or newer than the server's current time by a defined margin (e.g., ±120 seconds). This window should be configurable.
    *   **Server Time:** The comparison must be against the server's coordinated universal time (UTC) to avoid time zone issues.
*   **Acceptance Criteria:**
    *   **Given** a request whose `X-Timestamp` is within the allowed time window (e.g., 90 seconds ago):
        *   **When** the middleware executes.
        *   **Then** the request is allowed to proceed to the next check (e.g., HMAC verification).
    *   **Given** a request whose `X-Timestamp` is outside the allowed time window (e.g., 5 minutes ago):
        *   **When** the middleware executes.
        *   **Then** the request is immediately rejected with a `408 Request Timeout` status and a "Request has expired" error message.

### User Story 3.3: Implement General Rate Limiting Middleware
*   **User Story:** As a DevOps engineer, I want to implement a flexible rate-limiting middleware, so that we can protect the API from denial-of-service attacks, control costs, and prevent abuse from runaway clients.
*   **Narrative:** This middleware will track the number of requests from various sources within a given time window and block them if they exceed a configured limit. This is a crucial defense against many forms of automated abuse.
*   **Technical Requirements:**
    *   **Dependencies:** Story 1.5 (Redis Integration). This middleware will use Redis for efficiently tracking request counts.
    *   **Limiting Strategies:** The implementation should support rate limiting by multiple strategies, applied in order:
        1.  By IP address (to slow down attackers from a single source).
        2.  By `deviceId` (to prevent a single compromised device from spamming the API).
        3.  By `userId` (to prevent a single user from excessive usage across multiple devices).
    *   **Configuration:** The number of requests and the time window (e.g., 100 requests per minute) for each strategy must be easily configurable via environment variables.
    *   **HTTP Headers:** Successful responses should include headers like `X-RateLimit-Limit`, `X-RateLimit-Remaining`, and `X-RateLimit-Reset` to inform clients of their current status.
*   **Acceptance Criteria:**
    *   **Given** a client has made fewer requests than the configured limit:
        *   **When** a new request is made.
        *   **Then** the request is allowed to proceed.
        *   **And** the `X-RateLimit-Remaining` header is correctly decremented.
    *   **Given** a client has exceeded the configured limit:
        *   **When** a new request is made.
        *   **Then** the request is immediately rejected with a `429 Too Many Requests` status.
        *   **And** the `Retry-After` header indicates how long to wait before making a new request.

### User Story 3.4: Implement Brute-Force Protection for Auth Endpoints
*   **User Story:** As a security engineer, I want to implement strict, targeted brute-force protection on authentication endpoints, so that we can prevent attackers from guessing user passwords or attempting credential stuffing attacks.
*   **Narrative:** This is a specialized form of rate limiting that applies only to sensitive endpoints like `/api/auth/login`. It has much lower thresholds and can trigger temporary lockouts to stop an attacker in their tracks.
*   **Technical Requirements:**
    *   **Endpoint Specificity:** This middleware should only apply to `POST /api/auth/login` and `POST /api/auth/refresh`.
    *   **Logic:** It should track the number of *failed* login attempts per IP address and per email address.
    *   **Thresholds:** After a small number of failures (e.g., 5 failed attempts in 5 minutes), the system should temporarily block all further login attempts from that IP and/or for that user account for a configurable duration (e.g., 15 minutes).
    *   **State Management:** Uses Redis to track failure counts and lockout timers.
*   **Acceptance Criteria:**
    *   **Given** 5 failed login attempts are made for `<EMAIL>` within 5 minutes:
        *   **When** the 6th login attempt is made for that user.
        *   **Then** the request is rejected with a `429 Too Many Requests` status, regardless of whether the password is correct.
    *   **Given** a user is locked out:
        *   **When** they try to log in again before the lockout period expires.
        *   **Then** they continue to receive a `429` error.
    *   **Given** a user is locked out:
        *   **When** they wait for the lockout period to expire and try again with the correct password.
        *   **Then** the login succeeds.

### User Story 3.5: Implement Reusable Request Validation
*   **User Story:** As a developer, I want a standardized and reusable way to validate the body and parameters of incoming API requests, so that I can ensure all data is in the correct format before it reaches my business logic, preventing a wide range of errors.
*   **Narrative:** This story involves creating a pattern or helper function that uses a schema validation library (like Zod) to parse and validate the request body for each API route. This ensures that all downstream code can trust the shape and type of the data it's working with.
*   **Technical Requirements:**
    *   **Library:** Use **Zod** to define schemas for the request bodies of each API endpoint.
    *   **Pattern:** Create a higher-order function or a simple utility that can be called at the beginning of each API route handler. This utility will take a Zod schema and the request object as input.
    *   **Error Handling:** If validation fails, the utility should automatically throw a structured error that results in a `400 Bad Request` response. The response body should contain details about the validation errors (which fields were incorrect and why).
*   **Acceptance Criteria:**
    *   **Given** an API route for creating a business that expects `{ businessName: string }`.
    *   **When** a request is made with a valid body like `{ "businessName": "My Shop" }`.
    *   **Then** the request is successfully validated and proceeds.
    *   **When** a request is made with an invalid body like `{ "name": "My Shop" }` (wrong key) or `{ "businessName": 123 }` (wrong type).
    *   **Then** the API immediately responds with a `400 Bad Request` status.
    *   **And** the JSON response body contains an `errors` array detailing which field failed validation and why.

---

## Epic 4: `dukancard` (Web) Client & Server Refactoring to Use New API

### User Story 4.1: Integrate Web Client with New Authentication Flow
*   **User Story:** As a web user, I want to log in, log out, and have my session managed through the new, secure authentication API, so that my experience is seamless and my credentials are secure.
*   **Developer's Task:** Refactor the entire authentication flow for the `dukancard` web application. This includes updating login pages, sign-up forms, and session management logic (e.g., context providers) to stop using the Supabase client directly for auth. Instead, they must call the new API endpoints: `/api/auth/login`, `/api/auth/logout`, and `/api/devices/register`. The client-side logic must be updated to handle the new tokens (`accessToken`, `refreshToken`, `deviceId`, `deviceSecret`).
*   **Acceptance Criteria:**
    *   Users can successfully log in and log out via the web interface.
    *   Upon login, the web client securely stores the returned tokens and device identifiers.
    *   All subsequent requests from the web client to our API are authenticated using the new JWT `accessToken`.
    *   The silent refresh mechanism using the `refreshToken` is functional for the web client.

### User Story 4.2: Refactor Business Profile Features to Use the API
*   **User Story:** As a web user, I want to be able to view, create, and edit business profiles through the web interface, with all data flowing through the new secure API layer.
*   **Developer's Task:** Identify all frontend components and server-side functions in the `dukancard` codebase that currently interact directly with the `business_profiles` table via the Supabase client. Create the necessary secure API routes (e.g., `/api/business`, `/api/business/[id]`) and refactor the original code to call these new routes instead.
*   **Acceptance Criteria:**
    *   All functionality related to creating, reading, updating, and deleting business profiles works as before.
    *   All data for these features now flows exclusively through the new, secure API endpoints which are protected by the security middleware.
    *   Direct Supabase client calls related to the `business_profiles` table have been removed from the web application's frontend and server-side rendering logic.

### User Story 4.3: Refactor User Profile & Settings Features to Use the API
*   **User Story:** As a logged-in web user, I want to view and manage my own user profile and settings, with all my data being handled by the secure API.
*   **Developer's Task:** Find all code related to user profile management (e.g., viewing profile pages, updating user information in settings). Developers are responsible for creating the required API routes (e.g., `GET /api/user/profile`, `POST /api/user/profile`) and refactoring the client-side and server-side code to use them.
*   **Acceptance Criteria:**
    *   Users can view and update their profile information as before.
    *   All data for these features is now fetched and updated via the new secure API.
    *   Direct Supabase client calls related to the `customer_profiles` or `users` tables for profile management are eliminated from the codebase.

### User Story 4.4: Refactor Product & Service Features to Use the API
*   **User Story:** As a business owner using the web app, I want to manage my products and services, knowing that all operations are securely processed through the backend API.
*   **Developer's Task:** Go through the codebase and identify all features related to managing `products_services` and `product_variants`. This includes listing, viewing, creating, updating, and deleting products. Create the necessary API routes and refactor the existing code to use these new endpoints.
*   **Acceptance Criteria:**
    *   All product and variant management features are fully functional.
    *   The features now rely exclusively on the new secure API for all data operations.
    *   All direct Supabase client calls for `products_services` and `product_variants` have been removed.

### User Story 4.5: Refactor All Remaining Features to Use the API
*   **User Story:** As a developer, I want to ensure all remaining features of the web application are migrated to the new API architecture, so that we can completely eliminate direct Supabase client access from the `dukancard` project.
*   **Developer's Task:** This is a comprehensive story to audit the entire `dukancard` codebase for any remaining direct Supabase client calls (e.g., for posts, comments, likes, ratings, etc.). Developers are to group these remaining features logically, create the final set of required API routes, and refactor the code to complete the migration.
*   **Acceptance Criteria:**
    *   All remaining application features (posts, comments, likes, etc.) are fully functional.
    *   The entire `dukancard` web application no longer contains any instance of the Supabase client being used for data queries or modifications. All data flows through the new internal API.
    *   The application is ready to serve as a stable and secure backend for the mobile app migration in the next epic.

---

## Epic 5: `dukancard-app` (Mobile) Supabase SDK Removal & API Integration

### User Story 5.1: Implement Mobile Authentication Flow
*   **User Story:** As a mobile app user, I want to log in using the new secure API and have my session securely stored on my device, so I can have a persistent and safe experience.
*   **Developer's Task:** Refactor the entire authentication flow in the `dukancard-app`. This involves removing all direct calls to Supabase for auth and instead calling the new API endpoints (`/api/auth/login`, etc.). Implement secure storage for the tokens and secrets using the platform's Keychain/Keystore.
*   **Acceptance Criteria:**
    *   The mobile app's login, logout, and registration flows are fully functional using the `dukancard` API.
    *   `accessToken`, `refreshToken`, `deviceId`, and `deviceSecret` are correctly stored in the device's secure storage upon login.
    *   The application correctly uses the stored tokens for subsequent API calls.
    *   The silent token refresh mechanism is implemented and working.

### User Story 5.2: Create a Centralized API Client with HMAC Signing
*   **User Story:** As a mobile developer, I want a single, reusable API client module that automatically handles HMAC signing for all outgoing requests, so that I can easily and securely interact with the backend from anywhere in the app.
*   **Developer's Task:** Create a centralized API client (e.g., using Axios or Fetch). This client must be configured to automatically:
    1.  Read the stored `deviceId` and `deviceSecret`.
    2.  Generate the required `X-Timestamp` and `X-Signature` (HMAC) headers for every protected request.
    3.  Attach the JWT `accessToken` to the `Authorization` header.
    4.  Handle token refreshing logic transparently.
*   **Acceptance Criteria:**
    *   A single, reusable API client module exists.
    *   All protected API calls made using the client are automatically signed with the correct HMAC signature.
    *   The client correctly handles access token injection and automatic refresh logic.

### User Story 5.3: Refactor Mobile Business & User Profile Features
*   **User Story:** As a mobile user, I want to view and manage business and user profiles within the app, with all data being fetched securely from the new API.
*   **Developer's Task:** Identify all screens and components in the `dukancard-app` codebase related to business and user profiles. Refactor all data-fetching and mutation logic to use the new centralized API client instead of the Supabase SDK.
*   **Acceptance Criteria:**
    *   All business and user profile screens are fully functional.
    *   All data is now being routed through the secure `dukancard` API.
    *   Direct Supabase SDK calls for these features have been eliminated from the mobile codebase.

### User Story 5.4: Refactor Mobile Product & Posts Features
*   **User Story:** As a mobile user, I want to interact with products and view posts, with all interactions being securely handled by the backend API.
*   **Developer's Task:** Go through the mobile codebase to find all features related to `products_services`, `business_posts`, and `customer_posts`. Refactor the logic to use the new centralized API client for all create, read, update, and delete operations.
*   **Acceptance Criteria:**
    *   All product and post-related features in the mobile app work as expected.
    *   These features now exclusively use the new secure API for data operations.
    *   All direct Supabase SDK calls for these features have been removed.

### User Story 5.5: Finalize Migration and Remove Supabase SDK
*   **User Story:** As a developer, I want to remove the Supabase SDK dependency entirely from the mobile app, so that the migration is complete and no insecure, direct database calls are possible.
*   **Developer's Task:** Audit the entire `dukancard-app` project for any remaining Supabase SDK usage (e.g., for comments, likes, or other miscellaneous features). Refactor the remaining pieces and then completely remove the Supabase client library from the project's dependencies in `package.json`.
*   **Acceptance Criteria:**
    *   The Supabase SDK package (`@supabase/supabase-js`) is completely removed from `dukancard-app/package.json`.
    *   The mobile application is fully functional and relies 100% on the `dukancard` API for all data and authentication.
    *   The app builds and runs correctly without the Supabase SDK.

---

## Epic 6: Observability, Monitoring & Admin Tooling

### User Story 6.1: Implement Structured JSON Logging
*   **User Story:** As a developer, I want all API requests and key events to be logged in a structured JSON format with a unique correlation ID, so that we can easily search, filter, and analyze logs in a centralized logging platform (like Datadog, Sentry, etc.).
*   **Narrative:** To effectively debug issues and monitor API traffic, we need to move beyond simple `console.log`. This story involves integrating a production-grade logger library into our Next.js API. This will produce machine-readable logs for every request, which is essential for effective monitoring and alerting.
*   **Technical Requirements:**
    *   **Library:** Integrate a structured logging library like **Pino** (with `pino-http`) into the `dukancard` API.
    *   **Correlation ID:** A unique request ID must be generated for every incoming request and attached to every single log line produced during that request's lifecycle. This allows us to trace a single request through the system.
    *   **Standard Log Fields:** Every request log must automatically include key fields like `method`, `path`, `statusCode`, `responseTimeMs`, and the user's IP address.
    *   **Custom Logging:** The logger instance must be easily accessible throughout the application so developers can add contextual logs within the business logic (e.g., `logger.warn({ userId, deviceId }, 'Refresh token reuse detected')`).
    *   **Environment-Specific Formatting:** Logs should be pretty-printed in a human-readable format in the `development` environment and single-line JSON objects in the `production` environment.
*   **Acceptance Criteria:**
    *   **Given** any request is made to an API endpoint:
        *   **Then** a structured JSON log is generated containing the request's details.
        *   **And** all log entries related to that request (including custom ones) share the same unique `correlationId`.
    *   **Given** an error is thrown within an API route:
        *   **Then** a structured log with `level: "error"` is automatically generated, capturing the error message and stack trace.

### User Story 6.2: Create Security & Performance Monitoring Dashboards
*   **User Story:** As a DevOps engineer, I want a set of monitoring dashboards, so that I can visualize key security and performance metrics for the API layer at a glance.
*   **Narrative:** This story involves identifying the most critical metrics from our new system and creating dashboards in our chosen monitoring tool (e.g., Vercel Monitoring, Datadog). This provides a real-time view of the health and security posture of the application.
*   **Technical Requirements:**
    *   **Tooling:** This task will be implemented in the project's chosen monitoring or observability platform.
    *   **Dashboards to Create:**
        1.  **Security Dashboard:**
            *   Failed authentications (logins, refreshes) over time.
            *   Rate of HMAC signature failures.
            *   Number of brute-force lockouts triggered.
            *   Rate of requests blocked by rate-limiting.
            *   Chart of token reuse events detected.
        2.  **Performance Dashboard:**
            *   API request latency (average, p95, p99), broken down by endpoint.
            *   API request volume (requests per minute).
            *   API error rate (4xx and 5xx errors).
*   **Acceptance Criteria:**
    *   **Given** the monitoring platform is open:
        *   **Then** a "Security Overview" dashboard is present and correctly displays data for auth failures, HMAC failures, and lockouts.
        *   **And** a "API Performance" dashboard is present and correctly displays data for request latency, volume, and error rates.
*   **Dependencies:** Story 6.1 (Structured Logging), as the dashboards will be powered by querying the structured log data.

### User Story 6.3: Configure Alerts for Security Anomalies
*   **User Story:** As a security engineer, I want to be proactively notified of potential security events, so that I can respond immediately to threats.
*   **Narrative:** Dashboards are good for observing, but alerts are for taking action. This story involves setting up automated alerts based on thresholds defined in our monitoring tool.
*   **Technical Requirements:**
    *   **Tooling:** Implemented in the monitoring/alerting platform (e.g., Datadog, Sentry Alerts, PagerDuty).
    *   **Alerts to Configure:**
        *   **High Rate of Failed Logins:** Trigger an alert if the number of failed logins exceeds a threshold (e.g., >50 in 5 minutes).
        *   **High Rate of HMAC Failures:** Trigger an alert if the rate of invalid HMAC signatures is unusually high, suggesting a potential attack attempt.
        *   **Token Reuse Detected:** Trigger a high-priority alert immediately any time a refresh token reuse event is detected.
        *   **Spike in 5xx Errors:** Trigger an alert if the server-side error rate for the API exceeds a certain percentage, indicating a stability problem.
*   **Acceptance Criteria:**
    *   **Given** a sudden spike in failed login attempts occurs that crosses the defined threshold:
        *   **Then** an alert is triggered and sent to the designated channel (e.g., Slack, email).
    *   **Given** a token reuse event is logged:
        *   **Then** a high-priority alert is immediately triggered.

### User Story 6.4: Implement Admin API for Device Revocation
*   **User Story:** As an administrator, I want a secure API endpoint to revoke a user's device or all of a user's devices, so that I can respond to a security incident or a user's request.
*   **Narrative:** This provides a critical administrative control. If a user reports a device as lost or stolen, an admin needs a way to immediately invalidate that device's access and log it out.
*   **Technical Requirements:**
    *   **Endpoint:** `POST /api/admin/security/devices/revoke`
    *   **Authentication:** This endpoint must be protected by a middleware that checks for a valid JWT from a user with the `'admin'` role.
    *   **Request Body:** Accepts `{ deviceId: string }` to revoke a single device, or `{ userId: string }` to revoke all devices belonging to that user.
    *   **Database:** The logic should set the `revoked` flag to `true` on the specified `device` record(s) and all associated `refresh_token` records.
*   **Acceptance Criteria:**
    *   **Given** an admin is authenticated:
        *   **When** a `POST` request is made to the endpoint with a `deviceId`.
        *   **Then** the corresponding device and its tokens are marked as `revoked` in the database.
        *   **And** any subsequent attempt to use that device's refresh token will fail.
    *   **Given** a non-admin user attempts to call this endpoint:
        *   **Then** the request is rejected with a `403 Forbidden` error.

### User Story 6.5: Build User-Facing UI for Device Management
*   **User Story:** As a security-conscious user, I want to see a list of all devices currently logged into my account and be able to log out a specific device, so that I can manage my own account security.
*   **Narrative:** This feature empowers users by giving them visibility and control. It will live within the user's profile settings area (e.g. `/profile/security`) and lists all active devices, allowing remote logout.
*   **Technical Requirements:**
    *   **API Endpoints:**
        *   `GET /api/security/devices`: A new endpoint that lists all non-revoked devices for the currently authenticated user.
        *   `POST /api/security/devices/:id/revoke`: A new endpoint that allows a user to revoke one of their *own* devices.
    *   **UI:** A new component/screen in both the web and mobile apps, likely at a path like `/profile/settings/devices`, that:
        *   Calls the `GET /api/security/devices` endpoint to display the list of devices.
        *   Provides a "Log out" button next to each device (except the current one).
        *   Calls the `POST /api/security/devices/:id/revoke` endpoint when the button is clicked.
*   **Acceptance Criteria:**
    *   **Given** a user is logged in and navigates to their device management page:
        *   **Then** they can see a list of all their active devices (e.g., "iPhone 15 - Last seen 2 hours ago").
        *   **When** they click the "Log out" button for a different device.
        *   **Then** that device is successfully logged out and can no longer access the API with its old tokens.
        *   **And** the device list UI updates to show the device has been removed or marked as inactive.

### User Story 6.6: Build Role-Based Admin Portal Foundation
*   **User Story:** As an administrator, I want to log in to a secure, extensible admin portal to access special administrative tools, so that I can manage the application and respond to security incidents.
*   **Narrative:** This story establishes the foundation for the all-in-one admin portal. It involves creating the main `/admin` layout, protecting it with role-based access control, and delivering the first module: the security tools for device management. The architecture must support adding more modules (like analytics) in the future.
*   **Technical Requirements:**
    *   **Database:** Add a `roles TEXT[]` column to the `users` table to differentiate admins from regular users.
    *   **JWT:** Update the JWT generation logic (from Story 2.2) to include the user's `roles` in the token payload.
    *   **Routing & UI:**
        *   Create a main layout for the admin portal at `/admin`.
        *   Create the first module page at `/admin/security/devices`.
        *   This page should contain a simple UI to call the device revocation API from Story 6.4.
    *   **API & Page Middleware:**
        *   Implement middleware that protects all `/admin/*` pages and `/api/admin/*` routes.
        *   The middleware must inspect the user's JWT and reject any request from a user who does not have the `'admin'` role. This check should be written in an extensible way to support more granular roles in the future.
*   **Acceptance Criteria:**
    *   **Given** a user with the `'admin'` role logs in:
        *   **Then** they are able to successfully navigate to the `/admin/security/devices` page.
        *   **And** they can successfully use the UI to revoke a user's device.
    *   **Given** a normal user without the `'admin'` role logs in:
        *   **When** they attempt to navigate to any `/admin/*` page.
        *   **Then** they are redirected or shown a "Permission Denied" message.
        *   **And** any direct attempt to call an `/api/admin/*` endpoint is rejected with a `403 Forbidden` error.
