### User Story 6.4: Implement Admin API for Device Revocation

*   **User Story:** As an administrator, I want a secure API endpoint to revoke a user's device or all of a user's devices, so that I can respond to a security incident or a user's request.
*   **Acceptance Criteria:**
    *   A new API endpoint `POST /api/admin/security/devices/revoke` is created.
    *   The endpoint is protected and only accessible by users with the `'admin'` role.
    *   It correctly revokes specified devices and their associated tokens.

---

### Development Tasks

-   [ ] **1. Create API Route File Structure**
    *   **Task:** In the `dukancard` project, create the new API route file.
    *   **File Path:** `dukancard/app/api/admin/security/devices/revoke/route.ts`

-   [ ] **2. Implement Request Body Validation**
    *   **Task:** Using Zod, define a schema that validates the incoming request body. It should accept either a `deviceId` or a `userId`.
    *   **Schema Example:**

    ```typescript
    // dukancard/lib/schemas/admin.ts (or similar)
    import { z } from 'zod';

    export const revokeDeviceSchema = z.object({
        deviceId: z.string().uuid().optional(),
        userId: z.string().uuid().optional(),
    }).refine(data => data.deviceId || data.userId, {
        message: "Either deviceId or userId must be provided."
    });
    ```

-   [ ] **3. Implement Admin Role Protection**
    *   **Task:** Ensure this API route is protected by the admin role middleware (which will be implemented in Story 6.6). This means only users with the `'admin'` role can successfully call this endpoint.

-   [ ] **4. Implement the Route Handler Logic**
    *   **Task:** In `dukancard/app/api/admin/security/devices/revoke/route.ts`, implement the `POST` handler.
    *   **Logic Steps:**
        1.  **Validate Request Body:** Parse and validate the incoming payload using the schema from Step 2.
        2.  **Database Operations:**
            *   Use the service role Supabase client.
            *   **If `deviceId` is provided:** Update the `devices` table to set `revoked = true` for that specific `device_id`. Also, update all associated `refresh_tokens` for that device to `revoked = true`.
            *   **If `userId` is provided:** Update all `devices` belonging to that `user_id` to `revoked = true`. Also, update all associated `refresh_tokens` for those devices to `revoked = true`.
            *   Ensure these operations are atomic (e.g., within a transaction if multiple updates are needed).
        3.  **Return Success:** Respond with `200 OK` and a success message.

-   [ ] **5. Write Integration Tests**
    *   **Task:** Create a new test file.
    *   **File Path:** `dukancard/__tests__/app/api/admin/security/devices/revoke/route.test.ts`
    *   **Test Cases:**
        *   **Test 1: Revoke Single Device (Admin User):** Mock an authenticated admin user. Seed the database with a user, a device, and an associated refresh token. Call the endpoint with the `deviceId`. Assert a `200 OK` response. Verify that the device and its refresh token are marked `revoked` in the database.
        *   **Test 2: Revoke All Devices for User (Admin User):** Mock an authenticated admin user. Seed the database with a user and multiple devices/refresh tokens. Call the endpoint with the `userId`. Assert a `200 OK` response. Verify that all devices and their associated refresh tokens for that user are marked `revoked`.
        *   **Test 3: Unauthorized Access (Non-Admin User):** Mock an authenticated non-admin user. Call the endpoint. Assert a `403 Forbidden` response.
        *   **Test 4: Invalid Request Body:** Call the endpoint with a malformed request body (e.g., neither `deviceId` nor `userId` provided). Assert a `400 Bad Request` response.