# Dukancard — Architecture Specification

**Owner:** Architect (<PERSON>)
**Purpose:** Define the target architecture for removing Supabase from the mobile app, centralizing business logic in `dukancard` (Next.js) API routes, and implementing a hardened security and middleware layer to protect Supabase and serve both web and mobile clients.

---

## 1. Goals & Constraints

**Primary goals**

* Centralize all Supabase access in the Next.js API (single source of truth).
* Remove Supabase keys and direct SDK usage from `dukancard-app` (React Native).
* Implement defense-in-depth: JWT auth, per-device secrets, HMAC signing, refresh token rotation, middleware filtering and rate limits.
* Reduce wasted Supabase calls by rejecting malicious/invalid requests before they reach Supabase.
* Keep costs low; avoid paid attestation services.

**Constraints**

* No external attestation services (Firebase App Check, Play Integrity) — purely self-hosted techniques.
* Use Supabase as DB/storage (service role key on server only).
* Deploy primarily on Vercel (Next.js), with standard CI/CD.

---

## 2. High-Level Components

```
[Mobile App (dukancard-app)]  <-- HTTPS + HMAC -->  [Dukancard API (Next.js)]  <-- Secured --> [Supabase (Postgres, Storage)]
                                     |
                                     v
                              [Security Middleware]
                              [Rate Limiter / WAF]
                              [Logging & Monitoring]
```

### Components

* **Dukancard-App (React Native)**

  * Secure storage: Keychain / Android Keystore / Expo SecureStore
  * Stores: deviceId, deviceSecret, refreshToken, accessToken
  * Responsible for HMAC signing of all API requests
  * Silent token refresh on start/foreground

* **Dukancard API (Next.js)**

  * API routes (`/api/*`) for auth, devices, business, user, admin
  * Lightweight middleware stack: JWT verification -> Device/HMAC verification -> Rate limiting -> Request validation -> Supabase proxy
  * Uses Supabase server client with **SERVICE\_ROLE\_KEY** (env variable)
  * Stores only **hashes** of secrets and refresh tokens in DB

* **Security Middleware**

  * HMAC verification, timestamp freshness, constant-time compare
  * Rate limiting (per device, per IP, per user)
  * Brute-force detection & temporary device lockout
  * App-signature integrity checks during registration
  * Request logging, anomaly detection hooks

* **Supabase**

  * Postgres DB with RLS (row level security) still enabled as defense in depth
  * Storage buckets for media, images — accessed via Signed URLs generated by API

* **Ops & Infra**

  * Vercel (Next.js) or similar for API hosting
  * Redis (or in-memory + durable fallback) for rate limiting, token reuse cache, short-term state
  * Logging & observability stack (Sentry, Datadog, or open-source alternatives)

---

## 3. Data Model (Core Tables)

### `users`

* `id` (uuid, pk)
* `email` (unique)
* `password_hash`
* `roles` (json)
* `created_at`

### `devices`

* `device_id` (uuid, pk)
* `user_id` (fk -> users.id)
* `device_name`
* `platform` (enum: ios|android|web)
* `device_secret_hash` (bcrypt/argon2)
* `app_signature_hash` (text) -- recorded at registration for heuristics
* `revoked` (bool)
* `last_seen_at` (timestamp)
* `created_at`

**Notes:** never store device\_secret plaintext. Use strong KDF (argon2id / bcrypt) when storing. For HMAC verification, derive HMAC key from device secret using HKDF & store only derived key hash if desired.

### `refresh_tokens`

* `id` (uuid)
* `device_id` (fk)
* `refresh_token_hash` (sha256 or bcrypt)
* `issued_at`
* `expires_at`
* `rotated_from` (nullable fk)
* `revoked` (bool)

**Rotation & reuse detection**: keep N previous refresh token hashes for a sliding window (e.g., last 3 tokens) to detect reuse.

---

## 4. API Surface (summary)

* `POST /api/auth/login` — verify credentials, create device (if needed), issue tokens
* `POST /api/auth/refresh` — rotate refresh token (requires deviceId + HMAC)
* `POST /api/auth/logout` — revoke current refresh token
* `POST /api/devices/register` — create device entry; return deviceSecret once
* `GET /api/business/list` — protected read
* `POST /api/business/create` — protected write
* Additional admin endpoints: `/api/admin/devices/revoke`, `/api/admin/users/revoke-all`

All business endpoints must pass through the security middleware.

---

## 5. Security & Trust Model

### Authentication

* **Access token**: JWT signed by server (RS256 or HS256) with TTL = 15 minutes.
* **Refresh token**: long opaque token (rotate on use), TTL = 30 days sliding window, bound to `deviceId`. Stored hashed server-side.
* **Device secret**: random 32–64 bytes, returned once at registration, stored hashed.

### Request Integrity

* **HMAC** using device secret: signature = HMAC\_SHA256(device\_secret, method + path + timestamp + bodyHash)
* `X-Timestamp` must be within ±120s of server time; server rejects stale requests
* `X-Device-Id` required, server fetches device secret hash and verifies HMAC using derived key

### Middleware protections

* **Rate limiting** in Redis per device/IP/user
* **Temporary lockout** after N failed auth attempts
* **Request validation** (schema + business rules) before calling Supabase
* **Accept only JSON** and reject unknown content types

### Secrets handling

* Service role key ONLY in server env (Vercel secret)
* Rotate service role key periodically
* Store no long-lived plaintext secrets in DB or logs

---

## 6. Deployment & Infra

### Hosting

* **Vercel** for Next.js API — serverless Node runtime
* **Supabase** managed Postgres + Storage
* **Redis** (e.g., Upstash, Redis Cloud) — for rate-limiting & token reuse cache (optional; alternatives: in-memory LRU with persistence but Redis recommended)

### CI/CD

* GitHub Actions pipeline:

  * run tests (unit, integration)
  * lint & typecheck
  * run security checks (npm audit)
  * deploy to Vercel

### Env variables (examples)

* `SUPABASE_URL`
* `SUPABASE_SERVICE_ROLE_KEY`
* `JWT_PRIVATE_KEY` / `JWT_PUBLIC_KEY` (for RS256)
* `REDIS_URL`
* `HMAC_KDF_SALT`

---

## 7. Performance, Caching & Cost Optimizations

* **Reject early**: middleware must validate and reject >90% of malicious calls before any DB call
* **Edge caching**: for public read endpoints (business listings) put CDN caching in front of API
* **Pagination & selective fields**: always request minimal column sets from Supabase
* **Signed URLs**: generate short-lived Supabase signed URLs for uploads/downloads instead of streaming through API
* **Rate limiting** to prevent bursty costs

---

## 8. Observability & Monitoring

* **Structured logs** (JSON) with correlation id for each request
* **Metrics**:

  * Requests/sec, Errors/sec
  * Auth failures, signature failures
  * DB queries saved by middleware
  * Token rotations & reuse events
* **Alerts**: high rate of signature failures, sudden increase in device registrations, token reuse detection
* **Tracing**: distributed traces for slow requests

---

## 9. Threat Model (Top attack scenarios & mitigations)

1. **Stolen device secret & refresh token**

   * Detection: refresh token reuse detection; revoke device; notify user
   * Mitigation: rotate tokens, short access token TTL, device revocation UI

2. **Replay attack**

   * Mitigation: timestamp checks, HMAC over timestamp and bodyHash

3. **Brute force auth attempts**

   * Mitigation: rate limiting, account lockout, CAPTCHA for web flows

4. **Malicious scripted client**

   * Mitigation: HMAC + app signature heuristics + rate limiting; manual review if attackers are sophisticated

5. **MITM**

   * Mitigation: TLS + certificate pinning in mobile app

---

## 10. Migration Plan

### Phase A — Preparation

* Add device tables and refresh token tables to DB. Use migrations.
* Implement server-side utilities for hashing secrets and refresh token storage.
* Implement middleware scaffolding.

### Phase B — API & Auth

* Implement `/api/auth/login`, `/api/devices/register`, `/api/auth/refresh`, `/api/auth/logout`.
* Implement JWT issuance and validation.
* Implement secure storage & rotation logic for refresh tokens.

### Phase C — Client Migration

* Remove Supabase SDK from mobile codebase in a feature branch.
* Implement the mobile HMAC client utilities and secure storage.
* Migrate one read-only endpoint (e.g., businesses list) to API proxy and validate.
* Roll out staged testing to a beta user group.

### Phase D — Hardening & Rollout

* Add rate limiting, anomaly detection, and logging.
* Migrate remaining endpoints and remove public anon keys from mobile
* Monitor and roll back if anomalies occur.

---

## 11. API & DB Schema (examples)

**SQL (Postgres) - devices**

```sql
CREATE TABLE devices (
  device_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  device_name text,
  platform text,
  device_secret_hash text NOT NULL,
  app_signature_hash text,
  revoked boolean DEFAULT false,
  last_seen_at timestamptz,
  created_at timestamptz DEFAULT now()
);

CREATE TABLE refresh_tokens (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  device_id uuid REFERENCES devices(device_id) ON DELETE CASCADE,
  refresh_token_hash text NOT NULL,
  issued_at timestamptz DEFAULT now(),
  expires_at timestamptz,
  rotated_from uuid,
  revoked boolean DEFAULT false
);
```

**Index** on `refresh_token_hash`, `device_id` for fast lookup.

---

## 12. Developer Checklist

* [ ] Create DB migrations for `devices` and `refresh_tokens`.
* [ ] Implement server JWT signing and verification (RS256 recommended).
* [ ] Implement device registration endpoint returning a single-use deviceSecret.
* [ ] Implement HMAC signing util (server + client) and middleware for verification.
* [ ] Implement refresh token rotation & reuse detection logic with hashes.
* [ ] Add rate-limiting using Redis.
* [ ] Add request validation (Zod/ajv) for all API routes.
* [ ] Remove Supabase client usage from mobile; update to call API.
* [ ] Add certificate pinning for mobile.
* [ ] Add logs/metrics and alerting rules.

---

## 13. Open Questions / Decisions

* Do we prefer HS256 (shared secret) or RS256 (private/public key) for access tokens? **RS256 recommended**.
* Redis vendor choice and cost (Upstash vs self-managed)
* How aggressive should default rate limits be? Start conservative and tune.
* Will backup/rotation of service role key be automated?

---

## 14. Next Steps

1. Approve schema and migration plan.
2. Implement a small spike: `POST /api/auth/login` + device registration + `GET /api/business/list` through middleware.
3. Validate end-to-end with a test mobile build and a small beta user group.
4. Harden with rate limits and token rotation; schedule audit.

---

*Document created by: Architect (Winston)*
