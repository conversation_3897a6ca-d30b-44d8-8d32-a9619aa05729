// Mock Redis client with proper separation - MUST come before importing the middleware
jest.mock('@upstash/redis', () => ({
  Redis: jest.fn().mockImplementation(() => ({
    get: jest.fn(),
    incr: jest.fn(),
    expire: jest.fn(),
  })),
}));

import { Redis } from '@upstash/redis';
import { NextRequest } from 'next/server';
import { POST as loginPOST } from '@/app/api/auth/login/route';
import { POST as refreshPOST } from '@/app/api/auth/refresh/route';
import { POST as deviceRegisterPOST } from '@/app/api/devices/register/route';

const mockRedisConstructor = Redis as jest.MockedClass<typeof Redis>;
let globalMockRedisInstance: jest.Mocked<Redis>;

// Mock Supabase client
jest.mock('@/utils/supabase/service-role', () => ({
  createServiceRoleClient: jest.fn(() => ({
    auth: {
      signInWithPassword: jest.fn(),
    },
    from: jest.fn(() => ({
      insert: jest.fn(() => ({
        select: jest.fn(() => ({
          single: jest.fn(() => ({ data: { device_id: 'mock-device-id' }, error: null })),
        })),
      })),
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(() => ({ data: { user_id: 'mock-user-id', device_id: 'mock-device-id' }, error: null })),
        })),
      })),
    })),
  })),
}));

// Mock auth utils
jest.mock('@/lib/auth/utils', () => ({
  getUserIdFromRequest: jest.fn(() => Promise.resolve('mock-user-id')),
}));

// Mock JWT functions
jest.mock('@/lib/auth/jwt', () => ({
  generateAccessToken: jest.fn(() => 'mock-access-token'),
  generateRefreshToken: jest.fn(() => 'mock-refresh-token'),
}));

// Mock hashing functions
jest.mock('@/lib/security/hashing', () => ({
  hashSecret: jest.fn(() => Promise.resolve('mock-hash')),
  compareSecret: jest.fn(() => Promise.resolve(true)),
}));

describe('Brute Force Protection Integration', () => {
  beforeAll(() => {
    // Get the Redis instance that was created during module import
    globalMockRedisInstance = mockRedisConstructor.mock.results[0]?.value as jest.Mocked<Redis>;
  });

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Set up mock Redis instance
    const mockRedisInstance = globalMockRedisInstance;
    
    if (!mockRedisInstance) {
      throw new Error('Mock Redis instance not found');
    }
    
    // Clear call history but keep mock functions intact
    mockRedisInstance.get.mockClear();
    mockRedisInstance.incr.mockClear();
    mockRedisInstance.expire.mockClear();
    
    // Mock environment variables
    process.env.BRUTE_FORCE_LOGIN_IP_LIMIT = '3'; // Low limit for testing
    process.env.BRUTE_FORCE_EMAIL_LIMIT = '2';
    process.env.BRUTE_FORCE_REFRESH_LIMIT = '5';
    process.env.BRUTE_FORCE_LOGIN_WINDOW = '3600';
    process.env.BRUTE_FORCE_REFRESH_WINDOW = '3600';
  });

  const createMockRequest = (body: any, headers: Record<string, string> = {}) => {
    return new NextRequest('http://localhost/api/test', {
      method: 'POST',
      body: JSON.stringify(body),
      headers: {
        'content-type': 'application/json',
        'x-forwarded-for': '***********',
        ...headers,
      },
    });
  };

  describe('Login endpoint brute-force protection', () => {
    const validLoginBody = {
      email: '<EMAIL>',
      password: 'password123',
      deviceInfo: {
        deviceName: 'Test Device',
        platform: 'web' as const,
      },
    };

    it('should allow login requests under rate limit', async () => {
      const request = createMockRequest(validLoginBody);
      const mockRedisInstance = globalMockRedisInstance;
      
      // Mock Supabase auth success
      const mockClient = require('@/utils/supabase/service-role').createServiceRoleClient();
      mockClient.auth.signInWithPassword.mockResolvedValue({
        data: { user: { id: 'user-123' } },
        error: null,
      });

      // Mock Redis responses - under limit
      mockRedisInstance.get
        .mockResolvedValueOnce(1) // IP count
        .mockResolvedValueOnce(0); // Email count
      mockRedisInstance.incr
        .mockResolvedValueOnce(2) // IP increment
        .mockResolvedValueOnce(1); // Email increment

      const response = await loginPOST(request);
      const body = await response.json();

      expect(response.status).toBe(200);
      expect(body.accessToken).toBe('mock-access-token');
      expect(mockRedisInstance.get).toHaveBeenCalledTimes(2);
      expect(mockRedisInstance.incr).toHaveBeenCalledTimes(2);
    });

    it('should block login requests over IP rate limit', async () => {
      const request = createMockRequest(validLoginBody);
      const mockRedisInstance = globalMockRedisInstance;

      // Mock Redis responses - at IP limit
      mockRedisInstance.get.mockResolvedValueOnce(3); // At IP limit

      const response = await loginPOST(request);
      const body = await response.json();

      expect(response.status).toBe(429);
      expect(body.error).toBe('Rate limit exceeded');
      expect(body.message).toBe('Too many attempts. Please try again later.');
      expect(mockRedisInstance.incr).not.toHaveBeenCalled();
    });

    it('should block login requests over email rate limit', async () => {
      const request = createMockRequest(validLoginBody);
      const mockRedisInstance = globalMockRedisInstance;

      // Mock Redis responses - IP passes, email fails
      mockRedisInstance.get
        .mockResolvedValueOnce(1) // IP count - under limit
        .mockResolvedValueOnce(2); // Email count - at limit

      mockRedisInstance.incr.mockResolvedValueOnce(2); // IP increment

      const response = await loginPOST(request);
      const body = await response.json();

      expect(response.status).toBe(429);
      expect(body.error).toBe('Rate limit exceeded');
      expect(mockRedisInstance.incr).toHaveBeenCalledTimes(1); // Only IP got incremented
    });
  });

  describe('Token refresh endpoint brute-force protection', () => {
    const validRefreshBody = {
      refreshToken: 'valid-refresh-token',
      deviceId: '123e4567-e89b-12d3-a456-426614174000',
    };

    it('should allow refresh requests under rate limit', async () => {
      const request = createMockRequest(validRefreshBody);
      const mockRedisInstance = globalMockRedisInstance;

      // Mock Supabase responses
      const mockClient = require('@/utils/supabase/service-role').createServiceRoleClient();
      mockClient.from.mockReturnValue({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            single: jest.fn(() => ({ data: { user_id: 'user-123' }, error: null })),
            eq: jest.fn(() => ({ data: [{ token_hash: 'mock-hash', expires_at: new Date(Date.now() + 86400000).toISOString(), token_id: 'token-123' }], error: null })),
          })),
        })),
        update: jest.fn(() => ({ eq: jest.fn(() => ({ error: null })) })),
        insert: jest.fn(() => ({ error: null })),
      });

      // Mock Redis responses - under limit
      mockRedisInstance.get
        .mockResolvedValueOnce(2) // IP count
        .mockResolvedValueOnce(3); // Device count
      mockRedisInstance.incr
        .mockResolvedValueOnce(3) // IP increment
        .mockResolvedValueOnce(4); // Device increment

      const response = await refreshPOST(request);

      expect(response.status).toBe(200);
      expect(mockRedisInstance.get).toHaveBeenCalledTimes(2);
      expect(mockRedisInstance.incr).toHaveBeenCalledTimes(2);
    });

    it('should block refresh requests over device rate limit', async () => {
      const request = createMockRequest(validRefreshBody);
      const mockRedisInstance = globalMockRedisInstance;

      // Mock Redis responses - IP passes, device fails
      mockRedisInstance.get
        .mockResolvedValueOnce(10) // IP count - under limit
        .mockResolvedValueOnce(5); // Device count - at limit

      mockRedisInstance.incr.mockResolvedValueOnce(11); // IP increment

      const response = await refreshPOST(request);
      const body = await response.json();

      expect(response.status).toBe(429);
      expect(body.error).toBe('Rate limit exceeded');
      expect(mockRedisInstance.incr).toHaveBeenCalledTimes(1); // Only IP got incremented
    });
  });

  describe('Device registration endpoint brute-force protection', () => {
    const validDeviceBody = {
      deviceName: 'Test Device',
      platform: 'web' as const,
      appSignatureHash: 'mock-signature',
    };

    it('should allow device registration under rate limit', async () => {
      const request = createMockRequest(validDeviceBody);
      const mockRedisInstance = globalMockRedisInstance;

      // Mock Supabase responses
      const mockClient = require('@/utils/supabase/service-role').createServiceRoleClient();
      mockClient.from.mockReturnValue({
        insert: jest.fn(() => ({
          select: jest.fn(() => ({
            single: jest.fn(() => ({ data: { device_id: 'device-123' }, error: null })),
          })),
        })),
      });

      // Mock Redis responses - under limit
      mockRedisInstance.get.mockResolvedValueOnce(2); // IP count
      mockRedisInstance.incr.mockResolvedValueOnce(3); // IP increment

      const response = await deviceRegisterPOST(request);

      expect(response.status).toBe(200);
      expect(mockRedisInstance.get).toHaveBeenCalledTimes(1);
      expect(mockRedisInstance.incr).toHaveBeenCalledTimes(1);
    });

    it('should block device registration over IP rate limit', async () => {
      const request = createMockRequest(validDeviceBody);
      const mockRedisInstance = globalMockRedisInstance;

      // Mock Redis responses - at limit (5 for device registration)
      mockRedisInstance.get.mockResolvedValueOnce(5);

      const response = await deviceRegisterPOST(request);
      const body = await response.json();

      expect(response.status).toBe(429);
      expect(body.error).toBe('Rate limit exceeded');
      expect(mockRedisInstance.incr).not.toHaveBeenCalled();
    });
  });

  describe('Rate limit headers', () => {
    it('should include rate limit headers in blocked responses', async () => {
      const request = createMockRequest({
        email: '<EMAIL>',
        password: 'password123',
        deviceInfo: { deviceName: 'Test Device', platform: 'web' },
      });
      const mockRedisInstance = globalMockRedisInstance;

      // Mock Redis responses - at limit
      mockRedisInstance.get.mockResolvedValueOnce(3);

      const response = await loginPOST(request);

      expect(response.headers.get('X-RateLimit-Limit')).toBeTruthy();
      expect(response.headers.get('X-RateLimit-Remaining')).toBe('0');
      expect(response.headers.get('Retry-After')).toBeTruthy();
    });
  });
});