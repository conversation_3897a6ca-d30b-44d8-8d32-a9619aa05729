### User Story 1.4: Implement Type Synchronization Process

**Description:** This story involves creating a formal, documented process for keeping the auto-generated Supabase types synchronized between the `dukancard` (web/api) and `dukancard-app` (mobile) projects.

**Acceptance Criteria:**
- A clear guide explaining the process is created at the project root.
- The process is officially adopted as part of the team's "Definition of Done" for database-related tasks.

---

### Development Tasks

-   [ ] **1. Create the Type Synchronization Guide**
    *   Create a new file at the root of the entire workspace.
    *   **File Path:** `C:\web-app\SHARED_TYPES_GUIDE.md`
    *   Add the following content to the file. This document will serve as the single source of truth for this critical process.

    ````markdown
    # Shared Types Synchronization Guide

    ## 1. Overview

    This project contains two separate applications (`dukancard` and `dukancard-app`) that share a single Supabase database. To ensure both applications are always aware of the correct database schema and to prevent data-related bugs, we use TypeScript types generated directly from the database schema.

    This guide documents the **mandatory process** for updating and synchronizing these types after any database schema change.

    **The `dukancard` (Next.js) project is the single source of truth for generating the types.** The flow is always one-way: `dukancard` -> `dukancard-app`.

    ---

    ## 2. The Synchronization Process

    This process must be followed for **any task that includes a database migration**.

    **Step 1: Make Database Changes**
    *   Create your new `.sql` migration file in the `dukancard/supabase/migrations` directory as usual.

    **Step 2: Apply Migrations and Reset Local Database**
    *   Run the reset command from the `dukancard` directory. This will apply all migrations, including your new one.
    *   `supabase db reset`

    **Step 3: Regenerate the Master Types File**
    *   Once the database is up to date, run the following command from the `dukancard` directory to regenerate the master `supabase.ts` file.
    *   `supabase gen types typescript --local > lib/types/supabase.ts`

    **Step 4: Copy the Updated Types File**
    *   Manually copy the newly updated file from the `dukancard` project:
    *   `dukancard/lib/types/supabase.ts`

    **Step 5: Paste and Overwrite in the Mobile App**
    *   Paste the file into the `dukancard-app` project, overwriting the existing file at:
    *   `dukancard-app/src/types/supabase.ts`

    **Step 6: Commit Changes**
    *   Commit the new migration file and the updated `supabase.ts` file in the `dukancard` project.
    *   Commit the updated `supabase.ts` file in the `dukancard-app` project.

    ---

    ## 3. Definition of Done

    A pull request that contains a database migration will not be considered "Done" and cannot be merged unless:

    -   [ ] The PR for the `dukancard` repository includes the updated `supabase.ts` file.
    -   [ ] A corresponding PR for the `dukancard-app` repository has been created with its updated `supabase.ts` file.
    ````

-   [ ] **2. Add Process to Team's Official Workflow**
    *   **Task:** Announce this new guide to the development team.
    *   **Action:** Ensure all team members have read and understood the process.
    *   **Action:** Formally add the checklist from the guide to the Pull Request templates or team's official "Definition of Done".