# PRD: Dukancard Unified API Layer & Security Refactor

## 1. Summary
This project aims to:
1. **Remove Supabase SDK and direct calls from `dukancard-app`** (React Native).
2. **Refactor `dukancard` (Next.js)** to serve as the **single backend API** for both `dukancard` (web) and `dukancard-app` (mobile).
3. **Centralize all database interactions** in `dukancard` API routes, acting as a secure proxy to Supabase.
4. **Implement multi-layer API security** to ensure only the official Dukancard apps can connect.
5. **Introduce request filtering middleware** to block spam, hacks, brute-force, and abuse before hitting Supabase — reducing costs.

---

## 2. Goals
- **Unify backend logic** to avoid code duplication and inconsistencies between web and mobile.
- **Eliminate Supabase keys from mobile app** to prevent leakage.
- **Secure the API** against unauthorized access, replay attacks, bots, and automated abuse.
- **Reduce Supabase load & costs** by rejecting malicious requests before they reach Supabase.
- **Improve maintainability** by having one central place for business logic.

---

## 3. Non-Goals
- Changing business logic unrelated to authentication or security.
- Replacing Supabase as the backend database (still using Supabase as DB/storage).
- Adding third-party attestation services (e.g., Firebase App Check).

---

## 4. Technical Approach

### 4.1 Architecture Changes
- `dukancard-app` → All Supabase SDK usage removed.  
  - All data operations go through Next.js API endpoints.
- `dukancard` → API layer handles:
  - Auth (JWT + refresh token rotation)
  - Device registration & per-device secret
  - HMAC request signing
  - Request filtering middleware
  - Supabase service role key usage (server-side only)
- **Supabase**: Only accessible by Next.js API (no public keys in client).

---

### 4.2 Data Contract & Type Safety
To ensure consistency and type safety between the `dukancard` API and the `dukancard-app` mobile client, we will use the auto-generated TypeScript definitions from the Supabase CLI as the single source of truth.

- **Generation:** After any database schema change, the master type definition file will be regenerated in the `dukancard` project by running `supabase gen types typescript`.
- **Synchronization:** This generated `supabase.ts` file will be copied from the `dukancard` project to the `dukancard-app` project. This manual copy is a required step in the Definition of Done for any task that modifies the database schema.
- **Usage:** Both applications will import types directly from their local copy of this `supabase.ts` file. This provides compile-time safety, ensuring that the client and server code are always aligned with the database schema.

---

### 4.3 Authentication & Session
- Short-lived **access token** (JWT) + long-lived **refresh token** (rotated on use).
- Per-device **device secret** stored securely on client.
- **HMAC request signing** using device secret to prevent tampering & replay.
- **Silent refresh** flow for mobile to maintain persistent login.

---

### 4.4 Client Verification
- **Device registration** API assigns `deviceId` + `deviceSecret`.
- **Signature check** on every API call using:
- **Timestamp freshness check** (< 2 min).
- **App signature hash** verification on device registration.

---

### 4.5 Request Filtering Middleware
- Applied to **all API routes**:
1. **Authentication check** — valid JWT required.
2. **Device verification** — valid HMAC signature.
3. **Rate limiting** — per IP, per device, per user.
4. **Geo/IP checks** — optional blocking based on region.
5. **Brute-force protection** — lockout after repeated failures.
6. **Supabase proxy logic** — only clean, validated requests reach Supabase.

---

### 4.6 API Layer Responsibilities
- **Auth routes**:
- `/api/auth/login`
- `/api/auth/refresh`
- `/api/auth/logout`
- `/api/devices/register`
- **Business logic routes**:
- All data read/write routes to Supabase.
- **Security middleware**:
- JWT verification
- HMAC verification
- Timestamp & replay protection
- IP/device-based rate limiting
- Request logging for anomaly detection

---

### 4.7 Middleware to Protect Supabase
- **Placement**: Before any Supabase query in API routes.
- **Functionality**:
- Reject requests failing auth/device/signature checks.
- Log suspicious activity.
- Block repeated offenders via in-memory/IP ban list.
- Avoid unnecessary Supabase calls → reduces DB usage & costs.

---

## 5. Security Layers
1. **JWT auth** — short-lived access token, rotated refresh token.
2. **Device secret** — unique per registered device.
3. **HMAC signatures** — prevent tampering & replay.
4. **Timestamp freshness** — reject stale requests.
5. **App signature/package checks** — detect tampered apps.
6. **Rate limiting + WAF rules** — block high-volume abuse.
7. **Revocation system** — allow user & admin to revoke devices/tokens.
8. **Supabase access restricted** — only API server uses service role key.

---

## 6. Risks & Mitigations
| Risk | Impact | Mitigation |
|------|--------|------------|
| Device compromise | Attacker steals refresh token + device secret | Token rotation, reuse detection, device revocation |
| Replay attacks | Fraudulent re-use of signed requests | Timestamp freshness check, HMAC |
| Brute-force/bot traffic | High costs, DB strain | Rate limiting, middleware filtering before Supabase |
| App tampering | Fake client calls API | App signature checks, HMAC with secret not in code |
| Token theft | Account takeover | Refresh token binding to device, reuse detection |

---

## 7. Deliverables
- [ ] API refactor in `dukancard` with unified business logic.
- [ ] Remove Supabase SDK from `dukancard-app`.
- [ ] Device registration + secret storage in secure storage.
- [ ] JWT + refresh token auth flow with rotation & reuse detection.
- [ ] HMAC signing middleware in API routes.
- [ ] Request filtering middleware (rate limiting, IP/device bans).
- [ ] Supabase service role usage only from server.
- [ ] Documentation of API contract for mobile & web teams.

---

## 8. Success Metrics
- **Security**: No Supabase keys exposed in mobile build.
- **Performance**: < 100ms API auth overhead for verified requests.
- **Cost saving**: Reduction in fake Supabase calls > 90%.
- **Consistency**: Single API logic for web & mobile.
- **Stability**: No unexpected logouts unless tokens revoked/expired.

---

## 9. Timeline (Proposed)
**Phase 1 (Week 1–2)** — API refactor & Supabase removal from mobile.  
**Phase 2 (Week 3)** — Implement auth & device registration.  
**Phase 3 (Week 4)** — Implement HMAC signing + middleware.  
**Phase 4 (Week 5)** — Request filtering & Supabase proxy layer.  
**Phase 5 (Week 6)** — Testing, monitoring, deployment.

---

## 10. Open Questions
- Should we allow guest (unauthenticated) requests for some public data?  
- Do we enforce IP/geo restrictions globally or only on sensitive endpoints?  
- Should we implement concurrent session limits per user?  

## 11. End-to-End Request Flow

Below is the sequence diagram for **dukancard-app → dukancard API → Supabase**.

```mermaid
sequenceDiagram
    autonumber
    participant App as Dukancard-App (React Native)
    participant API as Dukancard API (Next.js)
    participant MW as Security Middleware
    participant DB as Supabase (Service Role)

    Note over App: First-time login
    App->>API: POST /api/auth/login (email/password)
    API->>DB: Verify credentials (Supabase Auth)
    DB-->>API: User record
    API->>API: Generate accessToken (15m) & refreshToken (30d)
    API->>API: Register deviceId & deviceSecret (store hash)
    API-->>App: { accessToken, refreshToken, deviceId, deviceSecret }
    Note over App: Store in SecureStorage

    Note over App: Subsequent request to fetch data
    App->>API: GET /api/business/list
Headers: Authorization: Bearer <accessToken>
X-Device-Id, X-Timestamp, X-Signature(HMAC)
    API->>MW: Security middleware runs
    MW->>MW: Validate JWT (expiry, signature, claims)
    MW->>MW: Validate deviceId + HMAC signature
    MW->>MW: Check timestamp freshness (< 2 mins)
    MW->>MW: Apply rate limiting / IP blocking rules
    MW-->>API: ✅ Passed
    API->>DB: Query data using service role key
    DB-->>API: Business list
    API-->>App: JSON response

    Note over App: Access token expired
    App->>API: POST /api/auth/refresh
Body: { deviceId, refreshToken }
Headers: HMAC signature
    API->>MW: Verify device + HMAC signature
    MW->>API: Pass
    API->>API: Check refresh token validity (hash match)
    API->>API: Rotate refresh token & detect reuse
    API->>API: Issue new accessToken & refreshToken
    API-->>App: New tokens
```

## 12. API Contract Table

| Endpoint | Method | Auth Required | Required Headers | Request Body | Response (200 OK) | Error Codes |
|----------|--------|--------------|------------------|--------------|-------------------|-------------|
| **/api/auth/login** | POST | No | `Content-Type: application/json` | `{ "email": string, "password": string, "deviceInfo": object }` | `{ accessToken: string, refreshToken: string, deviceId: string, deviceSecret: string }` | `400` Missing fields<br>`401` Invalid credentials |
| **/api/auth/refresh** | POST | Yes (HMAC + refresh token) | `Content-Type: application/json`<br>`X-Device-Id: string`<br>`X-Timestamp: number`<br>`X-Signature: string` | `{ "refreshToken": string }` | `{ accessToken: string, refreshToken: string }` | `401` Invalid refresh token<br>`403` Invalid HMAC/device<br>`409` Token reuse detected |
| **/api/auth/logout** | POST | Yes (JWT + HMAC) | `Authorization: Bearer <accessToken>`<br>`X-Device-Id`<br>`X-Timestamp`<br>`X-Signature` | None | `{ success: true }` | `401` Invalid JWT<br>`403` Invalid device/HMAC |
| **/api/devices/register** | POST | Yes (JWT) | `Authorization: Bearer <accessToken>` | `{ "deviceName": string, "platform": "ios"|"android"|"web" }` | `{ deviceId: string, deviceSecret: string }` | `400` Invalid request<br>`403` Unauthorized |
| **/api/business/list** | GET | Yes (JWT + HMAC) | `Authorization: Bearer <accessToken>`<br>`X-Device-Id`<br>`X-Timestamp`<br>`X-Signature` | None | `{ businesses: Array<Business> }` | `401` Invalid JWT<br>`403` Invalid device/HMAC |
| **/api/business/create** | POST | Yes (JWT + HMAC) | `Authorization: Bearer <accessToken>`<br>`X-Device-Id`<br>`X-Timestamp`<br>`X-Signature` | `{ name: string, description: string, ... }` | `{ businessId: string, status: "created" }` | `400` Validation error<br>`401` Invalid JWT<br>`403` Invalid HMAC/device |
| **/api/user/profile** | GET | Yes (JWT + HMAC) | `Authorization: Bearer <accessToken>`<br>`X-Device-Id`<br>`X-Timestamp`<br>`X-Signature` | None | `{ id: string, name: string, email: string, ... }` | `401` Invalid JWT<br>`403` Invalid HMAC/device |

---

### **Header Requirements**
| Header | Description |
|--------|-------------|
| `Authorization` | `Bearer <accessToken>` — required for JWT auth endpoints |
| `X-Device-Id` | Unique identifier assigned at device registration |
| `X-Timestamp` | Unix epoch timestamp (ms) — must be within ±120s of server time |
| `X-Signature` | HMAC-SHA256 signature of `(method + path + timestamp + bodyHash)` using `deviceSecret` |

---

### **Error Response Format**
```json
{
  "error": true,
  "code": "INVALID_SIGNATURE",
  "message": "HMAC signature validation failed"
}
```

---

## 13. Project Epics

For a detailed breakdown of these epics into specific user stories, please see the [Security Refactor Stories document](./stories/security_refactor_stories.md).

This project will be broken down into the following high-level epics. Each epic contains a bundle of related features and will be further broken down into specific user stories and tasks. The epics are ordered strategically to build the foundation, create and stabilize the API with the web app first, and then migrate the mobile client.

### Epic 1: Foundational Backend & Database Architecture
**Summary:** As a System Architect, I want to establish the core database schema and server-side utilities required for the new security model, so that developers have a stable foundation to build the authentication and API layers upon.
**Key Features:**
- Database migrations for `devices` and `refresh_tokens` tables.
- Server-side hashing utilities for secrets.
- Configuration of new environment variables.
- Integration of a Redis client for caching and rate-limiting.
- Scaffolding for the main API security middleware.
**Primary Goal Addressed:** Preparation and setup for the entire project.

### Epic 2: Core Authentication & Device Registration API
**Summary:** As a User, I want a robust and secure authentication system that registers my device, so that I can securely log in, log out, and maintain a persistent session on trusted devices.
**Key Features:**
- `POST /api/devices/register` endpoint.
- `POST /api/auth/login` endpoint with JWT and refresh token issuance.
- `POST /api/auth/refresh` endpoint with token rotation and reuse detection.
- `POST /api/auth/logout` endpoint to revoke tokens.
**Primary Goal Addressed:** Establish the primary authentication flow.

### Epic 3: API Security Middleware & Request Hardening
**Summary:** As the Company, I want a multi-layered security middleware for the API, so that all incoming requests are validated, authenticated, and protected from common threats before they reach the core business logic or Supabase.
**Key Features:**
- HMAC request signature verification.
- Timestamp freshness checks to prevent replay attacks.
- Robust rate-limiting (per device, IP, and user).
- Brute-force detection and temporary lockouts.
- Request validation (e.g., Zod schemas) for all API routes.
**Primary Goal Addressed:** Secure the API against a wide range of attacks.

### Epic 4: `dukancard` (Web) Client & Server Refactoring to Use New API
**Summary:** As a Web User, I want the web application to be the first client of the new secure API, so that we can stabilize the API with our own product before migrating other clients.
**Key Features:**
- Refactor all existing Supabase calls in the `dukancard` server to go through the new, internal, secured API routes.
- Update the `dukancard` web frontend to consume the new `/api/*` endpoints for all data operations.
- Ensure the web client follows the complete auth and device registration flow.
**Primary Goal Addressed:** Unify backend logic and stabilize the API.

### Epic 5: `dukancard-app` (Mobile) Supabase SDK Removal & API Integration
**Summary:** As a Mobile User, I want the app to communicate through the new, hardened API instead of directly with Supabase, so that my data is more secure and the experience is seamless.
**Key Features:**
- Complete removal of the Supabase SDK from the React Native codebase.
- Implementation of secure storage for secrets and tokens (Keychain/Keystore).
- Creation of a mobile-side API client with HMAC signing for all requests.
- Refactoring of all features (profile, etc.) to use the now-stable API endpoints.
- Implementation of certificate pinning.
**Primary Goal Addressed:** Eliminate Supabase keys from the mobile app.

### Epic 6: Observability, Monitoring & Admin Tooling
**Summary:** As a DevOps Engineer, I want comprehensive logging, monitoring, and alerting for the new API layer, so that I can detect threats, troubleshoot issues, and ensure system health.
**Key Features:**
- Structured JSON logging with correlation IDs.
- Monitoring dashboards for key security and performance metrics.
- Alerts for security anomalies (e.g., high rate of auth failures).
- Admin-level endpoints for revoking devices or tokens.
- User-facing UI for managing trusted devices.
**Primary Goal Addressed:** Hardening, monitoring, and maintaining the system.