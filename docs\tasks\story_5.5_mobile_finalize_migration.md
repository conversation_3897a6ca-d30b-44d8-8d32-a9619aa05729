### User Story 5.5: Finalize Migration and Remove Supabase SDK

*   **User Story:** As a developer, I want to remove the Supabase SDK dependency entirely from the mobile app, so that the migration is complete and no insecure, direct database calls are possible.
*   **Acceptance Criteria:**
    *   The Supabase SDK package (`@supabase/supabase-js`) is completely removed from `dukancard-app/package.json`.
    *   The mobile application is fully functional and relies 100% on the `dukancard` API for all data and authentication.
    *   The app builds and runs correctly without the Supabase SDK.

---

### Development Tasks

-   [ ] **1. Comprehensive Codebase Audit for Supabase Calls**
    *   **Developer's Task:** Perform a thorough search across the entire `dukancard-app` codebase to identify any and all remaining direct calls to the Supabase client (e.g., `supabase.from(...)`, `supabase.auth(...)` outside of the new authentication flow). This includes any features not explicitly covered by previous stories (e.g., comments, likes, subscriptions, notifications, etc.).

-   [ ] **2. Refactor Remaining Features**
    *   **Developer's Task:** For any identified remaining features, create the necessary API client calls using the centralized API client (from Story 5.2) and refactor the mobile app's code to use them. Ensure all data fetching and mutation logic is updated.

-   [ ] **3. Remove Supabase SDK Dependency**
    *   **Developer's Task:** Once all direct Supabase client calls have been replaced by calls to the new internal API, remove the Supabase client library from the `dukancard-app` project's dependencies.
    *   **Action:** Remove `@supabase/supabase-js` (and any related Supabase client libraries) from `dukancard-app/package.json`.

-   [ ] **4. Verify Application Functionality**
    *   **Developer's Task:** Perform a full regression test of the mobile application to ensure all features work correctly without the Supabase SDK. This includes testing all user flows and edge cases.