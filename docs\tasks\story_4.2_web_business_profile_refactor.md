### User Story 4.2: Refactor Business Profile Features to Use the API

**User Story:** As a web user, I want to be able to view, create, and edit business profiles through the web interface, with all data flowing through the new secure API layer.

**Acceptance Criteria:**
- All functionality related to creating, reading, updating, and deleting business profiles works as before.
- All data for these features now flows exclusively through the new, secure API endpoints which are protected by the security middleware.
- Direct Supabase client calls related to the `business_profiles` table have been removed from the web application's frontend and server-side rendering logic.

---

### Development Tasks

-   [ ] **1. Design and Create Business Profile API Routes**
    *   **Developer's Task:** Based on the existing features in the web application, design and implement a full set of internal API routes for handling business profiles. This will likely include routes for listing, searching, fetching by ID/slug, creating, updating, and deleting. These routes must be protected by the security middleware stack we have defined.

-   [ ] **2. Implement Backend Logic for New Routes**
    *   **Developer's Task:** Implement the business logic inside the new API routes. This logic will use the **service role Supabase client** to perform the necessary operations on the `business_profiles` table. Ensure that all data returned to the client is sanitized and does not expose any sensitive information.

-   [ ] **3. Refactor Client-Side Data Fetching**
    *   **Developer's Task:** Audit the `dukancard` frontend codebase to identify all pages and components that currently **fetch** business profile data directly from Supabase (e.g., using `useEffect`, SWR, or React Query). Replace these direct calls with calls to your new internal `GET` API endpoints.

-   [ ] **4. Refactor Client-Side Data Mutations**
    *   **Developer's Task:** Identify all forms and UI elements that handle the **creation, updating, or deletion** of business profiles. Refactor the event handlers for these components to send requests to your new internal `POST`, `PATCH`, or `DELETE` API endpoints. Ensure the UI correctly handles loading states, success messages, and potential errors returned from the API.