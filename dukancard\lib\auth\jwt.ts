import jwt from 'jsonwebtoken';
import crypto from 'crypto';

// JWT Secret - should be loaded from environment variable
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';

// Token durations
export const ACCESS_TOKEN_DURATION = '15m'; // 15 minutes
export const REFRESH_TOKEN_DURATION = 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds

export interface JWTPayload {
  user_id: string;
  roles: string[];
  iat?: number;
  exp?: number;
}

/**
 * Generates a short-lived JWT access token
 * @param userId - The user's UUID
 * @param roles - Array of user roles (e.g., ['admin'], ['user'])
 * @returns Signed JWT token
 */
export function generateAccessToken(userId: string, roles: string[] = []): string {
  const payload: JWTPayload = {
    user_id: userId,
    roles: roles,
  };

  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: ACCESS_TOKEN_DURATION,
    issuer: 'dukancard-api',
    subject: userId,
  });
}

/**
 * Generates a cryptographically secure refresh token
 * @returns 64-character hex string
 */
export function generateRefreshToken(): string {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * Verifies and decodes a JWT access token
 * @param token - The JWT token to verify
 * @returns Decoded payload if valid, null if invalid
 */
export function verifyAccessToken(token: string): JWTPayload | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload;
    return decoded;
  } catch (error) {
    console.error('JWT verification failed:', error);
    return null;
  }
}

/**
 * Extracts the Bearer token from the Authorization header
 * @param authHeader - The Authorization header value
 * @returns The token string or null if invalid format
 */
export function extractBearerToken(authHeader?: string): string | null {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7); // Remove 'Bearer ' prefix
}