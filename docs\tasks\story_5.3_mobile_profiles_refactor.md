### User Story 5.3: Refactor Mobile Business & User Profile Features

*   **User Story:** As a mobile user, I want to view and manage business and user profiles within the app, with all data being fetched securely from the new API.
*   **Acceptance Criteria:**
    *   All business and user profile screens are fully functional.
    *   All data is now being routed through the secure `dukancard` API.
    *   Direct Supabase SDK calls for these features have been eliminated from the mobile codebase.

---

### Development Tasks

-   [ ] **1. Identify Relevant Mobile Modules**
    *   **Developer's Task:** Locate all mobile screens, components, and data services within the `dukancard-app` codebase that currently interact with `business_profiles` and `customer_profiles` (or directly with `auth.users` for user data).

-   [ ] **2. Refactor Data Fetching for Profiles**
    *   **Developer's Task:** Replace all direct Supabase client calls used for **fetching** business and user profile data with calls to the new centralized API client (from Story 5.2). Ensure that the data received from the API is correctly parsed and displayed in the UI.

-   [ ] **3. Refactor Data Mutation for Profiles**
    *   **<PERSON><PERSON><PERSON>'s Task:** Replace all direct Supabase client calls used for **creating, updating, or deleting** business and user profile data with calls to the new centralized API client. Ensure that forms and user interactions correctly send data to the API and handle responses (success, error, loading states).

-   [ ] **4. Update UI to Reflect API Responses**
    *   **Developer's Task:** Verify that the mobile UI correctly displays data received from the new API. This includes handling potential differences in data structure (if any, though `supabase.ts` should minimize this), loading indicators, and error messages returned by the API.