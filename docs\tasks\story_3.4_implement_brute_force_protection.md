### User Story 3.4: Implement Brute-Force Protection for Auth Endpoints

**Description:** This story adds a specialized, stricter layer of rate-limiting specifically to the login endpoint. This is designed to mitigate password guessing and credential stuffing attacks by temporarily locking out an IP address or user account after a small number of failed attempts.

**Acceptance Criteria:**
- The `/api/auth/login` endpoint is protected by a strict, failure-based rate limiter.
- After N failed attempts, an IP or account is temporarily blocked from making further attempts.
- A successful login resets the failure counter for that IP/account.

---

### Development Tasks

-   [ ] **1. Configure Brute-Force Rate Limiters**
    *   **Task:** In your rate limiter configuration module (`dukancard/lib/security/rate-limiter.ts`), create two new, specialized limiter instances.
    *   **Implementation:**
        *   `loginFailureLimiterByIp`: This limiter should have a low number of points (e.g., 5) and a longer duration (e.g., 15 minutes). It tracks failed attempts from a single IP.
        *   `loginFailureLimiterByEmail`: This limiter tracks failed attempts for a specific email address, using the same low points and duration.
        *   The configuration for these limiters must come from new environment variables (e.g., `AUTH_FAILURE_LIMIT_POINTS`, `AUTH_FAILURE_LOCKOUT_DURATION_SECONDS`) with sensible defaults.

-   [ ] **2. Add Brute-Force Check to Login Route**
    *   **Task:** At the very beginning of your `POST /api/auth/login` route handler, add the brute-force protection logic.
    *   **Logic Steps:**
        1.  **Extract Identifiers:** Get the request IP address and the `email` from the request body.
        2.  **Check for Existing Lockout:** Before attempting to log in, check if either the IP or the email has exceeded the failure limit. Use the `.get()` method from the `rate-limiter-flexible` library. If the current attempt count is already over the limit, reject immediately with `429 Too Many Requests`.
        3.  **Proceed with Login:** Allow the `supabase.auth.signInWithPassword` call to proceed.
        4.  **Handle Login Failure:** If `signInWithPassword` **fails**, use the `.consume()` method on both `loginFailureLimiterByIp` and `loginFailureLimiterByEmail`. This increments the failure count for both.
        5.  **Handle Login Success (Critical):** If `signInWithPassword` **succeeds**, you must reset the failure counters for that IP and email. Use the `.delete()` method on both limiters to reset their counts to zero. This prevents a user who simply forgot their password from being locked out after they successfully log in.

-   [ ] **3. Write Integration Tests**
    *   **Task:** Add specific tests for the brute-force logic to the login route's test file.
    *   **File Path:** `dukancard/__tests__/app/api/auth/login/route.test.ts`
    *   **Test Cases:**
        *   **Test 1: Account Lockout:** In a test loop, simulate 5 failed login attempts for the same email address. On the 6th attempt, assert that the request is rejected with a `429` status, even if the password provided is correct.
        *   **Test 2: IP Lockout:** Simulate 5 failed login attempts from the same IP but for *different* email addresses. On the 6th attempt from that IP, assert it is rejected with a `429` status.
        *   **Test 3: Successful Login Resets Counter:** Simulate 4 failed attempts. On the 5th attempt, provide the correct credentials and assert a `200 OK` response. Immediately after, simulate another failed attempt and assert that it is **not** blocked (i.e., the failure count is now 1, not 5).