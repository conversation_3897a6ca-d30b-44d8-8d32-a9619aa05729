// Mock Redis client with proper separation - MUST come before importing the middleware
jest.mock('@upstash/redis', () => ({
  Redis: jest.fn().mockImplementation(() => ({
    get: jest.fn(),
    incr: jest.fn(),
    expire: jest.fn(),
  })),
}));

// Get the mock instance for testing
import { Redis } from '@upstash/redis';
import { NextRequest, NextResponse } from 'next/server';
const mockRedisConstructor = Redis as jest.MockedClass<typeof Redis>;
let mockRedisInstance: jest.Mocked<Redis>;

// Store the mock instance globally to avoid timing issues
let globalMockRedisInstance: jest.Mocked<Redis>;

// Import NextResponse from next/server (will use the mock from jest.setup.ts)
import { NextResponse } from 'next/server';

// Import AFTER the mock is set up
import { 
  applyBruteForceProtection, 
  calculateProgressiveDelay,
  bruteForceProtectionMiddleware,
  extractEmailFromLoginRequest,
  extractDeviceIdFromRequest,
  getClientIP,
  BruteForceContext,
  BruteForceProtectionConfig,
} from '@/lib/middleware/bruteForceProtection';

describe('Brute Force Protection Middleware', () => {
  beforeAll(() => {
    // Get the Redis instance that was created during module import
    globalMockRedisInstance = mockRedisConstructor.mock.results[0]?.value as jest.Mocked<Redis>;
  });

  beforeEach(() => {
    // Use the global instance
    mockRedisInstance = globalMockRedisInstance;
    
    if (!mockRedisInstance) {
      throw new Error('Mock Redis instance not found');
    }
    
    // Clear call history but keep mock functions intact
    mockRedisInstance.get.mockClear();
    mockRedisInstance.incr.mockClear();
    mockRedisInstance.expire.mockClear();
    
    // Mock current time for consistent testing
    jest.spyOn(Date, 'now').mockReturnValue(1640995200000); // Fixed timestamp
    
    // Mock environment variables
    process.env.BRUTE_FORCE_LOGIN_IP_LIMIT = '10';
    process.env.BRUTE_FORCE_LOGIN_WINDOW = '3600';
    process.env.BRUTE_FORCE_EMAIL_LIMIT = '5';
    process.env.BRUTE_FORCE_REFRESH_LIMIT = '20';
    process.env.BRUTE_FORCE_REFRESH_WINDOW = '3600';
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  const createMockRequest = (headers: Record<string, string> = {}) => {
    return {
      headers: {
        get: (name: string) => headers[name.toLowerCase()] || null,
      },
    } as any;
  };

  describe('Progressive delay calculation', () => {
    it('should return 0 delay for first 3 attempts', () => {
      expect(calculateProgressiveDelay(1)).toBe(0);
      expect(calculateProgressiveDelay(2)).toBe(0);
      expect(calculateProgressiveDelay(3)).toBe(0);
    });

    it('should calculate exponential backoff starting from 4th attempt', () => {
      const delay4 = calculateProgressiveDelay(4);
      const delay5 = calculateProgressiveDelay(5);
      const delay6 = calculateProgressiveDelay(6);

      expect(delay4).toBeGreaterThan(0);
      expect(delay5).toBeGreaterThan(delay4);
      expect(delay6).toBeGreaterThan(delay5);
    });

    it('should cap delay at 30 seconds', () => {
      const delay = calculateProgressiveDelay(20); // Very high attempt count
      expect(delay).toBeLessThanOrEqual(30000);
    });

    it('should add jitter to prevent thundering herd', () => {
      const delays = Array.from({ length: 10 }, () => calculateProgressiveDelay(5));
      const uniqueDelays = new Set(delays);
      
      // With jitter, we should get different values (though not guaranteed in all cases)
      expect(uniqueDelays.size).toBeGreaterThan(1);
    });
  });

  describe('Login brute-force protection', () => {
    const loginContext: BruteForceContext = {
      operation: 'login',
      email: '<EMAIL>',
    };

    it('should allow login requests under IP limit', async () => {
      const request = createMockRequest({
        'x-forwarded-for': '***********',
      });

      // Mock Redis responses - under IP limit
      mockRedisInstance.get
        .mockResolvedValueOnce(3) // IP count
        .mockResolvedValueOnce(1); // Email count
      mockRedisInstance.incr
        .mockResolvedValueOnce(4) // IP increment
        .mockResolvedValueOnce(2); // Email increment

      const result = await applyBruteForceProtection(request, loginContext);

      expect(result.success).toBe(true);
      expect(mockRedisInstance.get).toHaveBeenCalledTimes(2);
      expect(mockRedisInstance.incr).toHaveBeenCalledTimes(2);
    });

    it('should block login requests over IP limit', async () => {
      const request = createMockRequest({
        'x-forwarded-for': '***********',
      });

      // Mock Redis responses - at IP limit
      mockRedisInstance.get.mockResolvedValueOnce(10); // At IP limit

      const result = await applyBruteForceProtection(request, loginContext);

      expect(result.success).toBe(false);
      expect(result.status).toBe(429);
      expect(result.error).toBe('Rate limit exceeded');
      expect(mockRedisInstance.incr).not.toHaveBeenCalled();
    });

    it('should block login requests over email limit', async () => {
      const request = createMockRequest({
        'x-forwarded-for': '***********',
      });

      // Mock Redis responses - IP passes, email fails
      mockRedisInstance.get
        .mockResolvedValueOnce(3) // IP count - under limit
        .mockResolvedValueOnce(5); // Email count - at limit

      mockRedisInstance.incr.mockResolvedValueOnce(4); // IP increment

      const result = await applyBruteForceProtection(request, loginContext);

      expect(result.success).toBe(false);
      expect(result.status).toBe(429);
      expect(mockRedisInstance.incr).toHaveBeenCalledTimes(1); // Only IP got incremented
    });

    it('should work without email in context', async () => {
      const request = createMockRequest({
        'x-forwarded-for': '***********',
      });

      const contextWithoutEmail: BruteForceContext = {
        operation: 'login',
      };

      // Mock Redis responses - only IP limiting
      mockRedisInstance.get.mockResolvedValueOnce(3);
      mockRedisInstance.incr.mockResolvedValueOnce(4);

      const result = await applyBruteForceProtection(request, contextWithoutEmail);

      expect(result.success).toBe(true);
      expect(mockRedisInstance.get).toHaveBeenCalledTimes(1); // Only IP check
    });

    it('should use custom limits when provided', async () => {
      const request = createMockRequest({
        'x-forwarded-for': '***********',
      });

      const customConfig: BruteForceProtectionConfig = {
        maxLoginAttemptsPerIP: 3,
        maxLoginAttemptsPerEmail: 2,
      };

      // Mock Redis responses - under custom limits
      mockRedisInstance.get
        .mockResolvedValueOnce(2) // IP count
        .mockResolvedValueOnce(1); // Email count
      mockRedisInstance.incr
        .mockResolvedValueOnce(3) // IP increment
        .mockResolvedValueOnce(2); // Email increment

      const result = await applyBruteForceProtection(request, loginContext, customConfig);

      expect(result.success).toBe(true);
    });
  });

  describe('Token refresh brute-force protection', () => {
    const refreshContext: BruteForceContext = {
      operation: 'refresh',
      deviceId: 'device-123',
    };

    it('should allow refresh requests under device limit', async () => {
      const request = createMockRequest({
        'x-forwarded-for': '***********0',
      });

      // Mock Redis responses
      mockRedisInstance.get
        .mockResolvedValueOnce(15) // IP count
        .mockResolvedValueOnce(10); // Device count
      mockRedisInstance.incr
        .mockResolvedValueOnce(16) // IP increment
        .mockResolvedValueOnce(11); // Device increment

      const result = await applyBruteForceProtection(request, refreshContext);

      expect(result.success).toBe(true);
    });

    it('should block refresh requests over device limit', async () => {
      const request = createMockRequest({
        'x-forwarded-for': '***********1',
      });

      // Mock Redis responses - IP passes, device fails
      mockRedisInstance.get
        .mockResolvedValueOnce(15) // IP count - under limit
        .mockResolvedValueOnce(20); // Device count - at limit

      mockRedisInstance.incr.mockResolvedValueOnce(16); // IP increment

      const result = await applyBruteForceProtection(request, refreshContext);

      expect(result.success).toBe(false);
      expect(result.status).toBe(429);
    });

    it('should work without device ID in context', async () => {
      const request = createMockRequest({
        'x-forwarded-for': '***********2',
      });

      const contextWithoutDevice: BruteForceContext = {
        operation: 'refresh',
      };

      // Mock Redis responses - only IP limiting
      mockRedisInstance.get.mockResolvedValueOnce(30);
      mockRedisInstance.incr.mockResolvedValueOnce(31);

      const result = await applyBruteForceProtection(request, contextWithoutDevice);

      expect(result.success).toBe(true);
      expect(mockRedisInstance.get).toHaveBeenCalledTimes(1); // Only IP check
    });
  });

  describe('Device registration brute-force protection', () => {
    const deviceRegisterContext: BruteForceContext = {
      operation: 'device_register',
    };

    it('should allow device registration under IP limit', async () => {
      const request = createMockRequest({
        'x-forwarded-for': '***********0',
      });

      // Mock Redis responses
      mockRedisInstance.get.mockResolvedValueOnce(3);
      mockRedisInstance.incr.mockResolvedValueOnce(4);

      const result = await applyBruteForceProtection(request, deviceRegisterContext);

      expect(result.success).toBe(true);
    });

    it('should block device registration over IP limit', async () => {
      const request = createMockRequest({
        'x-forwarded-for': '***********1',
      });

      // Mock Redis responses - at limit (5 for device registration)
      mockRedisInstance.get.mockResolvedValueOnce(5);

      const result = await applyBruteForceProtection(request, deviceRegisterContext);

      expect(result.success).toBe(false);
      expect(result.status).toBe(429);
    });
  });

  describe('Middleware wrapper', () => {
    it('should return NextResponse when rate limited', async () => {
      const request = createMockRequest({
        'x-forwarded-for': '************',
      });

      const context: BruteForceContext = {
        operation: 'login',
        email: '<EMAIL>',
      };

      // Mock Redis responses - at limit
      mockRedisInstance.get.mockResolvedValueOnce(10);

      const response = await bruteForceProtectionMiddleware(request, context);

      expect(response).toBeInstanceOf(NextResponse);
      expect(response?.status).toBe(429);
      
      // Check response body
      const responseBody = JSON.parse(await response!.text());
      expect(responseBody.error).toBe('Rate limit exceeded');
      expect(responseBody.message).toBe('Too many attempts. Please try again later.');
    });

    it('should return null when request is allowed', async () => {
      const request = createMockRequest({
        'x-forwarded-for': '************',
      });

      const context: BruteForceContext = {
        operation: 'login',
        email: '<EMAIL>',
      };

      // Mock Redis responses - under limit
      mockRedisInstance.get
        .mockResolvedValueOnce(3) // IP count
        .mockResolvedValueOnce(1); // Email count
      mockRedisInstance.incr
        .mockResolvedValueOnce(4) // IP increment
        .mockResolvedValueOnce(2); // Email increment

      const response = await bruteForceProtectionMiddleware(request, context);

      expect(response).toBeNull();
    });

    it('should include progressive delay in retry-after header', async () => {
      const request = createMockRequest({
        'x-forwarded-for': '************',
      });

      const context: BruteForceContext = {
        operation: 'login',
        email: '<EMAIL>',
      };

      // Mock Redis responses - simulate high attempt count
      mockRedisInstance.get.mockResolvedValueOnce(10); // At limit

      const response = await bruteForceProtectionMiddleware(request, context);

      expect(response).toBeInstanceOf(NextResponse);
      expect(response?.headers.get('Retry-After')).toBeTruthy();
    });
  });

  describe('Utility functions', () => {
    it('should extract email from login request body', () => {
      const validBody = { email: '<EMAIL>', password: 'secret' };
      expect(extractEmailFromLoginRequest(validBody)).toBe('<EMAIL>');

      const invalidBody = { username: 'test' };
      expect(extractEmailFromLoginRequest(invalidBody)).toBeUndefined();

      const nullBody = null;
      expect(extractEmailFromLoginRequest(nullBody)).toBeUndefined();
    });

    it('should extract device ID from request body', () => {
      const validBody = { deviceId: 'device-123', refreshToken: 'token' };
      expect(extractDeviceIdFromRequest(validBody)).toBe('device-123');

      const invalidBody = { token: 'test' };
      expect(extractDeviceIdFromRequest(invalidBody)).toBeUndefined();

      const nullBody = null;
      expect(extractDeviceIdFromRequest(nullBody)).toBeUndefined();
    });

    it('should extract client IP from headers', () => {
      const request1 = createMockRequest({
        'x-forwarded-for': '***********, ***********',
      });
      expect(getClientIP(request1)).toBe('***********');

      const request2 = createMockRequest({
        'x-real-ip': '************',
      });
      expect(getClientIP(request2)).toBe('************');

      const request3 = createMockRequest({});
      expect(getClientIP(request3)).toBe('unknown');
    });
  });

  describe('Error handling', () => {
    it('should handle invalid operation type', async () => {
      const request = createMockRequest({
        'x-forwarded-for': '************',
      });

      const invalidContext: BruteForceContext = {
        operation: 'invalid' as any,
      };

      const result = await applyBruteForceProtection(request, invalidContext);

      expect(result.success).toBe(false);
      expect(result.status).toBe(400);
      expect(result.error).toBe('Invalid operation type');
    });

    it('should handle Redis errors gracefully', async () => {
      const request = createMockRequest({
        'x-forwarded-for': '************',
      });

      const context: BruteForceContext = {
        operation: 'login',
        email: '<EMAIL>',
      };

      // Mock Redis error
      mockRedisInstance.get.mockRejectedValue(new Error('Redis connection failed'));

      const result = await applyBruteForceProtection(request, context);

      // Should allow request despite Redis error (fail-open)
      expect(result.success).toBe(true);
    });
  });
});