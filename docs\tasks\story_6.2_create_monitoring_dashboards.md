### User Story 6.2: Create Security & Performance Monitoring Dashboards

*   **User Story:** As a DevOps engineer, I want a set of monitoring dashboards, so that I can visualize key security and performance metrics for the API layer at a glance.
*   **Acceptance Criteria:**
    *   A "Security Overview" dashboard is present and correctly displays data for auth failures, HMAC failures, and lockouts.
    *   A "API Performance" dashboard is present and correctly displays data for request latency, volume, and error rates.

---

### Development Tasks

-   [ ] **1. Identify Monitoring Platform**
    *   **Developer's Task:** Confirm the specific monitoring platform to be used (e.g., Vercel Monitoring, Datadog, New Relic, Grafana with Loki/Prometheus, etc.). This will dictate the exact steps for configuration.

-   [ ] **2. Define Key Metrics for Extraction**
    *   **Develo<PERSON>'s Task:** Based on the structured logs from Story 6.1, identify the specific log fields and patterns that correspond to the required metrics.
    *   **Security Metrics:**
        *   `log.level: "warn"` or `"error"` for failed authentications (login, refresh).
        *   `log.message: "Invalid Signature"` or similar for HMAC failures.
        *   `log.message: "Too Many Requests"` from brute-force protection.
        *   `log.message: "Request has expired"` for timestamp issues.
        *   Specific log entries for token reuse events.
    *   **Performance Metrics:**
        *   `req.method`, `req.url`, `res.statusCode`, `responseTime` (from `pino-http` logs).

-   [ ] **3. Configure Log Parsers/Processors (if applicable)**
    *   **Developer's Task:** If your chosen monitoring platform requires specific parsing rules for structured logs (e.g., Datadog's Grok patterns, Splunk field extractions), configure these to correctly extract the metrics defined in Step 2.

-   [ ] **4. Build Security Dashboard**
    *   **Developer's Task:** Create a new dashboard in the monitoring platform.
    *   **Action:** Add widgets/panels to visualize the security metrics:
        *   Time-series graphs for failed login attempts, HMAC failures, and rate-limit blocks.
        *   Counters for total token reuse events.
        *   Tables showing top IPs/devices/users hitting security limits.

-   [ ] **5. Build Performance Dashboard**
    *   **Developer's Task:** Create a separate dashboard in the monitoring platform.
    *   **Action:** Add widgets/panels to visualize the performance metrics:
        *   Time-series graphs for API request volume and average/p95/p99 latency.
        *   Error rate percentage (4xx and 5xx responses).
        *   Breakdowns by API endpoint (e.g., top 10 slowest endpoints).

-   [ ] **6. Verify Data Flow and Dashboard Accuracy**
    *   **Developer's Task:** Generate test traffic (e.g., simulate failed logins, make many requests to trigger rate limits, introduce a temporary error in an API route).
    *   **Action:** Verify that the generated logs appear correctly in the monitoring platform and that the dashboards accurately reflect the test data.