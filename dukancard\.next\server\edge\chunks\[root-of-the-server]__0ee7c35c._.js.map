{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/lib/auth/middleware-jwt.ts"], "sourcesContent": ["import { NextRequest } from 'next/server';\nimport { verify } from 'jsonwebtoken';\n\nexport interface JWTPayload {\n  user_id: string;\n  roles: string[];\n  iat: number;\n  exp: number;\n}\n\n/**\n * Extract and validate JW<PERSON> token from Authorization header in middleware\n * Returns the decoded payload if valid, null if invalid or missing\n */\nexport function validateJWTInMiddleware(request: NextRequest): JWTPayload | null {\n  try {\n    const authHeader = request.headers.get('Authorization');\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return null;\n    }\n\n    const token = authHeader.substring(7); // Remove 'Bearer ' prefix\n    if (!token) {\n      return null;\n    }\n\n    const jwtSecret = process.env.JWT_SECRET;\n    if (!jwtSecret) {\n      console.error('JWT_SECRET is not defined in environment variables');\n      return null;\n    }\n\n    // Verify and decode the JWT\n    const decoded = verify(token, jwtSecret) as JWTPayload;\n    \n    // Additional validation\n    if (!decoded.user_id || !decoded.exp) {\n      return null;\n    }\n\n    // Check if token is expired (should not happen as verify() checks this, but extra safety)\n    const now = Math.floor(Date.now() / 1000);\n    if (decoded.exp < now) {\n      return null;\n    }\n\n    return decoded;\n  } catch (error) {\n    // Token is invalid, expired, or malformed\n    return null;\n  }\n}\n\n/**\n * Extract JWT token from cookies (for web client session persistence)\n * This would be used if we stored JWTs in HttpOnly cookies\n */\nexport function getJWTFromCookies(request: NextRequest): string | null {\n  try {\n    const accessTokenCookie = request.cookies.get('accessToken');\n    return accessTokenCookie?.value || null;\n  } catch (error) {\n    return null;\n  }\n}\n\n/**\n * Check if user has specific role based on JWT payload\n */\nexport function hasRole(payload: JWTPayload | null, role: string): boolean {\n  if (!payload || !payload.roles) {\n    return false;\n  }\n  return payload.roles.includes(role);\n}\n\n/**\n * Check if user is admin based on JWT payload\n */\nexport function isAdmin(payload: JWTPayload | null): boolean {\n  return hasRole(payload, 'admin');\n}"], "names": [], "mappings": ";;;;;;AACA;;AAaO,SAAS,wBAAwB,OAAoB;IAC1D,IAAI;QACF,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QACvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,OAAO;QACT;QAEA,MAAM,QAAQ,WAAW,SAAS,CAAC,IAAI,0BAA0B;QACjE,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,MAAM,YAAY,QAAQ,GAAG,CAAC,UAAU;QACxC,IAAI,CAAC,WAAW;YACd,QAAQ,KAAK,CAAC;YACd,OAAO;QACT;QAEA,4BAA4B;QAC5B,MAAM,UAAU,CAAA,GAAA,6IAAA,CAAA,SAAM,AAAD,EAAE,OAAO;QAE9B,wBAAwB;QACxB,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ,GAAG,EAAE;YACpC,OAAO;QACT;QAEA,0FAA0F;QAC1F,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;QACpC,IAAI,QAAQ,GAAG,GAAG,KAAK;YACrB,OAAO;QACT;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,0CAA0C;QAC1C,OAAO;IACT;AACF;AAMO,SAAS,kBAAkB,OAAoB;IACpD,IAAI;QACF,MAAM,oBAAoB,QAAQ,OAAO,CAAC,GAAG,CAAC;QAC9C,OAAO,mBAAmB,SAAS;IACrC,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAKO,SAAS,QAAQ,OAA0B,EAAE,IAAY;IAC9D,IAAI,CAAC,WAAW,CAAC,QAAQ,KAAK,EAAE;QAC9B,OAAO;IACT;IACA,OAAO,QAAQ,KAAK,CAAC,QAAQ,CAAC;AAChC;AAKO,SAAS,QAAQ,OAA0B;IAChD,OAAO,QAAQ,SAAS;AAC1B"}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/utils/supabase/service-role.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\r\nimport { Database } from '@/types/supabase';\r\n\r\nexport function createServiceRoleClient() {\r\n  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\r\n  const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\r\n\r\n  if (!supabaseUrl || !supabaseServiceRoleKey) {\r\n    throw new Error('Missing required Supabase environment variables for service role');\r\n  }\r\n\r\n  return createClient<Database>(supabaseUrl, supabaseServiceRoleKey, {\r\n    auth: {\r\n      autoRefreshToken: false,\r\n      persistSession: false,\r\n    },\r\n  });\r\n}"], "names": [], "mappings": ";;;AAAA;;AAGO,SAAS;IACd,MAAM;IACN,MAAM,yBAAyB,QAAQ,GAAG,CAAC,yBAAyB;IAEpE,IAAI,CAAC,eAAe,CAAC,wBAAwB;QAC3C,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,CAAA,GAAA,+LAAA,CAAA,eAAY,AAAD,EAAY,aAAa,wBAAwB;QACjE,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;QAClB;IACF;AACF"}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/utils/auth/jwt-middleware.ts"], "sourcesContent": ["import { NextResponse, type NextRequest } from \"next/server\";\nimport { validateJWTInMiddleware, JWTPayload } from \"@/lib/auth/middleware-jwt\";\nimport { createServiceRoleClient } from \"@/utils/supabase/service-role\";\n\n/**\n * JWT-based middleware for authentication and authorization\n * This replaces the Supabase auth-based middleware\n */\nexport async function updateSessionWithJWT(request: NextRequest) {\n  const { pathname } = request.nextUrl;\n\n  // Define protected path prefixes\n  const protectedPrefixes = [\n    \"/dashboard\",\n    \"/onboarding\", \n    \"/choose-role\",\n  ];\n\n  // Check if the current path is a protected path\n  const isProtectedRoute = protectedPrefixes.some((prefix) => pathname.startsWith(prefix));\n\n  // Try to get JWT from Authorization header (for API calls) or cookies (for web client)\n  let jwtPayload: JWTPayload | null = null;\n  \n  // First try Authorization header (for authenticated API requests)\n  jwtPayload = validateJWTInMiddleware(request);\n  \n  // If no JWT in header, try cookies (for web client)\n  if (!jwtPayload) {\n    const accessTokenCookie = request.cookies.get('accessToken');\n    if (accessTokenCookie) {\n      // Create a temporary request with the cookie value in Authorization header\n      const tempRequest = new Request(request.url, {\n        headers: {\n          ...Object.fromEntries(request.headers.entries()),\n          'Authorization': `Bearer ${accessTokenCookie.value}`\n        }\n      });\n      jwtPayload = validateJWTInMiddleware(tempRequest as any);\n    }\n  }\n\n  // Check if the user just logged out\n  const justLoggedOut = request.nextUrl.searchParams.get('logged_out') === 'true';\n\n  // === UNAUTHENTICATED USER LOGIC ===\n  if (!jwtPayload) {\n    // Redirect to login if user is not authenticated AND accessing a protected path\n    if (isProtectedRoute) {\n      const url = request.nextUrl.clone();\n      url.pathname = \"/login\";\n      url.searchParams.set(\"next\", pathname);\n      return NextResponse.redirect(url);\n    }\n    return NextResponse.next();\n  }\n\n  // === AUTHENTICATED USER LOGIC ===\n  const userId = jwtPayload.user_id;\n\n  try {\n    // Query user profiles to determine user type and status\n    const supabase = createServiceRoleClient();\n    \n    const [customerProfileRes, businessProfileRes] = await Promise.all([\n      supabase\n        .from(\"customer_profiles\")\n        .select(\"id\")\n        .eq(\"id\", userId)\n        .maybeSingle(),\n      supabase\n        .from(\"business_profiles\")\n        .select(\"id, business_slug\")\n        .eq(\"id\", userId)\n        .maybeSingle(),\n    ]);\n\n    const customerProfile = customerProfileRes.data;\n    const customerError = customerProfileRes.error;\n    const businessProfile = businessProfileRes.data;\n    const businessError = businessProfileRes.error;\n\n    if (customerError || businessError) {\n      // Allow request to proceed - profile fetch errors are not critical for middleware\n      console.warn('Profile fetch error in JWT middleware:', { customerError, businessError });\n    } else if (!customerProfile && !businessProfile) {\n      // No profile found in either table - first time user\n      // Allow access ONLY to the choose-role page OR the onboarding page\n      // EXCEPTION: If user just logged out, allow them to reach login page\n      if (pathname !== \"/choose-role\" && pathname !== \"/onboarding\" && !justLoggedOut) {\n        const url = request.nextUrl.clone();\n        url.pathname = \"/choose-role\";\n        return NextResponse.redirect(url);\n      }\n    } else {\n      // Profile found - determine user type\n      const userType = customerProfile ? \"customer\" : \"business\";\n\n      // If business user hasn't completed onboarding (no slug), redirect to onboarding\n      // EXCEPTION: If user just logged out, allow them to reach login page\n      if (userType === \"business\" && !businessProfile?.business_slug && pathname !== \"/onboarding\" && !justLoggedOut) {\n        const url = request.nextUrl.clone();\n        url.pathname = \"/onboarding\";\n        return NextResponse.redirect(url);\n      }\n\n      // Redirect away from public auth pages if logged in and profile exists,\n      // UNLESS they just logged out and are heading to the login page.\n      if (\n        (pathname === \"/login\" ||\n          pathname === \"/choose-role\") &&\n        !(pathname === \"/login\" && justLoggedOut)\n      ) {\n        const redirectPath =\n          userType === \"business\"\n            ? \"/dashboard/business\"\n            : \"/dashboard/customer\";\n        const url = request.nextUrl.clone();\n        url.pathname = redirectPath;\n        return NextResponse.redirect(url);\n      }\n\n      // Redirect away from onboarding if user is a customer\n      // EXCEPTION: If user just logged out, allow them to reach login page\n      if (pathname === \"/onboarding\" && userType === \"customer\" && !justLoggedOut) {\n        const url = request.nextUrl.clone();\n        url.pathname = \"/dashboard/customer\";\n        return NextResponse.redirect(url);\n      }\n\n      // Protect dashboard routes based on user type\n      // EXCEPTION: If user just logged out, allow them to reach login page\n      if (\n        pathname.startsWith(\"/dashboard/customer\") &&\n        userType !== \"customer\" &&\n        !justLoggedOut\n      ) {\n        const url = request.nextUrl.clone();\n        url.pathname = \"/dashboard/business\";\n        return NextResponse.redirect(url);\n      }\n      if (\n        pathname.startsWith(\"/dashboard/business\") &&\n        userType !== \"business\" &&\n        !justLoggedOut\n      ) {\n        const url = request.nextUrl.clone();\n        url.pathname = \"/dashboard/customer\";\n        return NextResponse.redirect(url);\n      }\n    }\n  } catch (error) {\n    console.error('Error in JWT middleware profile check:', error);\n    // On error, allow request to proceed but log the issue\n  }\n\n  // Create response with JWT payload attached (for downstream use)\n  const response = NextResponse.next();\n  \n  // Add user info to response headers for server components to access\n  response.headers.set('x-user-id', userId);\n  response.headers.set('x-user-roles', JSON.stringify(jwtPayload.roles || []));\n\n  return response;\n}"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;;;;AAMO,eAAe,qBAAqB,OAAoB;IAC7D,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,iCAAiC;IACjC,MAAM,oBAAoB;QACxB;QACA;QACA;KACD;IAED,gDAAgD;IAChD,MAAM,mBAAmB,kBAAkB,IAAI,CAAC,CAAC,SAAW,SAAS,UAAU,CAAC;IAEhF,uFAAuF;IACvF,IAAI,aAAgC;IAEpC,kEAAkE;IAClE,aAAa,CAAA,GAAA,wIAAA,CAAA,0BAAuB,AAAD,EAAE;IAErC,oDAAoD;IACpD,IAAI,CAAC,YAAY;QACf,MAAM,oBAAoB,QAAQ,OAAO,CAAC,GAAG,CAAC;QAC9C,IAAI,mBAAmB;YACrB,2EAA2E;YAC3E,MAAM,cAAc,IAAI,QAAQ,QAAQ,GAAG,EAAE;gBAC3C,SAAS;oBACP,GAAG,OAAO,WAAW,CAAC,QAAQ,OAAO,CAAC,OAAO,GAAG;oBAChD,iBAAiB,CAAC,OAAO,EAAE,kBAAkB,KAAK,EAAE;gBACtD;YACF;YACA,aAAa,CAAA,GAAA,wIAAA,CAAA,0BAAuB,AAAD,EAAE;QACvC;IACF;IAEA,oCAAoC;IACpC,MAAM,gBAAgB,QAAQ,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,kBAAkB;IAEzE,qCAAqC;IACrC,IAAI,CAAC,YAAY;QACf,gFAAgF;QAChF,IAAI,kBAAkB;YACpB,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;YACjC,IAAI,QAAQ,GAAG;YACf,IAAI,YAAY,CAAC,GAAG,CAAC,QAAQ;YAC7B,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;QACA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,mCAAmC;IACnC,MAAM,SAAS,WAAW,OAAO;IAEjC,IAAI;QACF,wDAAwD;QACxD,MAAM,WAAW,CAAA,GAAA,4IAAA,CAAA,0BAAuB,AAAD;QAEvC,MAAM,CAAC,oBAAoB,mBAAmB,GAAG,MAAM,QAAQ,GAAG,CAAC;YACjE,SACG,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,QACT,WAAW;YACd,SACG,IAAI,CAAC,qBACL,MAAM,CAAC,qBACP,EAAE,CAAC,MAAM,QACT,WAAW;SACf;QAED,MAAM,kBAAkB,mBAAmB,IAAI;QAC/C,MAAM,gBAAgB,mBAAmB,KAAK;QAC9C,MAAM,kBAAkB,mBAAmB,IAAI;QAC/C,MAAM,gBAAgB,mBAAmB,KAAK;QAE9C,IAAI,iBAAiB,eAAe;YAClC,kFAAkF;YAClF,QAAQ,IAAI,CAAC,0CAA0C;gBAAE;gBAAe;YAAc;QACxF,OAAO,IAAI,CAAC,mBAAmB,CAAC,iBAAiB;YAC/C,qDAAqD;YACrD,mEAAmE;YACnE,qEAAqE;YACrE,IAAI,aAAa,kBAAkB,aAAa,iBAAiB,CAAC,eAAe;gBAC/E,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;gBACjC,IAAI,QAAQ,GAAG;gBACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B;QACF,OAAO;YACL,sCAAsC;YACtC,MAAM,WAAW,kBAAkB,aAAa;YAEhD,iFAAiF;YACjF,qEAAqE;YACrE,IAAI,aAAa,cAAc,CAAC,iBAAiB,iBAAiB,aAAa,iBAAiB,CAAC,eAAe;gBAC9G,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;gBACjC,IAAI,QAAQ,GAAG;gBACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B;YAEA,wEAAwE;YACxE,iEAAiE;YACjE,IACE,CAAC,aAAa,YACZ,aAAa,cAAc,KAC7B,CAAC,CAAC,aAAa,YAAY,aAAa,GACxC;gBACA,MAAM,eACJ,aAAa,aACT,wBACA;gBACN,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;gBACjC,IAAI,QAAQ,GAAG;gBACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B;YAEA,sDAAsD;YACtD,qEAAqE;YACrE,IAAI,aAAa,iBAAiB,aAAa,cAAc,CAAC,eAAe;gBAC3E,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;gBACjC,IAAI,QAAQ,GAAG;gBACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B;YAEA,8CAA8C;YAC9C,qEAAqE;YACrE,IACE,SAAS,UAAU,CAAC,0BACpB,aAAa,cACb,CAAC,eACD;gBACA,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;gBACjC,IAAI,QAAQ,GAAG;gBACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B;YACA,IACE,SAAS,UAAU,CAAC,0BACpB,aAAa,cACb,CAAC,eACD;gBACA,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;gBACjC,IAAI,QAAQ,GAAG;gBACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0CAA0C;IACxD,uDAAuD;IACzD;IAEA,iEAAiE;IACjE,MAAM,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI;IAElC,oEAAoE;IACpE,SAAS,OAAO,CAAC,GAAG,CAAC,aAAa;IAClC,SAAS,OAAO,CAAC,GAAG,CAAC,gBAAgB,KAAK,SAAS,CAAC,WAAW,KAAK,IAAI,EAAE;IAE1E,OAAO;AACT"}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { type NextRequest, NextResponse } from \"next/server\";\r\nimport { updateSession } from \"./utils/supabase/middleware\";\r\nimport { updateSessionWithJWT } from \"./utils/auth/jwt-middleware\";\r\nimport { Ratelimit } from \"@upstash/ratelimit\";\r\nimport { Redis } from \"@upstash/redis\";\r\n\r\n// Initialize Redis client for rate limiting only\r\nconst redisUrl = process.env.UPSTASH_REDIS_REST_URL;\r\nconst redisToken = process.env.UPSTASH_REDIS_REST_TOKEN;\r\n\r\nif (!redisUrl || !redisToken) {\r\n  console.error(\"Upstash Redis URL or Token is not defined in environment variables.\");\r\n}\r\n\r\nconst redis = redisUrl && redisToken ? new Redis({ url: redisUrl, token: redisToken }) : null;\r\n\r\n// Initialize Rate Limiter\r\nconst maxRequests = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || \"10\");\r\nconst windowSeconds = parseInt(process.env.RATE_LIMIT_WINDOW_SECONDS || \"10\");\r\n\r\nconst ratelimit = redis\r\n  ? new Ratelimit({\r\n      redis: redis,\r\n      limiter: Ratelimit.slidingWindow(maxRequests, `${windowSeconds} s`),\r\n      analytics: true,\r\n      prefix: \"@upstash/ratelimit/dukancard\",\r\n    })\r\n  : null;\r\n\r\n\r\nexport async function middleware(request: NextRequest) {\r\n  // --- Test Environment Bypass START ---\r\n  // Check if we're in a test environment (Playwright E2E tests)\r\n  const isTestEnvironment = process.env.NODE_ENV === 'test' ||\r\n                           process.env.PLAYWRIGHT_TESTING === 'true' ||\r\n                           request.headers.get('x-playwright-testing') === 'true';\r\n\r\n  if (isTestEnvironment) {\r\n    // For test environment, we'll handle auth differently\r\n    // This allows us to test business logic without real authentication\r\n    return await handleTestEnvironment(request);\r\n  }\r\n  // --- Test Environment Bypass END ---\r\n\r\n  // --- Domain and HTTPS Redirect Logic START ---\r\n  const url = request.nextUrl.clone();\r\n  const hostname = url.hostname;\r\n  const protocol = url.protocol;\r\n\r\n  // Only apply redirects in production environment and exclude development/testing domains\r\n  const isDevelopmentDomain = hostname.includes('localhost') ||\r\n                              hostname.includes('ngrok.io') ||\r\n                              hostname.includes('ngrok-free.app') ||\r\n                              hostname.includes('127.0.0.1');\r\n\r\n  if (process.env.NODE_ENV === 'production' && !isDevelopmentDomain) {\r\n    let shouldRedirect = false;\r\n\r\n    // Check for www redirect (www.dukancard.in -> dukancard.in)\r\n    if (hostname.startsWith('www.')) {\r\n      url.hostname = hostname.replace('www.', '');\r\n      shouldRedirect = true;\r\n    }\r\n\r\n    // Check for HTTPS redirect (http:// -> https://)\r\n    if (protocol === 'http:') {\r\n      url.protocol = 'https:';\r\n      shouldRedirect = true;\r\n    }\r\n\r\n    // Perform redirect if needed\r\n    if (shouldRedirect) {\r\n      return NextResponse.redirect(url.toString(), 301); // Permanent redirect\r\n    }\r\n  }\r\n  // --- Domain and HTTPS Redirect Logic END ---\r\n\r\n  // --- Rate Limiting Logic START ---\r\n  // Apply rate limiting to API routes only (skip webhooks)\r\n  if (request.nextUrl.pathname.startsWith(\"/api/\") && !request.nextUrl.pathname.startsWith(\"/api/webhooks/\")) {\r\n    // Skip rate limiting if Redis is not configured\r\n    if (!ratelimit) {\r\n      console.warn(\"Rate limiting skipped: Redis not configured\");\r\n    } else {\r\n      // Get IP address: Check 'x-forwarded-for' header first, then fallback.\r\n      const forwardedFor = request.headers.get('x-forwarded-for');\r\n      // The header can contain multiple IPs (client, proxy1, proxy2). The client IP is usually the first one.\r\n      const ip = forwardedFor ? forwardedFor.split(',')[0].trim() : \"127.0.0.1\";\r\n\r\n      try {\r\n        // Use Upstash rate limiting\r\n        const { success, limit, remaining, reset } = await ratelimit.limit(ip);\r\n\r\n        if (!success) {\r\n          // Rate limit exceeded, return 429\r\n          return new NextResponse(\"Too Many Requests\", {\r\n            status: 429,\r\n            headers: {\r\n              \"X-RateLimit-Limit\": limit.toString(),\r\n              \"X-RateLimit-Remaining\": remaining.toString(),\r\n              \"X-RateLimit-Reset\": new Date(reset * 1000).toISOString(),\r\n            },\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Rate limiting error:\", error);\r\n        // If rate limiting fails, allow the request to proceed\r\n      }\r\n    }\r\n  }\r\n  // --- Rate Limiting Logic END ---\r\n\r\n\r\n  // Use JWT-based session validation instead of Supabase\r\n  return await updateSessionWithJWT(request);\r\n}\r\n\r\n/**\r\n * Handle requests in test environment\r\n * This replicates the complete Supabase middleware authentication flow\r\n * without making actual Supabase calls, allowing us to test business logic\r\n */\r\nasync function handleTestEnvironment(request: NextRequest) {\r\n  const { pathname } = request.nextUrl;\r\n\r\n  // Check for test authentication headers\r\n  const testAuthState = request.headers.get('x-test-auth-state');\r\n  const testUserType = request.headers.get('x-test-user-type');\r\n  const testHasProfile = request.headers.get('x-test-has-profile') === 'true';\r\n  const testBusinessSlug = request.headers.get('x-test-business-slug');\r\n  const testPlanId = request.headers.get('x-test-plan-id') || 'free';\r\n\r\n  // Define protected path prefixes (same as Supabase middleware)\r\n  const protectedPrefixes = [\r\n    \"/dashboard\",\r\n    \"/onboarding\",\r\n    \"/choose-role\",\r\n  ];\r\n\r\n  const isProtectedRoute = protectedPrefixes.some((prefix) => pathname.startsWith(prefix));\r\n\r\n  // Check if user just logged out (same logic as Supabase middleware)\r\n  const justLoggedOut = request.nextUrl.searchParams.get('logged_out') === 'true';\r\n\r\n  // === UNAUTHENTICATED USER LOGIC ===\r\n  if (testAuthState === 'unauthenticated' || testAuthState !== 'authenticated') {\r\n    // Redirect to login if user is not authenticated AND accessing a protected path\r\n    if (isProtectedRoute) {\r\n      const url = request.nextUrl.clone();\r\n      url.pathname = \"/login\";\r\n      url.searchParams.set(\"next\", pathname);\r\n      return NextResponse.redirect(url);\r\n    }\r\n    return NextResponse.next();\r\n  }\r\n\r\n  // === AUTHENTICATED USER LOGIC ===\r\n  if (testAuthState === 'authenticated') {\r\n    // === NO PROFILE LOGIC ===\r\n    if (!testHasProfile) {\r\n      // No profile found - first time user\r\n      // Allow access ONLY to choose-role page OR onboarding page\r\n      // EXCEPTION: If user just logged out, allow them to reach login page\r\n      if (pathname !== \"/choose-role\" && pathname !== \"/onboarding\" && !justLoggedOut) {\r\n        const url = request.nextUrl.clone();\r\n        url.pathname = \"/choose-role\";\r\n        return NextResponse.redirect(url);\r\n      }\r\n      // If already on choose-role or onboarding, allow the request\r\n      return NextResponse.next();\r\n    }\r\n\r\n    // === PROFILE EXISTS LOGIC ===\r\n    if (testHasProfile && testUserType) {\r\n      // === BUSINESS USER ONBOARDING CHECK ===\r\n      // If business user hasn't completed onboarding (no slug), redirect to onboarding\r\n      if (testUserType === \"business\" && !testBusinessSlug && pathname !== \"/onboarding\" && !justLoggedOut) {\r\n        const url = request.nextUrl.clone();\r\n        url.pathname = \"/onboarding\";\r\n        return NextResponse.redirect(url);\r\n      }\r\n\r\n      // === FREE TIER FEATURE ACCESS CHECKS ===\r\n      if (testUserType === \"business\") {\r\n        // Free plan restrictions\r\n        if (testPlanId === \"free\") {\r\n          // Check for analytics access\r\n          if (pathname.startsWith(\"/dashboard/business/analytics\")) {\r\n            // Redirect free tier users away from analytics pages\r\n            const url = request.nextUrl.clone();\r\n            url.pathname = \"/dashboard/business/plan\";\r\n            url.searchParams.set(\"upgrade\", \"analytics\");\r\n            return NextResponse.redirect(url);\r\n          }\r\n        }\r\n      }\r\n\r\n      // === REDIRECT AWAY FROM AUTH PAGES ===\r\n      // Redirect away from public auth pages if logged in and profile exists\r\n      if (\r\n        (pathname === \"/login\" || pathname === \"/choose-role\") &&\r\n        !(pathname === \"/login\" && justLoggedOut)\r\n      ) {\r\n        const redirectPath = testUserType === \"business\"\r\n          ? \"/dashboard/business\"\r\n          : \"/dashboard/customer\";\r\n        const url = request.nextUrl.clone();\r\n        url.pathname = redirectPath;\r\n        return NextResponse.redirect(url);\r\n      }\r\n\r\n      // === ONBOARDING REDIRECT FOR CUSTOMERS ===\r\n      // Redirect away from onboarding if user is a customer\r\n      if (pathname === \"/onboarding\" && testUserType === \"customer\" && !justLoggedOut) {\r\n        const url = request.nextUrl.clone();\r\n        url.pathname = \"/dashboard/customer\";\r\n        return NextResponse.redirect(url);\r\n      }\r\n\r\n      // === DASHBOARD ROUTE PROTECTION ===\r\n      // Protect dashboard routes based on user type\r\n      if (\r\n        pathname.startsWith(\"/dashboard/customer\") &&\r\n        testUserType !== \"customer\" &&\r\n        !justLoggedOut\r\n      ) {\r\n        const url = request.nextUrl.clone();\r\n        url.pathname = \"/dashboard/business\";\r\n        return NextResponse.redirect(url);\r\n      }\r\n      if (\r\n        pathname.startsWith(\"/dashboard/business\") &&\r\n        testUserType !== \"business\" &&\r\n        !justLoggedOut\r\n      ) {\r\n        const url = request.nextUrl.clone();\r\n        url.pathname = \"/dashboard/customer\";\r\n        return NextResponse.redirect(url);\r\n      }\r\n    }\r\n  }\r\n\r\n  // Default: allow request to proceed\r\n  return NextResponse.next();\r\n}\r\n\r\nexport const config = {\r\n  matcher: [\r\n    /*\r\n     * Match all request paths except forhe ones starting with:\r\n     * - _next/static (static files)\r\n     * - _next/image (image optimization files)\r\n     * - favicon.ico (favicon file)\r\n     * Feel free to modify this pattern to include more paths.\r\n     */\r\n    \"/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)\",\r\n  ],\r\n};"], "names": [], "mappings": ";;;;AAAA;AAAA;AAEA;AACA;AACA;AAAA;;;;;AAEA,iDAAiD;AACjD,MAAM,WAAW,QAAQ,GAAG,CAAC,sBAAsB;AACnD,MAAM,aAAa,QAAQ,GAAG,CAAC,wBAAwB;AAEvD,IAAI,CAAC,YAAY,CAAC,YAAY;IAC5B,QAAQ,KAAK,CAAC;AAChB;AAEA,MAAM,QAAQ,YAAY,aAAa,IAAI,qKAAA,CAAA,QAAK,CAAC;IAAE,KAAK;IAAU,OAAO;AAAW,KAAK;AAEzF,0BAA0B;AAC1B,MAAM,cAAc,SAAS,QAAQ,GAAG,CAAC,uBAAuB,IAAI;AACpE,MAAM,gBAAgB,SAAS,QAAQ,GAAG,CAAC,yBAAyB,IAAI;AAExE,MAAM,YAAY,QACd,IAAI,+JAAA,CAAA,YAAS,CAAC;IACZ,OAAO;IACP,SAAS,+JAAA,CAAA,YAAS,CAAC,aAAa,CAAC,aAAa,GAAG,cAAc,EAAE,CAAC;IAClE,WAAW;IACX,QAAQ;AACV,KACA;AAGG,eAAe,WAAW,OAAoB;IACnD,wCAAwC;IACxC,8DAA8D;IAC9D,MAAM,oBAAoB,oDAAyB,UAC1B,QAAQ,GAAG,CAAC,kBAAkB,KAAK,UACnC,QAAQ,OAAO,CAAC,GAAG,CAAC,4BAA4B;IAEzE,IAAI,mBAAmB;QACrB,sDAAsD;QACtD,oEAAoE;QACpE,OAAO,MAAM,sBAAsB;IACrC;IACA,sCAAsC;IAEtC,gDAAgD;IAChD,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;IACjC,MAAM,WAAW,IAAI,QAAQ;IAC7B,MAAM,WAAW,IAAI,QAAQ;IAE7B,yFAAyF;IACzF,MAAM,sBAAsB,SAAS,QAAQ,CAAC,gBAClB,SAAS,QAAQ,CAAC,eAClB,SAAS,QAAQ,CAAC,qBAClB,SAAS,QAAQ,CAAC;IAE9C,uCAAmE;;IAmBnE;IACA,8CAA8C;IAE9C,oCAAoC;IACpC,yDAAyD;IACzD,IAAI,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,mBAAmB;QAC1G,gDAAgD;QAChD,IAAI,CAAC,WAAW;YACd,QAAQ,IAAI,CAAC;QACf,OAAO;YACL,uEAAuE;YACvE,MAAM,eAAe,QAAQ,OAAO,CAAC,GAAG,CAAC;YACzC,wGAAwG;YACxG,MAAM,KAAK,eAAe,aAAa,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK;YAE9D,IAAI;gBACF,4BAA4B;gBAC5B,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,MAAM,UAAU,KAAK,CAAC;gBAEnE,IAAI,CAAC,SAAS;oBACZ,kCAAkC;oBAClC,OAAO,IAAI,6LAAA,CAAA,eAAY,CAAC,qBAAqB;wBAC3C,QAAQ;wBACR,SAAS;4BACP,qBAAqB,MAAM,QAAQ;4BACnC,yBAAyB,UAAU,QAAQ;4BAC3C,qBAAqB,IAAI,KAAK,QAAQ,MAAM,WAAW;wBACzD;oBACF;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,uDAAuD;YACzD;QACF;IACF;IACA,kCAAkC;IAGlC,uDAAuD;IACvD,OAAO,MAAM,CAAA,GAAA,0IAAA,CAAA,uBAAoB,AAAD,EAAE;AACpC;AAEA;;;;CAIC,GACD,eAAe,sBAAsB,OAAoB;IACvD,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,wCAAwC;IACxC,MAAM,gBAAgB,QAAQ,OAAO,CAAC,GAAG,CAAC;IAC1C,MAAM,eAAe,QAAQ,OAAO,CAAC,GAAG,CAAC;IACzC,MAAM,iBAAiB,QAAQ,OAAO,CAAC,GAAG,CAAC,0BAA0B;IACrE,MAAM,mBAAmB,QAAQ,OAAO,CAAC,GAAG,CAAC;IAC7C,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC,qBAAqB;IAE5D,+DAA+D;IAC/D,MAAM,oBAAoB;QACxB;QACA;QACA;KACD;IAED,MAAM,mBAAmB,kBAAkB,IAAI,CAAC,CAAC,SAAW,SAAS,UAAU,CAAC;IAEhF,oEAAoE;IACpE,MAAM,gBAAgB,QAAQ,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,kBAAkB;IAEzE,qCAAqC;IACrC,IAAI,kBAAkB,qBAAqB,kBAAkB,iBAAiB;QAC5E,gFAAgF;QAChF,IAAI,kBAAkB;YACpB,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;YACjC,IAAI,QAAQ,GAAG;YACf,IAAI,YAAY,CAAC,GAAG,CAAC,QAAQ;YAC7B,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;QACA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,mCAAmC;IACnC,IAAI,kBAAkB,iBAAiB;QACrC,2BAA2B;QAC3B,IAAI,CAAC,gBAAgB;YACnB,qCAAqC;YACrC,2DAA2D;YAC3D,qEAAqE;YACrE,IAAI,aAAa,kBAAkB,aAAa,iBAAiB,CAAC,eAAe;gBAC/E,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;gBACjC,IAAI,QAAQ,GAAG;gBACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B;YACA,6DAA6D;YAC7D,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;QAC1B;QAEA,+BAA+B;QAC/B,IAAI,kBAAkB,cAAc;YAClC,yCAAyC;YACzC,iFAAiF;YACjF,IAAI,iBAAiB,cAAc,CAAC,oBAAoB,aAAa,iBAAiB,CAAC,eAAe;gBACpG,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;gBACjC,IAAI,QAAQ,GAAG;gBACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B;YAEA,0CAA0C;YAC1C,IAAI,iBAAiB,YAAY;gBAC/B,yBAAyB;gBACzB,IAAI,eAAe,QAAQ;oBACzB,6BAA6B;oBAC7B,IAAI,SAAS,UAAU,CAAC,kCAAkC;wBACxD,qDAAqD;wBACrD,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;wBACjC,IAAI,QAAQ,GAAG;wBACf,IAAI,YAAY,CAAC,GAAG,CAAC,WAAW;wBAChC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;oBAC/B;gBACF;YACF;YAEA,wCAAwC;YACxC,uEAAuE;YACvE,IACE,CAAC,aAAa,YAAY,aAAa,cAAc,KACrD,CAAC,CAAC,aAAa,YAAY,aAAa,GACxC;gBACA,MAAM,eAAe,iBAAiB,aAClC,wBACA;gBACJ,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;gBACjC,IAAI,QAAQ,GAAG;gBACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B;YAEA,4CAA4C;YAC5C,sDAAsD;YACtD,IAAI,aAAa,iBAAiB,iBAAiB,cAAc,CAAC,eAAe;gBAC/E,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;gBACjC,IAAI,QAAQ,GAAG;gBACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B;YAEA,qCAAqC;YACrC,8CAA8C;YAC9C,IACE,SAAS,UAAU,CAAC,0BACpB,iBAAiB,cACjB,CAAC,eACD;gBACA,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;gBACjC,IAAI,QAAQ,GAAG;gBACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B;YACA,IACE,SAAS,UAAU,CAAC,0BACpB,iBAAiB,cACjB,CAAC,eACD;gBACA,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;gBACjC,IAAI,QAAQ,GAAG;gBACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B;QACF;IACF;IAEA,oCAAoC;IACpC,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}