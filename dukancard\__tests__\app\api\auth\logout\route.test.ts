import { POST } from '@/app/api/auth/logout/route';

// Mock the dependencies
jest.mock('@/lib/auth/jwt', () => ({
  verifyAccessToken: jest.fn(),
  extractBearerToken: jest.fn(),
}));

jest.mock('@/lib/security/hashing', () => ({
  compareSecret: jest.fn(),
}));

jest.mock('@/utils/supabase/service-role', () => ({
  createServiceRoleClient: jest.fn(),
}));

import { verifyAccessToken, extractBearerToken } from '@/lib/auth/jwt';
import { compareSecret } from '@/lib/security/hashing';
import { createServiceRoleClient } from '@/utils/supabase/service-role';

describe('/api/auth/logout', () => {
  let mockSupabase: any;
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Create a mock Supabase client with simplified structure
    mockSupabase = {
      from: jest.fn(),
    };
    
    (createServiceRoleClient as jest.Mock).mockReturnValue(mockSupabase);
  });

  describe('POST', () => {
    it('should successfully logout user with valid JWT and refresh token', async () => {
      const userId = '550e8400-e29b-41d4-a716-446655440001';
      
      // Mock successful JWT validation
      (extractBearerToken as jest.Mock).mockReturnValue('valid.jwt.token');
      (verifyAccessToken as jest.Mock).mockReturnValue({
        user_id: userId,
        roles: [],
      });
      
      // Mock refresh token lookup
      const mockToken = {
        token_id: 'token-1',
        token_hash: 'hashed-token',
      };
      
      // Mock Supabase queries
      mockSupabase.from.mockImplementation((table: string) => {
        if (table === 'refresh_tokens') {
          return {
            select: () => ({
              eq: () => ({
                eq: jest.fn().mockResolvedValue({
                  data: [mockToken],
                  error: null,
                })
              })
            }),
            update: () => ({
              eq: jest.fn().mockResolvedValue({ error: null })
            })
          };
        }
      });
      
      // Mock successful token comparison
      (compareSecret as jest.Mock).mockResolvedValue(true);

      const request = {
        headers: {
          get: jest.fn((header: string) => {
            if (header === 'Authorization') return 'Bearer valid.jwt.token';
            return null;
          }),
        },
        json: jest.fn().mockResolvedValue({
          refreshToken: 'valid-refresh-token',
        }),
      } as any;

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(200);
      expect(responseData.message).toBe('Logout successful');
      
      // Verify JWT validation was called
      expect(extractBearerToken).toHaveBeenCalledWith('Bearer valid.jwt.token');
      expect(verifyAccessToken).toHaveBeenCalledWith('valid.jwt.token');
      
      // Verify token comparison was called
      expect(compareSecret).toHaveBeenCalledWith('valid-refresh-token', 'hashed-token');
      
      // Verify Supabase methods were called
      expect(mockSupabase.from).toHaveBeenCalledWith('refresh_tokens');
    });

    it('should return 401 for missing Authorization header', async () => {
      // Mock extractBearerToken to return null for missing header
      (extractBearerToken as jest.Mock).mockReturnValue(null);
      
      const request = {
        headers: {
          get: jest.fn(() => null), // No Authorization header
        },
        json: jest.fn().mockResolvedValue({
          refreshToken: 'valid-refresh-token',
        }),
      } as any;

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(401);
      expect(responseData.error).toBe('Unauthorized');
      
      // Verify no token verification was attempted
      expect(verifyAccessToken).not.toHaveBeenCalled();
    });

    it('should return 401 for invalid JWT token', async () => {
      // Mock successful token extraction but failed verification
      (extractBearerToken as jest.Mock).mockReturnValue('invalid.jwt.token');
      (verifyAccessToken as jest.Mock).mockReturnValue(null);

      const request = {
        headers: {
          get: jest.fn((header: string) => {
            if (header === 'Authorization') return 'Bearer invalid.jwt.token';
            return null;
          }),
        },
        json: jest.fn().mockResolvedValue({
          refreshToken: 'valid-refresh-token',
        }),
      } as any;

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(401);
      expect(responseData.error).toBe('Invalid or expired token');
      
      // Verify verification was attempted
      expect(verifyAccessToken).toHaveBeenCalledWith('invalid.jwt.token');
    });

    it('should return 400 for invalid request body', async () => {
      const userId = '550e8400-e29b-41d4-a716-446655440001';
      
      // Mock successful JWT validation
      (extractBearerToken as jest.Mock).mockReturnValue('valid.jwt.token');
      (verifyAccessToken as jest.Mock).mockReturnValue({
        user_id: userId,
        roles: [],
      });

      const request = {
        headers: {
          get: jest.fn((header: string) => {
            if (header === 'Authorization') return 'Bearer valid.jwt.token';
            return null;
          }),
        },
        json: jest.fn().mockResolvedValue({
          refreshToken: '', // Empty refresh token
        }),
      } as any;

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(400);
      expect(responseData.error).toBe('Validation failed');
      expect(responseData.details).toBeInstanceOf(Array);
    });

    it('should return 200 even for non-existent refresh token (prevent info leakage)', async () => {
      const userId = '550e8400-e29b-41d4-a716-446655440001';
      
      // Mock successful JWT validation
      (extractBearerToken as jest.Mock).mockReturnValue('valid.jwt.token');
      (verifyAccessToken as jest.Mock).mockReturnValue({
        user_id: userId,
        roles: [],
      });
      
      // Mock refresh token lookup - no tokens found
      mockSupabase.from.mockImplementation((table: string) => {
        if (table === 'refresh_tokens') {
          return {
            select: () => ({
              eq: () => ({
                eq: jest.fn().mockResolvedValue({
                  data: [],
                  error: null,
                })
              })
            })
          };
        }
      });

      const request = {
        headers: {
          get: jest.fn((header: string) => {
            if (header === 'Authorization') return 'Bearer valid.jwt.token';
            return null;
          }),
        },
        json: jest.fn().mockResolvedValue({
          refreshToken: 'non-existent-refresh-token',
        }),
      } as any;

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(200);
      expect(responseData.message).toBe('Logout successful');
    });

    it('should return 200 for invalid refresh token (prevent info leakage)', async () => {
      const userId = '550e8400-e29b-41d4-a716-446655440001';
      
      // Mock successful JWT validation
      (extractBearerToken as jest.Mock).mockReturnValue('valid.jwt.token');
      (verifyAccessToken as jest.Mock).mockReturnValue({
        user_id: userId,
        roles: [],
      });
      
      // Mock refresh token lookup - token found but doesn't match
      const mockToken = {
        token_id: 'token-1',
        token_hash: 'different-hash',
      };
      
      mockSupabase.from.mockImplementation((table: string) => {
        if (table === 'refresh_tokens') {
          return {
            select: () => ({
              eq: () => ({
                eq: jest.fn().mockResolvedValue({
                  data: [mockToken],
                  error: null,
                })
              })
            })
          };
        }
      });
      
      // Mock token comparison failure
      (compareSecret as jest.Mock).mockResolvedValue(false);

      const request = {
        headers: {
          get: jest.fn((header: string) => {
            if (header === 'Authorization') return 'Bearer valid.jwt.token';
            return null;
          }),
        },
        json: jest.fn().mockResolvedValue({
          refreshToken: 'invalid-refresh-token',
        }),
      } as any;

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(200);
      expect(responseData.message).toBe('Logout successful');
      
      // Verify token comparison was attempted
      expect(compareSecret).toHaveBeenCalledWith('invalid-refresh-token', 'different-hash');
    });

    it('should return 500 for database errors', async () => {
      const userId = '550e8400-e29b-41d4-a716-446655440001';
      
      // Mock successful JWT validation
      (extractBearerToken as jest.Mock).mockReturnValue('valid.jwt.token');
      (verifyAccessToken as jest.Mock).mockReturnValue({
        user_id: userId,
        roles: [],
      });
      
      // Mock database error
      mockSupabase.from.mockImplementation((table: string) => {
        if (table === 'refresh_tokens') {
          return {
            select: () => ({
              eq: () => ({
                eq: jest.fn().mockResolvedValue({
                  data: null,
                  error: { message: 'Database error' },
                })
              })
            })
          };
        }
      });

      const request = {
        headers: {
          get: jest.fn((header: string) => {
            if (header === 'Authorization') return 'Bearer valid.jwt.token';
            return null;
          }),
        },
        json: jest.fn().mockResolvedValue({
          refreshToken: 'valid-refresh-token',
        }),
      } as any;

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(500);
      expect(responseData.error).toBe('Internal Server Error');
    });
  });
});