import React from 'react';
import { render, screen } from '@testing-library/react';
import { SocialLoginButton } from '@/app/(main)/login/components/SocialLoginButton';

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    error: jest.fn(),
    info: jest.fn(),
  },
}));

// Mock Supabase client
jest.mock('@/utils/supabase/client', () => ({
  createClient: jest.fn(() => ({
    auth: {
      signInWithOAuth: jest.fn(),
    },
  })),
}));

// Mock window.open to prevent actual opening
Object.defineProperty(window, 'open', {
  writable: true,
  value: jest.fn(),
});

// Mock window.location
Object.defineProperty(window, 'location', {
  writable: true,
  value: {
    origin: 'http://localhost:3000',
  },
});

describe('SocialLoginButton', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the Google login button correctly', () => {
    render(<SocialLoginButton />);
    
    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
    expect(screen.getByText('Login with Google')).toBeInTheDocument();
    expect(button).not.toBeDisabled();
  });

  it('disables the button when disabled prop is true', () => {
    render(<SocialLoginButton disabled={true} />);
    
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
  });

  it('displays the Google icon in the button', () => {
    render(<SocialLoginButton />);
    
    // Check that the SVG icon is rendered
    const svgIcon = document.querySelector('svg');
    expect(svgIcon).toBeInTheDocument();
    expect(svgIcon).toHaveAttribute('viewBox', '0 0 24 24');
  });

  it('applies correct CSS classes for styling', () => {
    render(<SocialLoginButton />);
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('cursor-pointer');
    expect(button).toHaveClass('bg-background');
    expect(button).toHaveClass('hover:bg-muted');
    expect(button).toHaveClass('border-border');
  });

  it('has correct button structure and accessibility', () => {
    render(<SocialLoginButton />);
    
    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
    // Button is accessible via role="button" which is what matters for screen readers
  });

  it('renders within proper container structure', () => {
    const { container } = render(<SocialLoginButton />);
    
    const wrapper = container.firstChild;
    expect(wrapper).toHaveClass('flex', 'justify-center', 'mb-5', 'sm:mb-6');
  });

  it('accepts redirectSlug prop without error', () => {
    expect(() => {
      render(<SocialLoginButton redirectSlug="test-slug" />);
    }).not.toThrow();
    
    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
  });

  it('accepts message prop without error', () => {
    expect(() => {
      render(<SocialLoginButton message="Test message" />);
    }).not.toThrow();
    
    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
  });

  it('accepts all props together without error', () => {
    expect(() => {
      render(
        <SocialLoginButton 
          redirectSlug="test-slug" 
          message="Test message" 
          disabled={false} 
        />
      );
    }).not.toThrow();
    
    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
    expect(button).not.toBeDisabled();
  });

  it('maintains button appearance when disabled prop is false', () => {
    render(<SocialLoginButton disabled={false} />);
    
    const button = screen.getByRole('button');
    expect(button).not.toBeDisabled();
    expect(screen.getByText('Login with Google')).toBeInTheDocument();
  });
});