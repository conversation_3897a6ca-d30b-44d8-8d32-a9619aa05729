import { NextResponse, type NextRequest } from "next/server";
import { validateJWTInMiddleware, JWTPayload } from "@/lib/auth/middleware-jwt";
import { createServiceRoleClient } from "@/utils/supabase/service-role";

/**
 * JWT-based middleware for authentication and authorization
 * This replaces the Supabase auth-based middleware
 */
export async function updateSessionWithJWT(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Define protected path prefixes
  const protectedPrefixes = [
    "/dashboard",
    "/onboarding", 
    "/choose-role",
  ];

  // Check if the current path is a protected path
  const isProtectedRoute = protectedPrefixes.some((prefix) => pathname.startsWith(prefix));

  // Try to get JWT from Authorization header (for API calls) or cookies (for web client)
  let jwtPayload: JWTPayload | null = null;
  
  // First try Authorization header (for authenticated API requests)
  jwtPayload = validateJWTInMiddleware(request);
  
  // If no JWT in header, try cookies (for web client)
  if (!jwtPayload) {
    const accessTokenCookie = request.cookies.get('accessToken');
    if (accessTokenCookie) {
      // Create a temporary request with the cookie value in Authorization header
      const tempRequest = new Request(request.url, {
        headers: {
          ...Object.fromEntries(request.headers.entries()),
          'Authorization': `Bearer ${accessTokenCookie.value}`
        }
      });
      jwtPayload = validateJWTInMiddleware(tempRequest as any);
    }
  }

  // Check if the user just logged out
  const justLoggedOut = request.nextUrl.searchParams.get('logged_out') === 'true';

  // === UNAUTHENTICATED USER LOGIC ===
  if (!jwtPayload) {
    // Redirect to login if user is not authenticated AND accessing a protected path
    if (isProtectedRoute) {
      const url = request.nextUrl.clone();
      url.pathname = "/login";
      url.searchParams.set("next", pathname);
      return NextResponse.redirect(url);
    }
    return NextResponse.next();
  }

  // === AUTHENTICATED USER LOGIC ===
  const userId = jwtPayload.user_id;

  try {
    // Query user profiles to determine user type and status
    const supabase = createServiceRoleClient();
    
    const [customerProfileRes, businessProfileRes] = await Promise.all([
      supabase
        .from("customer_profiles")
        .select("id")
        .eq("id", userId)
        .maybeSingle(),
      supabase
        .from("business_profiles")
        .select("id, business_slug")
        .eq("id", userId)
        .maybeSingle(),
    ]);

    const customerProfile = customerProfileRes.data;
    const customerError = customerProfileRes.error;
    const businessProfile = businessProfileRes.data;
    const businessError = businessProfileRes.error;

    if (customerError || businessError) {
      // Allow request to proceed - profile fetch errors are not critical for middleware
      console.warn('Profile fetch error in JWT middleware:', { customerError, businessError });
    } else if (!customerProfile && !businessProfile) {
      // No profile found in either table - first time user
      // Allow access ONLY to the choose-role page OR the onboarding page
      // EXCEPTION: If user just logged out, allow them to reach login page
      if (pathname !== "/choose-role" && pathname !== "/onboarding" && !justLoggedOut) {
        const url = request.nextUrl.clone();
        url.pathname = "/choose-role";
        return NextResponse.redirect(url);
      }
    } else {
      // Profile found - determine user type
      const userType = customerProfile ? "customer" : "business";

      // If business user hasn't completed onboarding (no slug), redirect to onboarding
      // EXCEPTION: If user just logged out, allow them to reach login page
      if (userType === "business" && !businessProfile?.business_slug && pathname !== "/onboarding" && !justLoggedOut) {
        const url = request.nextUrl.clone();
        url.pathname = "/onboarding";
        return NextResponse.redirect(url);
      }

      // Redirect away from public auth pages if logged in and profile exists,
      // UNLESS they just logged out and are heading to the login page.
      if (
        (pathname === "/login" ||
          pathname === "/choose-role") &&
        !(pathname === "/login" && justLoggedOut)
      ) {
        const redirectPath =
          userType === "business"
            ? "/dashboard/business"
            : "/dashboard/customer";
        const url = request.nextUrl.clone();
        url.pathname = redirectPath;
        return NextResponse.redirect(url);
      }

      // Redirect away from onboarding if user is a customer
      // EXCEPTION: If user just logged out, allow them to reach login page
      if (pathname === "/onboarding" && userType === "customer" && !justLoggedOut) {
        const url = request.nextUrl.clone();
        url.pathname = "/dashboard/customer";
        return NextResponse.redirect(url);
      }

      // Protect dashboard routes based on user type
      // EXCEPTION: If user just logged out, allow them to reach login page
      if (
        pathname.startsWith("/dashboard/customer") &&
        userType !== "customer" &&
        !justLoggedOut
      ) {
        const url = request.nextUrl.clone();
        url.pathname = "/dashboard/business";
        return NextResponse.redirect(url);
      }
      if (
        pathname.startsWith("/dashboard/business") &&
        userType !== "business" &&
        !justLoggedOut
      ) {
        const url = request.nextUrl.clone();
        url.pathname = "/dashboard/customer";
        return NextResponse.redirect(url);
      }
    }
  } catch (error) {
    console.error('Error in JWT middleware profile check:', error);
    // On error, allow request to proceed but log the issue
  }

  // Create response with JWT payload attached (for downstream use)
  const response = NextResponse.next();
  
  // Add user info to response headers for server components to access
  response.headers.set('x-user-id', userId);
  response.headers.set('x-user-roles', JSON.stringify(jwtPayload.roles || []));

  return response;
}