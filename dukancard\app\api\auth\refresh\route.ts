import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { generateAccessToken, generateRefreshToken } from '@/lib/auth/jwt';
import { hashSecret, compareSecret } from '@/lib/security/hashing';
import { createServiceRoleClient } from '@/utils/supabase/service-role';
import { 
  bruteForceProtectionMiddleware, 
  getClientIP 
} from '@/lib/middleware/bruteForceProtection';

const refreshSchema = z.object({
  refreshToken: z.string().min(1, "Refresh token is required"),
  deviceId: z.string().uuid("Device ID must be a valid UUID"),
});

export async function POST(req: NextRequest) {
  try {
    // 1. Parse and validate request body first
    const body = await req.json();
    const validation = refreshSchema.safeParse(body);
    if (!validation.success) {
      return new NextResponse(JSON.stringify({ 
        error: 'Validation failed',
        details: validation.error.issues 
      }), { 
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const { refreshToken, deviceId } = validation.data;

    // 2. Apply brute-force protection
    const ipAddress = getClientIP(req);

    const bruteForceCheck = await bruteForceProtectionMiddleware(req, {
      operation: 'refresh',
      deviceId,
      ipAddress,
    });

    if (bruteForceCheck) {
      return bruteForceCheck; // Rate limited
    }
    const supabase = createServiceRoleClient();

    // 3. Validate device exists
    const { data: deviceData, error: deviceError } = await supabase
      .from('devices')
      .select('user_id, device_id')
      .eq('device_id', deviceId)
      .single();

    if (deviceError || !deviceData) {
      return new NextResponse(JSON.stringify({ error: 'Invalid device ID' }), { 
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const userId = deviceData.user_id;

    // 4. Find all refresh tokens for this device to validate the provided token
    const { data: refreshTokens, error: refreshTokensError } = await supabase
      .from('refresh_tokens')
      .select('token_id, token_hash, revoked, expires_at')
      .eq('device_id', deviceId)
      .eq('revoked', false);

    if (refreshTokensError) {
      console.error('Error fetching refresh tokens:', refreshTokensError);
      return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 5. Find matching token by comparing hashes
    let matchingTokenId: string | null = null;
    for (const tokenRecord of refreshTokens || []) {
      const isMatch = await compareSecret(refreshToken, tokenRecord.token_hash);
      if (isMatch) {
        // Check if token is expired
        const expiresAt = new Date(tokenRecord.expires_at);
        if (expiresAt < new Date()) {
          return new NextResponse(JSON.stringify({ error: 'Refresh token expired' }), { 
            status: 401,
            headers: { 'Content-Type': 'application/json' }
          });
        }
        
        matchingTokenId = tokenRecord.token_id;
        break;
      }
    }

    if (!matchingTokenId) {
      // 6. Check if the token was already revoked (potential token theft)
      const { data: revokedTokens } = await supabase
        .from('refresh_tokens')
        .select('token_id')
        .eq('device_id', deviceId)
        .eq('revoked', true);

      if (revokedTokens && revokedTokens.length > 0) {
        // Check if the provided token matches any revoked token
        for (const revokedToken of revokedTokens) {
          const { data: fullRevokedToken } = await supabase
            .from('refresh_tokens')
            .select('token_hash')
            .eq('token_id', revokedToken.token_id)
            .single();

          if (fullRevokedToken) {
            const isRevokedMatch = await compareSecret(refreshToken, fullRevokedToken.token_hash);
            if (isRevokedMatch) {
              // Token reuse detected! Revoke all tokens for this device
              await supabase
                .from('refresh_tokens')
                .update({ revoked: true, revoked_at: new Date().toISOString() })
                .eq('device_id', deviceId);

              // Also revoke the device
              await supabase
                .from('devices')
                .update({ revoked: true, revoked_at: new Date().toISOString() })
                .eq('device_id', deviceId);

              return new NextResponse(JSON.stringify({ 
                error: 'Token reuse detected - all device tokens have been revoked' 
              }), { 
                status: 409,
                headers: { 'Content-Type': 'application/json' }
              });
            }
          }
        }
      }

      return new NextResponse(JSON.stringify({ error: 'Invalid refresh token' }), { 
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 7. Revoke the current refresh token (token rotation)
    const { error: revokeError } = await supabase
      .from('refresh_tokens')
      .update({ 
        revoked: true, 
        revoked_at: new Date().toISOString() 
      })
      .eq('token_id', matchingTokenId);

    if (revokeError) {
      console.error('Error revoking refresh token:', revokeError);
      return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 8. Get user roles (default to empty for now - will implement in Story 6.6)
    const userRoles: string[] = [];

    // 9. Generate new tokens
    const newAccessToken = generateAccessToken(userId, userRoles);
    const newRefreshToken = generateRefreshToken();

    // 10. Store new refresh token in database
    const newRefreshTokenHash = await hashSecret(newRefreshToken);
    const newRefreshTokenExpiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days

    const { error: storeError } = await supabase
      .from('refresh_tokens')
      .insert({
        user_id: userId,
        device_id: deviceId,
        token_hash: newRefreshTokenHash,
        expires_at: newRefreshTokenExpiresAt.toISOString(),
      });

    if (storeError) {
      console.error('Error storing new refresh token:', storeError);
      return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 11. Return new tokens
    return NextResponse.json({
      accessToken: newAccessToken,
      refreshToken: newRefreshToken,
    });

  } catch (error) {
    console.error('Unexpected error in token refresh:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), { 
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}