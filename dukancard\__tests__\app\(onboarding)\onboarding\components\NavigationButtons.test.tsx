import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { NavigationButtons } from '../../../../../app/(onboarding)/onboarding/components/NavigationButtons';
import { useRouter } from 'next/navigation';

// Mock useRouter
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

describe('NavigationButtons', () => {
  const mockOnNextStep = jest.fn();
  const mockOnPreviousStep = jest.fn();
  const mockOnSubmitIntended = jest.fn();
  const mockPush = jest.fn();

  beforeEach(() => {
    (useRouter as jest.Mock).mockReturnValue({
      push: mockPush,
    });
    jest.clearAllMocks();
  });

  const defaultProps = {
    isSubmitting: false,
    isCheckingSlug: false,
    slugAvailable: true,
    selectedPlan: {
      id: 'free',
      name: 'Free Plan',
      price: 'Free',
      period: 'forever',
      description: 'Free plan description',
      features: ['Basic Feature'],
      button: 'Get Started',
      available: true,
      featured: true,
      recommended: false
    },
    existingData: null,
    onNextStep: mockOnNextStep,
    onPreviousStep: mockOnPreviousStep,
    onSubmitIntended: mockOnSubmitIntended,
  };

  // Test for currentStep === 1
  it('renders Back and Next buttons for step 1', () => {
    render(<NavigationButtons {...defaultProps} currentStep={1} />);
    expect(screen.getByRole('button', { name: /Back/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Next/i })).toBeInTheDocument();
  });

  it('calls router.push("/choose-role") when Back button is clicked on step 1', () => {
    render(<NavigationButtons {...defaultProps} currentStep={1} />);
    fireEvent.click(screen.getByRole('button', { name: /Back/i }));
    expect(mockPush).toHaveBeenCalledWith('/choose-role');
  });

  it('calls onNextStep when Next button is clicked on step 1', () => {
    render(<NavigationButtons {...defaultProps} currentStep={1} />);
    fireEvent.click(screen.getByRole('button', { name: /Next/i }));
    expect(mockOnNextStep).toHaveBeenCalledTimes(1);
  });

  it('disables Next button on step 1 when isSubmitting is true', () => {
    render(<NavigationButtons {...defaultProps} currentStep={1} isSubmitting={true} />);
    expect(screen.getByRole('button', { name: /Next/i })).toBeDisabled();
  });

  // Test for currentStep < 4 (but not 1)
  it('renders Back and Next buttons for step 2', () => {
    render(<NavigationButtons {...defaultProps} currentStep={2} />);
    expect(screen.getByRole('button', { name: /Back/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Next/i })).toBeInTheDocument();
  });

  it('calls onPreviousStep when Back button is clicked on step 2', () => {
    render(<NavigationButtons {...defaultProps} currentStep={2} />);
    fireEvent.click(screen.getByRole('button', { name: /Back/i }));
    expect(mockOnPreviousStep).toHaveBeenCalledTimes(1);
  });

  it('calls onNextStep when Next button is clicked on step 2', () => {
    render(<NavigationButtons {...defaultProps} currentStep={2} />);
    fireEvent.click(screen.getByRole('button', { name: /Next/i }));
    expect(mockOnNextStep).toHaveBeenCalledTimes(1);
  });

  it('disables Next button on step 2 when isSubmitting is true', () => {
    render(<NavigationButtons {...defaultProps} currentStep={2} isSubmitting={true} />);
    expect(screen.getByRole('button', { name: /Next/i })).toBeDisabled();
  });

  it('disables Next button on step 2 when isCheckingSlug is true', () => {
    render(<NavigationButtons {...defaultProps} currentStep={2} isCheckingSlug={true} />);
    expect(screen.getByRole('button', { name: /Next/i })).toBeDisabled();
  });

  it('disables Next button on step 2 when slugAvailable is false', () => {
    render(<NavigationButtons {...defaultProps} currentStep={2} slugAvailable={false} />);
    expect(screen.getByRole('button', { name: /Next/i })).toBeDisabled();
  });

  it('disables Next button on step 2 when slugAvailable is null', () => {
    render(<NavigationButtons {...defaultProps} currentStep={2} slugAvailable={null} />);
    expect(screen.getByRole('button', { name: /Next/i })).toBeDisabled();
  });

  // Test for currentStep === 4 (submit step)
  it('renders Back and submit button for step 4', () => {
    render(<NavigationButtons {...defaultProps} currentStep={4} />);
    expect(screen.getByRole('button', { name: /Back/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Get Started for FREE/i })).toBeInTheDocument();
  });

  it('calls onPreviousStep when Back button is clicked on step 4', () => {
    render(<NavigationButtons {...defaultProps} currentStep={4} />);
    fireEvent.click(screen.getByRole('button', { name: /Back/i }));
    expect(mockOnPreviousStep).toHaveBeenCalledTimes(1);
  });

  it('calls onSubmitIntended when submit button is clicked on step 4', () => {
    render(<NavigationButtons {...defaultProps} currentStep={4} />);
    fireEvent.click(screen.getByRole('button', { name: /Get Started for FREE/i }));
    expect(mockOnSubmitIntended).toHaveBeenCalledTimes(1);
  });

  it('disables submit button on step 4 when isSubmitting is true', () => {
    render(<NavigationButtons {...defaultProps} currentStep={4} isSubmitting={true} />);
    expect(screen.getByRole('button', { name: /Creating Account.../i })).toBeDisabled();
  });

  it('disables submit button on step 4 when selectedPlan is null', () => {
    // Component no longer requires selectedPlan; button text is 'Get Started for FREE'
    render(<NavigationButtons {...defaultProps} currentStep={4} />);
    expect(screen.getByRole('button', { name: /Get Started for FREE/i })).toBeInTheDocument();
  });

  it('shows "Updating Profile..." when existingData has existing subscription and isSubmitting', () => {
    render(
      <NavigationButtons
        {...defaultProps}
        currentStep={4}
        isSubmitting={true}
        existingData={{}} // Removed hasExistingSubscription as we're now free for all users
      />
    );
    expect(screen.getByText(/Updating Profile.../i)).toBeInTheDocument();
  });

  it('shows "Creating Account..." when isSubmitting', () => {
    render(
      <NavigationButtons
        {...defaultProps}
        currentStep={4}
        isSubmitting={true}
      />
    );
    expect(screen.getByText(/Creating Account.../i)).toBeInTheDocument();
  });

  // Removed trial and subscription tests as we're now free for all users

  it('shows "Complete Profile Setup" when existingData exists and not submitting', () => {
    render(
      <NavigationButtons
        {...defaultProps}
        currentStep={4}
        isSubmitting={false}
        existingData={{}} // Removed hasExistingSubscription as we're now free for all users
      />
    );
    expect(screen.getByText(/Complete Profile Setup/i)).toBeInTheDocument();
  });

  it('shows "Get Started for FREE" when not submitting', () => {
    render(
      <NavigationButtons
        {...defaultProps}
        currentStep={4}
        isSubmitting={false}
      />
    );
    expect(screen.getByText(/Get Started for FREE/i)).toBeInTheDocument();
  });

  // Removed trial test as we're now free for all users
});