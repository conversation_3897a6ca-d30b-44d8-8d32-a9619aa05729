"use server";

import { BusinessProfilePublicData, BusinessSortBy } from "./types";

/**
 * Securely fetch business profiles for discover page using the service role key
 */
export async function getSecureBusinessProfilesForDiscover(
  pincodes: string | string[],
  locality?: string | null,
  page: number = 1,
  limit: number = 10,
  sortBy: BusinessSortBy = "created_desc"
): Promise<{
  data?: BusinessProfilePublicData[];
  count?: number;
  error?: string;
}> {
  if (!pincodes || (Array.isArray(pincodes) && pincodes.length === 0)) {
    return { error: "At least one pincode is required." };
  }

  // Convert single pincode to array for consistent handling
  const pincodeArray = Array.isArray(pincodes) ? pincodes : [pincodes];

  try {
    // Build query parameters
    const queryParams = new URLSearchParams();
    queryParams.set('pincodes', pincodeArray.join(','));
    queryParams.set('page', page.toString());
    queryParams.set('limit', limit.toString());
    queryParams.set('status', 'online');

    // Convert sortBy to API format
    let apiSortBy = 'created_desc';
    switch (sortBy) {
      case 'name_asc':
        apiSortBy = 'name_asc';
        break;
      case 'name_desc':
        apiSortBy = 'name_desc';
        break;
      case 'created_asc':
        apiSortBy = 'created_asc';
        break;
      case 'created_desc':
        apiSortBy = 'created_desc';
        break;
      case 'likes_asc':
        apiSortBy = 'likes_asc';
        break;
      case 'likes_desc':
        apiSortBy = 'likes_desc';
        break;
      case 'subscriptions_asc':
        apiSortBy = 'subscriptions_asc';
        break;
      case 'subscriptions_desc':
        apiSortBy = 'subscriptions_desc';
        break;
      case 'rating_asc':
        apiSortBy = 'rating_asc';
        break;
      case 'rating_desc':
        apiSortBy = 'rating_desc';
        break;
    }
    queryParams.set('sort_by', apiSortBy);

    if (locality) {
      queryParams.set('locality', locality);
    }

    // Use the business profile API endpoint
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business?${queryParams.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result = await response.json();

    if (!response.ok) {
      console.error("API Fetch Error:", result);
      return {
        error: result.error || "Failed to fetch business profiles",
      };
    }

    const businesses = result.businesses || [];
    const count = result.pagination?.total || 0;

    // Transform data to match expected format
    const safeData: BusinessProfilePublicData[] = businesses.map((profile: any) => {
      return {
        ...profile,
        // Add missing fields with default values if not present
        total_visits: profile.total_visits || 0,
        today_visits: profile.today_visits || 0,
        yesterday_visits: profile.yesterday_visits || 0,
        visits_7_days: profile.visits_7_days || 0,
        visits_30_days: profile.visits_30_days || 0,
        city_slug: profile.city_slug || null,
        state_slug: profile.state_slug || null,
        locality_slug: profile.locality_slug || null,
        gallery: profile.gallery || null,
        latitude: profile.latitude || null,
        longitude: profile.longitude || null,
      } as BusinessProfilePublicData;
    });

    return { data: safeData, count };
  } catch (e) {
    console.error("Exception in getSecureBusinessProfilesForDiscover:", e);
    return { error: "An unexpected error occurred." };
  }
}

/**
 * Securely fetch business profile IDs for discover page products using the service role key
 */
export async function getSecureBusinessProfileIdsForDiscover(
  pincodes: string | string[],
  locality?: string | null,
  sortBy: BusinessSortBy = "created_desc"
): Promise<{
  data?: string[];
  error?: string;
}> {
  if (!pincodes || (Array.isArray(pincodes) && pincodes.length === 0)) {
    return { error: "At least one pincode is required." };
  }

  // Convert single pincode to array for consistent handling
  const pincodeArray = Array.isArray(pincodes) ? pincodes : [pincodes];

  try {
    // Build query parameters
    const queryParams = new URLSearchParams();
    queryParams.set('pincodes', pincodeArray.join(','));
    queryParams.set('status', 'online');
    queryParams.set('ids_only', 'true');

    // Convert sortBy to API format
    let apiSortBy = 'created_desc';
    switch (sortBy) {
      case 'name_asc':
        apiSortBy = 'name_asc';
        break;
      case 'name_desc':
        apiSortBy = 'name_desc';
        break;
      case 'created_asc':
        apiSortBy = 'created_asc';
        break;
      case 'created_desc':
        apiSortBy = 'created_desc';
        break;
      case 'likes_asc':
        apiSortBy = 'likes_asc';
        break;
      case 'likes_desc':
        apiSortBy = 'likes_desc';
        break;
      case 'subscriptions_asc':
        apiSortBy = 'subscriptions_asc';
        break;
      case 'subscriptions_desc':
        apiSortBy = 'subscriptions_desc';
        break;
      case 'rating_asc':
        apiSortBy = 'rating_asc';
        break;
      case 'rating_desc':
        apiSortBy = 'rating_desc';
        break;
    }
    queryParams.set('sort_by', apiSortBy);

    if (locality) {
      queryParams.set('locality', locality);
    }

    // Use the business profile API endpoint
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business?${queryParams.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result = await response.json();

    if (!response.ok) {
      console.error("API Fetch Error:", result);
      return {
        error: result.error || "Failed to fetch business profile IDs",
      };
    }

    const businessIds = result.business_ids || [];

    return { data: businessIds };
  } catch (e) {
    console.error("Exception in getSecureBusinessProfileIdsForDiscover:", e);
    return { error: "An unexpected error occurred." };
  }
}
