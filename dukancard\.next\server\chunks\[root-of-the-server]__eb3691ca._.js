module.exports = {

"[project]/.next-internal/server/app/api/business/me/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[project]/lib/auth/jwt.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ACCESS_TOKEN_DURATION": (()=>ACCESS_TOKEN_DURATION),
    "REFRESH_TOKEN_DURATION": (()=>REFRESH_TOKEN_DURATION),
    "extractBearerToken": (()=>extractBearerToken),
    "generateAccessToken": (()=>generateAccessToken),
    "generateRefreshToken": (()=>generateRefreshToken),
    "verifyAccessToken": (()=>verifyAccessToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jsonwebtoken/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/crypto [external] (crypto, cjs)");
;
;
// JWT Secret - should be loaded from environment variable
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';
const ACCESS_TOKEN_DURATION = '15m'; // 15 minutes
const REFRESH_TOKEN_DURATION = 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds
function generateAccessToken(userId, roles = []) {
    const payload = {
        user_id: userId,
        roles: roles
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].sign(payload, JWT_SECRET, {
        expiresIn: ACCESS_TOKEN_DURATION,
        issuer: 'dukancard-api',
        subject: userId
    });
}
function generateRefreshToken() {
    return __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].randomBytes(32).toString('hex');
}
function verifyAccessToken(token) {
    try {
        const decoded = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].verify(token, JWT_SECRET);
        return decoded;
    } catch (error) {
        console.error('JWT verification failed:', error);
        return null;
    }
}
function extractBearerToken(authHeader) {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return null;
    }
    return authHeader.substring(7); // Remove 'Bearer ' prefix
}
}}),
"[project]/lib/security/hmac.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "extractHMACHeaders": (()=>extractHMACHeaders),
    "generateHMACSignature": (()=>generateHMACSignature),
    "validateTimestamp": (()=>validateTimestamp),
    "verifyHMACSignature": (()=>verifyHMACSignature)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/crypto [external] (crypto, cjs)");
;
function generateHMACSignature(method, path, timestamp, body, deviceSecret) {
    // 1. Create SHA256 hash of the request body
    const bodyHash = __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].createHash('sha256').update(body || '').digest('hex');
    // 2. Create the string to be signed: method + path + timestamp + bodyHash
    const stringToSign = `${method.toUpperCase()}${path}${timestamp}${bodyHash}`;
    // 3. Generate HMAC-SHA256 signature using device secret
    const signature = __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].createHmac('sha256', deviceSecret).update(stringToSign).digest('base64');
    return signature;
}
function verifyHMACSignature(receivedSignature, expectedSignature) {
    // Convert to buffers for constant-time comparison
    const receivedBuffer = Buffer.from(receivedSignature, 'base64');
    const expectedBuffer = Buffer.from(expectedSignature, 'base64');
    // Ensure both signatures are the same length to prevent timing attacks
    if (receivedBuffer.length !== expectedBuffer.length) {
        return false;
    }
    // Use crypto.timingSafeEqual for constant-time comparison
    return __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].timingSafeEqual(receivedBuffer, expectedBuffer);
}
function validateTimestamp(timestamp, windowSeconds = 120) {
    const requestTime = parseInt(timestamp, 10);
    const currentTime = Date.now();
    const windowMs = windowSeconds * 1000;
    // Check if timestamp is a valid number
    if (isNaN(requestTime)) {
        return false;
    }
    // Check if request is within the allowed time window
    const timeDiff = Math.abs(currentTime - requestTime);
    return timeDiff <= windowMs;
}
function extractHMACHeaders(headers) {
    const deviceId = typeof headers.get === 'function' ? headers.get('x-device-id') : headers['x-device-id'];
    const timestamp = typeof headers.get === 'function' ? headers.get('x-timestamp') : headers['x-timestamp'];
    const signature = typeof headers.get === 'function' ? headers.get('x-signature') : headers['x-signature'];
    if (!deviceId || !timestamp || !signature) {
        return null;
    }
    return {
        deviceId,
        timestamp,
        signature
    };
}
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[project]/utils/supabase/service-role.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createServiceRoleClient": (()=>createServiceRoleClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-route] (ecmascript) <locals>");
;
function createServiceRoleClient() {
    const supabaseUrl = ("TURBOPACK compile-time value", "https://rnjolcoecogzgglnblqn.supabase.co");
    const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    if (!supabaseUrl || !supabaseServiceRoleKey) {
        throw new Error('Missing required Supabase environment variables for service role');
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseServiceRoleKey, {
        auth: {
            autoRefreshToken: false,
            persistSession: false
        }
    });
}
}}),
"[project]/lib/middleware/hmac.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "hmacMiddleware": (()=>hmacMiddleware),
    "verifyHMACMiddleware": (()=>verifyHMACMiddleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$security$2f$hmac$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/security/hmac.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$service$2d$role$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/service-role.ts [app-route] (ecmascript)");
;
;
;
async function verifyHMACMiddleware(req, requireHMAC = true) {
    try {
        // Skip HMAC verification if not required (e.g., for login endpoint)
        if (!requireHMAC) {
            return {
                success: true
            };
        }
        // 1. Extract required headers
        const hmacHeaders = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$security$2f$hmac$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["extractHMACHeaders"])(req.headers);
        if (!hmacHeaders) {
            return {
                success: false,
                error: 'Missing required headers: X-Device-Id, X-Timestamp, or X-Signature',
                status: 400
            };
        }
        const { deviceId, timestamp, signature } = hmacHeaders;
        // 2. Validate timestamp to prevent replay attacks
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$security$2f$hmac$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateTimestamp"])(timestamp)) {
            return {
                success: false,
                error: 'Request has expired',
                status: 408
            };
        }
        // 3. Fetch device from database
        const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$service$2d$role$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createServiceRoleClient"])();
        const { data: device, error: deviceError } = await supabase.from('devices').select('device_id, device_secret_hash, hmac_key_hash, revoked').eq('device_id', deviceId).single();
        if (deviceError || !device) {
            return {
                success: false,
                error: 'Invalid device ID',
                status: 403
            };
        }
        // 4. Check if device is revoked
        if (device.revoked) {
            return {
                success: false,
                error: 'Device has been revoked',
                status: 403
            };
        }
        // 5. Get request body for signature verification
        let requestBody = '';
        try {
            // Clone the request to read the body without consuming it
            const clonedReq = req.clone();
            requestBody = await clonedReq.text();
        } catch (error) {
            requestBody = '';
        }
        // 6. Generate expected signature using stored HMAC key
        const method = req.method;
        const path = new URL(req.url).pathname;
        const expectedSignature = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$security$2f$hmac$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateHMACSignature"])(method, path, timestamp, requestBody, device.hmac_key_hash // Use the stored HMAC key
        );
        // 7. Verify signature using constant-time comparison
        const isValidSignature = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$security$2f$hmac$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["verifyHMACSignature"])(signature, expectedSignature);
        if (!isValidSignature) {
            return {
                success: false,
                error: 'Invalid signature',
                status: 403
            };
        }
        // 8. HMAC verification successful
        return {
            success: true,
            deviceId: deviceId
        };
    } catch (error) {
        console.error('Unexpected error in HMAC verification:', error);
        return {
            success: false,
            error: 'Internal Server Error',
            status: 500
        };
    }
}
async function hmacMiddleware(req, requireHMAC = true) {
    const result = await verifyHMACMiddleware(req, requireHMAC);
    if (!result.success) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](JSON.stringify({
            error: result.error
        }), {
            status: result.status || 500,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
    return null; // Continue to next middleware/handler
}
}}),
"[externals]/node:crypto [external] (node:crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:crypto", () => require("node:crypto"));

module.exports = mod;
}}),
"[project]/lib/middleware/rateLimiter.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "addRateLimitHeaders": (()=>addRateLimitHeaders),
    "applyRateLimiting": (()=>applyRateLimiting),
    "rateLimitMiddleware": (()=>rateLimitMiddleware),
    "rateLimitingMiddleware": (()=>rateLimitingMiddleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$upstash$2f$redis$2f$nodejs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@upstash/redis/nodejs.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$upstash$2f$redis$2f$nodejs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@upstash/redis/nodejs.mjs [app-route] (ecmascript) <locals>");
;
;
// Initialize Redis client
const redis = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$upstash$2f$redis$2f$nodejs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Redis"]({
    url: process.env.UPSTASH_REDIS_REST_URL,
    token: process.env.UPSTASH_REDIS_REST_TOKEN
});
/**
 * Get client IP address from request
 */ function getClientIP(req) {
    const forwarded = req.headers.get('x-forwarded-for');
    const realIP = req.headers.get('x-real-ip');
    const remoteAddress = req.headers.get('x-remote-address');
    if (forwarded) {
        return forwarded.split(',')[0].trim();
    }
    return realIP || remoteAddress || 'unknown';
}
/**
 * Generate Redis key for rate limiting
 */ function generateRateLimitKey(identifier, value, windowStart) {
    return `ratelimit:${identifier}:${value}:${windowStart}`;
}
/**
 * Get the start of the current time window
 */ function getWindowStart(windowSeconds) {
    const now = Math.floor(Date.now() / 1000);
    return Math.floor(now / windowSeconds) * windowSeconds;
}
/**
 * Check rate limit for a specific identifier and value
 */ async function checkRateLimit(identifier, value, config) {
    try {
        const windowStart = getWindowStart(config.windowSeconds);
        const key = generateRateLimitKey(identifier, value, windowStart);
        const windowEnd = windowStart + config.windowSeconds;
        // Get current count
        const currentCount = await redis.get(key) || 0;
        if (currentCount >= config.maxRequests) {
            // Rate limit exceeded
            const retryAfter = windowEnd - Math.floor(Date.now() / 1000);
            return {
                success: false,
                error: 'Rate limit exceeded',
                status: 429,
                limit: config.maxRequests,
                remaining: 0,
                reset: windowEnd,
                retryAfter: Math.max(retryAfter, 0)
            };
        }
        // Increment counter
        const newCount = await redis.incr(key);
        // Set expiration if this is the first request in the window
        if (newCount === 1) {
            await redis.expire(key, config.windowSeconds);
        }
        // Calculate remaining requests
        const remaining = Math.max(config.maxRequests - newCount, 0);
        return {
            success: true,
            limit: config.maxRequests,
            remaining,
            reset: windowEnd
        };
    } catch (error) {
        console.error(`Rate limit check failed for ${identifier}:${value}:`, error);
        // In case of Redis error, allow the request but log the error
        return {
            success: true,
            limit: config.maxRequests,
            remaining: config.maxRequests,
            reset: Math.floor(Date.now() / 1000) + config.windowSeconds
        };
    }
}
async function rateLimitMiddleware(req, strategies = []) {
    // Default strategies if none provided
    if (strategies.length === 0) {
        const defaultMaxRequests = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10);
        const defaultWindowSeconds = parseInt(process.env.RATE_LIMIT_WINDOW_SECONDS || '60', 10);
        strategies = [
            {
                identifier: 'ip',
                getValue: (req)=>getClientIP(req),
                config: {
                    maxRequests: defaultMaxRequests,
                    windowSeconds: defaultWindowSeconds,
                    identifier: 'ip'
                }
            }
        ];
    }
    let finalResult = {
        success: true,
        limit: strategies[0]?.config.maxRequests || 100,
        remaining: strategies[0]?.config.maxRequests || 100,
        reset: Math.floor(Date.now() / 1000) + (strategies[0]?.config.windowSeconds || 60)
    };
    // Apply strategies in order
    for (const strategy of strategies){
        const value = strategy.getValue(req);
        // Skip strategy if value cannot be determined
        if (!value || value === 'unknown') {
            continue;
        }
        const result = await checkRateLimit(strategy.identifier, value, strategy.config);
        if (!result.success) {
            return result;
        }
        // Update final result with the most restrictive limits
        if (result.remaining !== undefined && result.remaining < (finalResult.remaining || Infinity)) {
            finalResult = {
                ...finalResult,
                limit: result.limit,
                remaining: result.remaining,
                reset: result.reset
            };
        }
    }
    return finalResult;
}
async function applyRateLimiting(req, options = {}) {
    const strategies = [];
    // Default configurations
    const defaultIPLimit = {
        maxRequests: parseInt(process.env.RATE_LIMIT_IP_MAX_REQUESTS || '100', 10),
        windowSeconds: parseInt(process.env.RATE_LIMIT_IP_WINDOW_SECONDS || '60', 10),
        identifier: 'ip'
    };
    const defaultDeviceLimit = {
        maxRequests: parseInt(process.env.RATE_LIMIT_DEVICE_MAX_REQUESTS || '200', 10),
        windowSeconds: parseInt(process.env.RATE_LIMIT_DEVICE_WINDOW_SECONDS || '60', 10),
        identifier: 'device'
    };
    const defaultUserLimit = {
        maxRequests: parseInt(process.env.RATE_LIMIT_USER_MAX_REQUESTS || '500', 10),
        windowSeconds: parseInt(process.env.RATE_LIMIT_USER_WINDOW_SECONDS || '60', 10),
        identifier: 'user'
    };
    // Add IP rate limiting (enabled by default)
    if (options.byIP !== false) {
        strategies.push({
            identifier: 'ip',
            getValue: (req)=>getClientIP(req),
            config: options.customLimits?.ip || defaultIPLimit
        });
    }
    // Add device rate limiting
    if (options.byDevice && options.deviceId) {
        strategies.push({
            identifier: 'device',
            getValue: ()=>options.deviceId,
            config: options.customLimits?.device || defaultDeviceLimit
        });
    }
    // Add user rate limiting
    if (options.byUser && options.userId) {
        strategies.push({
            identifier: 'user',
            getValue: ()=>options.userId,
            config: options.customLimits?.user || defaultUserLimit
        });
    }
    return rateLimitMiddleware(req, strategies);
}
async function rateLimitingMiddleware(req, options = {}) {
    const result = await applyRateLimiting(req, options);
    if (!result.success) {
        const response = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](JSON.stringify({
            error: result.error
        }), {
            status: result.status || 429,
            headers: {
                'Content-Type': 'application/json',
                'X-RateLimit-Limit': result.limit?.toString() || '',
                'X-RateLimit-Remaining': result.remaining?.toString() || '0',
                'X-RateLimit-Reset': result.reset?.toString() || '',
                'Retry-After': result.retryAfter?.toString() || ''
            }
        });
        return response;
    }
    // Add rate limit headers to successful responses
    // Note: This will be handled by the calling code since we're returning null to continue
    return null;
}
function addRateLimitHeaders(response, result) {
    if (result.limit !== undefined) {
        response.headers.set('X-RateLimit-Limit', result.limit.toString());
    }
    if (result.remaining !== undefined) {
        response.headers.set('X-RateLimit-Remaining', result.remaining.toString());
    }
    if (result.reset !== undefined) {
        response.headers.set('X-RateLimit-Reset', result.reset.toString());
    }
    return response;
}
}}),
"[project]/lib/middleware/bruteForceProtection.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "applyBruteForceProtection": (()=>applyBruteForceProtection),
    "bruteForceProtectionMiddleware": (()=>bruteForceProtectionMiddleware),
    "calculateProgressiveDelay": (()=>calculateProgressiveDelay),
    "extractDeviceIdFromRequest": (()=>extractDeviceIdFromRequest),
    "extractEmailFromLoginRequest": (()=>extractEmailFromLoginRequest),
    "getClientIP": (()=>getClientIP)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$middleware$2f$rateLimiter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/middleware/rateLimiter.ts [app-route] (ecmascript)");
;
;
function calculateProgressiveDelay(attemptCount) {
    if (attemptCount <= 3) return 0;
    // Base delay starts at 1 second for 4th attempt
    const baseDelay = Math.pow(2, attemptCount - 4) * 1000;
    // Cap at 30 seconds
    const cappedDelay = Math.min(baseDelay, 30000);
    // Add jitter (±20%) but ensure result doesn't exceed cap
    const jitter = cappedDelay * 0.2 * (Math.random() - 0.5);
    const finalDelay = cappedDelay + jitter;
    // Ensure final delay doesn't exceed the cap and is not negative
    return Math.floor(Math.max(0, Math.min(finalDelay, 30000)));
}
async function applyBruteForceProtection(req, context, config = {}) {
    const { maxLoginAttemptsPerIP = parseInt(process.env.BRUTE_FORCE_LOGIN_IP_LIMIT || '10', 10), loginWindowSeconds = parseInt(process.env.BRUTE_FORCE_LOGIN_WINDOW || '3600', 10), maxLoginAttemptsPerEmail = parseInt(process.env.BRUTE_FORCE_EMAIL_LIMIT || '5', 10), maxRefreshAttemptsPerDevice = parseInt(process.env.BRUTE_FORCE_REFRESH_LIMIT || '20', 10), refreshWindowSeconds = parseInt(process.env.BRUTE_FORCE_REFRESH_WINDOW || '3600', 10), enableProgressiveDelays = true } = config;
    const strategies = {};
    switch(context.operation){
        case 'login':
            // For login, we apply both IP-based and email-based rate limiting
            strategies.byIP = true;
            strategies.customLimits = {
                ip: {
                    maxRequests: maxLoginAttemptsPerIP,
                    windowSeconds: loginWindowSeconds,
                    identifier: 'login_ip'
                }
            };
            // If we have an email, add email-based rate limiting
            if (context.email) {
                strategies.byUser = true;
                strategies.userId = `email:${context.email}`;
                strategies.customLimits.user = {
                    maxRequests: maxLoginAttemptsPerEmail,
                    windowSeconds: loginWindowSeconds,
                    identifier: 'login_email'
                };
            }
            break;
        case 'refresh':
            // For token refresh, we apply device-based and IP-based rate limiting
            strategies.byIP = true;
            strategies.customLimits = {
                ip: {
                    maxRequests: maxRefreshAttemptsPerDevice * 2,
                    windowSeconds: refreshWindowSeconds,
                    identifier: 'refresh_ip'
                }
            };
            if (context.deviceId) {
                strategies.byDevice = true;
                strategies.deviceId = context.deviceId;
                strategies.customLimits.device = {
                    maxRequests: maxRefreshAttemptsPerDevice,
                    windowSeconds: refreshWindowSeconds,
                    identifier: 'refresh_device'
                };
            }
            break;
        case 'device_register':
            // For device registration, apply IP-based rate limiting
            strategies.byIP = true;
            strategies.customLimits = {
                ip: {
                    maxRequests: 5,
                    windowSeconds: 3600,
                    identifier: 'device_register_ip'
                }
            };
            break;
        default:
            return {
                success: false,
                error: 'Invalid operation type',
                status: 400
            };
    }
    const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$middleware$2f$rateLimiter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["applyRateLimiting"])(req, strategies);
    // If rate limiting failed and progressive delays are enabled,
    // add delay information to the result
    if (!result.success && enableProgressiveDelays) {
        const attemptCount = (result.limit || 0) - (result.remaining || 0);
        const delay = calculateProgressiveDelay(attemptCount);
        if (delay > 0) {
            result.retryAfter = Math.max(result.retryAfter || 0, Math.ceil(delay / 1000));
        }
    }
    return result;
}
async function bruteForceProtectionMiddleware(req, context, config = {}) {
    const result = await applyBruteForceProtection(req, context, config);
    if (!result.success) {
        const response = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](JSON.stringify({
            error: result.error,
            retryAfter: result.retryAfter,
            message: 'Too many attempts. Please try again later.'
        }), {
            status: result.status || 429,
            headers: {
                'Content-Type': 'application/json',
                'X-RateLimit-Limit': result.limit?.toString() || '',
                'X-RateLimit-Remaining': result.remaining?.toString() || '0',
                'X-RateLimit-Reset': result.reset?.toString() || '',
                'Retry-After': result.retryAfter?.toString() || ''
            }
        });
        return response;
    }
    return null; // Continue to next middleware/handler
}
function extractEmailFromLoginRequest(body) {
    try {
        if (typeof body === 'object' && body !== null && typeof body.email === 'string') {
            return body.email.toLowerCase().trim();
        }
    } catch (error) {
    // Ignore parsing errors
    }
    return undefined;
}
function extractDeviceIdFromRequest(body) {
    try {
        if (typeof body === 'object' && body !== null && typeof body.deviceId === 'string') {
            return body.deviceId;
        }
    } catch (error) {
    // Ignore parsing errors
    }
    return undefined;
}
function getClientIP(req) {
    const forwarded = req.headers.get('x-forwarded-for');
    const realIP = req.headers.get('x-real-ip');
    const remoteAddress = req.headers.get('x-remote-address');
    if (forwarded) {
        return forwarded.split(',')[0].trim();
    }
    return realIP || remoteAddress || 'unknown';
}
}}),
"[project]/app/api/business/me/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2f$jwt$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/auth/jwt.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$middleware$2f$hmac$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/middleware/hmac.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$service$2d$role$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/service-role.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$middleware$2f$bruteForceProtection$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/middleware/bruteForceProtection.ts [app-route] (ecmascript)");
;
;
;
;
;
/**
 * Security middleware wrapper for business API routes
 */ async function applySecurityMiddleware(req, requireHMAC = true) {
    // 1. Apply rate limiting and brute force protection
    const ipAddress = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$middleware$2f$bruteForceProtection$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getClientIP"])(req);
    const bruteForceCheck = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$middleware$2f$bruteForceProtection$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["bruteForceProtectionMiddleware"])(req, {
        operation: 'business_api',
        ipAddress
    });
    if (bruteForceCheck) {
        return bruteForceCheck;
    }
    // 2. Verify JWT token
    const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2f$jwt$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["extractBearerToken"])(req);
    if (!token) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](JSON.stringify({
            error: 'Authorization token required'
        }), {
            status: 401,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
    const jwtPayload = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2f$jwt$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["verifyAccessToken"])(token);
    if (!jwtPayload) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](JSON.stringify({
            error: 'Invalid or expired token'
        }), {
            status: 401,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
    // 3. Verify HMAC signature (if required)
    if (requireHMAC) {
        const hmacResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$middleware$2f$hmac$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["verifyHMACMiddleware"])(req, true);
        if (!hmacResult.success) {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](JSON.stringify({
                error: hmacResult.error
            }), {
                status: hmacResult.status || 403,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }
    }
    return {
        jwtPayload
    };
}
async function GET(req) {
    try {
        // Apply security middleware
        const securityResult = await applySecurityMiddleware(req, true);
        if (securityResult instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"]) {
            return securityResult;
        }
        const { jwtPayload } = securityResult;
        const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$service$2d$role$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createServiceRoleClient"])();
        // Parse query parameters for additional data
        const url = new URL(req.url);
        const includeProducts = url.searchParams.get('include_products') === 'true';
        const includeGallery = url.searchParams.get('include_gallery') === 'true';
        const includeMetrics = url.searchParams.get('include_metrics') === 'true';
        // Build the select query based on what's requested
        let selectQuery = `
      id, business_name, business_slug, contact_email, member_name, title,
      business_category, phone, whatsapp_number, address_line, city, state,
      pincode, locality, about_bio, status, logo_url, instagram_url,
      facebook_url, established_year, delivery_info, business_hours,
      latitude, longitude, created_at, updated_at
    `;
        // Add metrics if requested
        if (includeMetrics) {
            selectQuery += `, total_likes, total_subscriptions, average_rating, total_visits, today_visits, yesterday_visits`;
        }
        // Add products if requested
        if (includeProducts) {
            selectQuery += `, products_services (
        id, name, description, base_price, discounted_price, is_available, 
        image_url, images, featured_image_index, product_type, slug, created_at, updated_at
      )`;
        }
        // Add gallery if requested
        if (includeGallery) {
            selectQuery += `, gallery`;
        }
        // Fetch the user's business profile
        const { data: business, error } = await supabase.from('business_profiles').select(selectQuery).eq('id', jwtPayload.user_id).maybeSingle();
        if (error) {
            console.error('Error fetching user business profile:', error);
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](JSON.stringify({
                error: 'Failed to fetch business profile'
            }), {
                status: 500,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }
        if (!business) {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](JSON.stringify({
                error: 'Business profile not found'
            }), {
                status: 404,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            business
        });
    } catch (error) {
        console.error('Unexpected error in GET /api/business/me:', error);
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](JSON.stringify({
            error: 'Internal server error'
        }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__eb3691ca._.js.map