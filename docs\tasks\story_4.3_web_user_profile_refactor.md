### User Story 4.3: Refactor User Profile & Settings Features to Use the API

**User Story:** As a logged-in web user, I want to view and manage my own user profile and settings, with all my data being handled by the secure API.

**Acceptance Criteria:**
- Users can view and update their profile information as before.
- All data for these features is now fetched and updated via the new secure API.
- Direct Supabase client calls related to the `customer_profiles` or `users` tables for profile management are eliminated from the codebase.

---

### Development Tasks

-   [ ] **1. Design and Create User Profile API Routes**
    *   **Developer's Task:** Design and implement the necessary API routes for managing a user's own profile. This will likely include a `GET /api/user/profile` endpoint to fetch the logged-in user's data and a `PATCH /api/user/profile` endpoint to update it. These routes must be protected by the security middleware.

-   [ ] **2. Implement Backend Logic for New Routes**
    *   **Developer's Task:** Implement the logic within the new API routes. The logic must ensure that a user can only ever access or modify their own data. The `userId` from the validated JWT token should be used as the key for all database operations. Use the service role Supabase client to interact with the database.

-   [ ] **3. Refactor Client-Side Profile Management**
    *   **Developer's Task:** Audit the frontend codebase for all pages and components related to viewing and editing user settings (e.g., `/profile`, `/settings/profile`). Refactor all data fetching logic to use the new `GET` endpoint and all forms to submit data to the new `PATCH` endpoint. Ensure UI feedback for success and error states is handled correctly.