### User Story 3.3: Implement General Rate Limiting Middleware

**Description:** This story involves implementing a flexible, multi-layered rate-limiting solution to protect the API from abuse, denial-of-service attacks, and runaway clients. It relies on Redis for fast, distributed state management.

**Acceptance Criteria:**
- API requests are rate-limited based on multiple strategies (IP, device, user).
- The limits and durations are configurable via environment variables.
- Blocked requests receive a `429` status code.
- Successful responses include `X-RateLimit-*` headers.

---

### Development Tasks

-   [ ] **1. Install Dependencies**
    *   **Task:** Install a robust library for rate limiting. `rate-limiter-flexible` is a good choice as it integrates well with Redis.
    *   **Command:** `npm install rate-limiter-flexible`
    *   **Prerequisite:** This story depends on **Story 1.5 (Redis Integration)**. The Redis client must be available.

-   [ ] **2. Create Rate Limiter Configuration Module**
    *   **Task:** Create a centralized module to configure and export all our rate-limiting instances.
    *   **File Path:** `dukancard/lib/security/rate-limiter.ts`
    *   **Implementation:**
        *   Import the Redis client.
        *   Create and export separate `RateLimiterRedis` instances for each strategy: `ipLimiter`, `deviceLimiter`, `userLimiter`.
        *   The configuration for each limiter (e.g., `points`, `duration`) must be read from environment variables (e.g., `RATE_LIMIT_IP_POINTS`, `RATE_LIMIT_IP_DURATION`) with sensible defaults.

-   [ ] **3. Integrate Rate Limiting into API Middleware**
    *   **Task:** In the main security middleware (`middleware.ts`), add the rate-limiting logic. This should run after successful authentication checks.
    *   **Logic Steps:**
        1.  Extract the necessary identifiers for each strategy: the request IP, the `deviceId` (from the `X-Device-Id` header), and the `userId` (from the JWT payload).
        2.  Wrap the rate-limiting checks in a `try...catch` block.
        3.  **Consume points in order:**
            *   `await ipLimiter.consume(ip)`
            *   `await deviceLimiter.consume(deviceId)`
            *   `await userLimiter.consume(userId)`
        4.  **Handle Rejection:** If any `.consume()` call throws an error, it means the limit has been exceeded. The `catch` block should immediately respond with a `429 Too Many Requests` status. The error object from the library contains information for the `Retry-After` header.
        5.  **Add Headers:** On both successful requests and `429` responses, add the appropriate `X-RateLimit-Limit`, `X-RateLimit-Remaining`, and `X-RateLimit-Reset` headers to the response.

-   [ ] **4. Write Integration Tests**
    *   **Task:** Test the rate-limiting logic. This will require mocking the `rate-limiter-flexible` library or the underlying Redis client.
    *   **File Path:** `dukancard/__tests__/middleware.test.ts`
    *   **Test Cases:**
        *   **Test 1: Single Request:** Mock a request from a new IP/device/user. Assert that the request is allowed and that the `X-RateLimit-Remaining` header shows `limit - 1`.
        *   **Test 2: Exceeding the Limit:** In a test loop, simulate making N+1 requests from the same IP address (where N is the limit). Assert that the first N requests pass and the N+1th request is rejected with a `429` status code.
        *   **Test 3: Independent Limits:** Verify that exceeding the limit for a `deviceId` does not affect the rate limit for a different `deviceId`, even if they share the same IP.