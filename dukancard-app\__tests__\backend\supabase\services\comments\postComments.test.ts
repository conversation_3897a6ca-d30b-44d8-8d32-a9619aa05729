import { getPostComments } from '@/backend/supabase/services/comments/postComments';
import { supabase } from '@/lib/supabase';
import type { CommentFilters } from '@/lib/types/like-comment';

// Use the globally mocked Supabase from jest.setup.js and stub per test
import type { SupabaseClient } from '@supabase/supabase-js';

type AnyObj = Record<string, any>;

const makeQueryBuilder = () => {
  const builder: AnyObj = {
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    is: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    range: jest.fn().mockResolvedValue({ data: [], error: null }),
    in: jest.fn().mockResolvedValue({ data: [], error: null }),
    maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
    single: jest.fn().mockResolvedValue({ data: null, error: null }),
  };
  return builder;
};

const mockQuery = makeQueryBuilder();

describe('getPostComments (Mobile)', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Supabase client from global mock
    const { supabase } = require('@/lib/supabase');
    supabase.auth.getUser.mockResolvedValue({ data: { user: { id: 'test-user-id' } }, error: null });

    // Reset mock query methods
    Object.keys(mockQuery).forEach((k) => {
      if (typeof (mockQuery as any)[k]?.mockClear === 'function') (mockQuery as any)[k].mockClear();
    });
    mockQuery.select.mockReturnThis();
    mockQuery.eq.mockReturnThis();
    mockQuery.is.mockReturnThis();
    mockQuery.order.mockReturnThis();
    mockQuery.range.mockResolvedValue({ data: [], error: null });
    mockQuery.in.mockResolvedValue({ data: [], error: null });

    supabase.from.mockReturnValue(mockQuery);
  });

  it('applies correct sorting for pinned_first order with newest first', async () => {
    const mockComments = [
      {
        id: '1',
        is_pinned: true,
        created_at: '2023-01-01',
        user_id: 'user1',
        post_id: 'post1',
        post_source: 'business',
        parent_comment_id: null,
        content: 'Pinned comment',
      },
      {
        id: '2',
        is_pinned: false,
        created_at: '2023-01-02',
        user_id: 'user2',
        post_id: 'post1',
        post_source: 'business',
        parent_comment_id: null,
        content: 'Recent comment',
      },
    ];

    mockQuery.range.mockResolvedValue({
      data: mockComments,
      error: null,
    });

    // Mock user profiles query
    const { supabase } = require('@/lib/supabase');
    supabase.from.mockImplementation((table: string) => {
      if (table === 'post_comments') {
        return mockQuery;
      }
      // Mock profiles queries
      return {
        select: jest.fn().mockReturnThis(),
        in: jest.fn().mockResolvedValue({
          data: [
            { id: 'user1', business_name: 'Business One', logo_url: null },
            { id: 'user2', name: 'User Two', avatar_url: null },
          ],
          error: null,
        }),
      } as any;
    });

    const filters: CommentFilters = {
      post_id: 'test-post-id',
      post_source: 'business',
      sort_order: 'pinned_first',
      limit: 20,
      offset: 0,
    };

    await getPostComments(filters);

    // Verify sorting: pinned first (descending), then created_at newest first (descending)
    expect(mockQuery.order).toHaveBeenCalledWith('is_pinned', { ascending: false });
    expect(mockQuery.order).toHaveBeenCalledWith('created_at', { ascending: false });
  });

  it('applies correct sorting for newest order', async () => {
    const mockOrderFn = jest.fn().mockReturnThis();
    const mockRangeFn = jest.fn().mockResolvedValue({
      data: [],
      error: null,
    });

    // Reset the mock to have proper from method
    const { supabase } = require('@/lib/supabase');
    supabase.from.mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      is: jest.fn().mockReturnThis(),
      order: mockOrderFn,
      range: mockRangeFn,
    } as any);

    const filters: CommentFilters = {
      post_id: 'test-post-id',
      post_source: 'business',
      sort_order: 'newest',
      limit: 20,
      offset: 0,
    };

    await getPostComments(filters);

    // Verify sorting: created_at newest first (descending)
    expect(mockOrderFn).toHaveBeenCalledWith('created_at', { ascending: false });
  });

  it('uses database-level pagination with range', async () => {
    const mockRangeFn = jest.fn().mockResolvedValue({
      data: [],
      error: null,
    });

    // Reset the mock to have proper from method
    const { supabase } = require('@/lib/supabase');
    supabase.from.mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      is: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      range: mockRangeFn,
    } as any);

    const filters: CommentFilters = {
      post_id: 'test-post-id',
      post_source: 'business',
      sort_order: 'pinned_first',
      limit: 50,
      offset: 20,
    };

    await getPostComments(filters);

    // Verify range is used for pagination: offset to offset + limit - 1
    expect(mockRangeFn).toHaveBeenCalledWith(20, 69); // 20 + 50 - 1
  });

  it('filters for top-level comments only', async () => {
    const mockIsFn = jest.fn().mockReturnThis();
    const mockEqFn = jest.fn().mockReturnThis();
    const mockRangeFn = jest.fn().mockResolvedValue({
      data: [],
      error: null,
    });

    // Reset the mock to have proper from method
    const { supabase } = require('@/lib/supabase');
    supabase.from.mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: mockEqFn,
      is: mockIsFn,
      order: jest.fn().mockReturnThis(),
      range: mockRangeFn,
    } as any);

    const filters: CommentFilters = {
      post_id: 'test-post-id',
      post_source: 'customer',
      sort_order: 'pinned_first',
    };

    await getPostComments(filters);

    // Verify it filters for null parent_comment_id (top-level only)
    expect(mockIsFn).toHaveBeenCalledWith('parent_comment_id', null);
    expect(mockEqFn).toHaveBeenCalledWith('post_id', 'test-post-id');
    expect(mockEqFn).toHaveBeenCalledWith('post_source', 'customer');
  });

  it('handles errors gracefully', async () => {
    // Reset the mock to have proper from method
    const { supabase } = require('@/lib/supabase');
    supabase.from.mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      is: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      range: jest.fn().mockResolvedValue({
        data: null,
        error: { message: 'Database connection failed' },
      }),
    } as any);

    const filters: CommentFilters = {
      post_id: 'test-post-id',
      post_source: 'business',
      sort_order: 'pinned_first',
    };

    const result = await getPostComments(filters);

    expect(result.success).toBe(false);
    expect(result.error).toBe('Database connection failed');
    expect(result.data).toEqual([]);
  });

  it('handles empty results correctly', async () => {
    // Reset the mock to have proper from method
    const { supabase } = require('@/lib/supabase');
    supabase.from.mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      is: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      range: jest.fn().mockResolvedValue({
        data: [],
        error: null,
      }),
    } as any);

    const filters: CommentFilters = {
      post_id: 'test-post-id',
      post_source: 'business',
      sort_order: 'pinned_first',
    };

    const result = await getPostComments(filters);

    expect(result.success).toBe(true);
    expect(result.data).toEqual([]);
  });

  it('uses default values for limit and offset when not provided', async () => {
    const mockRangeFn = jest.fn().mockResolvedValue({
      data: [],
      error: null,
    });

    // Reset the mock to have proper from method
    const { supabase } = require('@/lib/supabase');
    supabase.from.mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      is: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      range: mockRangeFn,
    } as any);

    const filters: CommentFilters = {
      post_id: 'test-post-id',
      post_source: 'business',
      sort_order: 'pinned_first',
    };

    await getPostComments(filters);

    // Should use default limit (20) and offset (0)
    expect(mockRangeFn).toHaveBeenCalledWith(0, 19); // 0 + 20 - 1
  });
});