### User Story 2.2: Implement User Login Endpoint

**Description:** This story creates the primary entry point for users. It handles credential validation and, if successful, orchestrates the generation of all necessary tokens (access, refresh) and triggers the device registration flow.

**Acceptance Criteria:**
- A new API endpoint `POST /api/auth/login` is created.
- It validates user credentials against the Supabase `auth.users` table.
- On success, it generates and returns a full set of session and device tokens.
- It correctly creates `device` and `refresh_token` records in the database.

---

### Development Tasks

-   [ ] **1. Create API Route File Structure**
    *   In the `dukancard` project, create the new API route file:
    *   **File Path:** `dukancard/app/api/auth/login/route.ts`

-   [ ] **2. Create JWT Utility Module**
    *   Create a reusable module for handling JWT operations.
    *   **File Path:** `dukancard/lib/auth/jwt.ts`
    *   **Implementation:** Add functions to sign and verify tokens. The signing function should accept a user ID and roles and embed them in the token payload. Use a secret key from your environment variables.

    ```typescript
    // dukancard/lib/auth/jwt.ts
    import jwt from 'jsonwebtoken';

    const JWT_SECRET = process.env.JWT_SECRET || 'your-default-secret';

    export function generateAccessToken(userId: string, roles: string[]) {
        const payload = { userId, roles };
        return jwt.sign(payload, JWT_SECRET, { expiresIn: '15m' });
    }

    // Add verification function as needed
    ```

-   [ ] **3. Refactor Device Registration Logic (Optional but Recommended)**
    *   To avoid code duplication, consider refactoring the core logic from Story 2.1 into a reusable function `registerDevice(userId, deviceInfo)` that can be called from both its own route and this login route.

-   [ ] **4. Implement the Login Route Handler**
    *   In `dukancard/app/api/auth/login/route.ts`, implement the `POST` handler.
    *   **Logic Steps:**
        1.  **Validate Request Body:** Use Zod to validate the incoming `{ email, password, deviceInfo }` payload.
        2.  **Authenticate User:** Use a service role Supabase client to call `supabase.auth.signInWithPassword({ email, password })`.
        3.  **Handle Auth Failure:** If `signInWithPassword` returns an error, respond immediately with `401 Unauthorized`.
        4.  **Get User Data:** If successful, the user object will be available. Extract the `id` and `raw_user_meta_data.roles` from the user object.
        5.  **Register Device:** Call the device registration logic (from Step 3 or Story 2.1) with the user's ID and `deviceInfo` to get a `deviceId` and `deviceSecret`.
        6.  **Generate Tokens:**
            *   Generate a new JWT `accessToken` using the utility from Step 2.
            *   Generate a new, cryptographically secure `refreshToken` (e.g., using `crypto.randomBytes`).
        7.  **Store Refresh Token:** Hash the new `refreshToken` using the `hashSecret` utility and insert it into the `refresh_tokens` table, linking it to the `deviceId`.
        8.  **Return Payload:** Respond with `200 OK` and a body containing `{ accessToken, refreshToken, deviceId, deviceSecret }`.

-   [ ] **5. Write Integration Tests**
    *   Create a new test file.
    *   **File Path:** `dukancard/__tests__/app/api/auth/login/route.test.ts`
    *   **Test Cases:**
        *   **Test 1: Successful Login:** Mock the `signInWithPassword` call to return a valid user object. Mock the device registration and database insert calls. Call the endpoint and assert that the response is `200 OK` and contains all four expected token/ID strings.
        *   **Test 2: Invalid Credentials:** Mock the `signInWithPassword` call to return an error. Assert the response is `401 Unauthorized`.
        *   **Test 3: Invalid Request Body:** Call the endpoint with a missing `email` or `password`. Assert the response is `400 Bad Request`.