// dukancard/__tests__/lib/security/hashing.test.ts
import { hashSecret, compareSecret } from '../../../lib/security/hashing';

describe('Security - Hashing Utilities', () => {

    it('should correctly hash a secret and verify it', async () => {
        const secret = 'mySuperSecretPassword123';
        const hashedSecret = await hashSecret(secret);

        // Ensure the hash is not the same as the secret
        expect(hashedSecret).not.toBe(secret);

        // Compare the original secret with the hash
        const isMatch = await compareSecret(secret, hashedSecret);
        expect(isMatch).toBe(true);
    });

    it('should fail to verify an incorrect secret', async () => {
        const secret = 'mySuperSecretPassword123';
        const wrongSecret = 'wrongPassword';
        const hashedSecret = await hashSecret(secret);

        // Compare the wrong secret with the hash
        const isMatch = await compareSecret(wrongSecret, hashedSecret);
        expect(isMatch).toBe(false);
    });

    it('should generate different hashes for the same secret', async () => {
        const secret = 'testSecret123';
        const hash1 = await hashSecret(secret);
        const hash2 = await hashSecret(secret);

        // Different hashes due to salting
        expect(hash1).not.toBe(hash2);

        // But both should verify correctly
        expect(await compareSecret(secret, hash1)).toBe(true);
        expect(await compareSecret(secret, hash2)).toBe(true);
    });

    it('should handle empty strings appropriately', async () => {
        const emptySecret = '';
        const hashedSecret = await hashSecret(emptySecret);

        expect(hashedSecret).not.toBe(emptySecret);
        expect(await compareSecret(emptySecret, hashedSecret)).toBe(true);
        expect(await compareSecret('notEmpty', hashedSecret)).toBe(false);
    });

});