"use server";

import { createClient } from "@/utils/supabase/server";
import { BusinessCardData, requiredFieldsForOnline } from "../schema";
import { mapBusinessCardData } from "../data/businessCardMapper";

// Import the type that mapBusinessCardData expects
type RawBusinessCardData = Record<string, unknown> & {
  id?: string;
  business_name?: string | null;
  contact_email?: string | null;
  logo_url?: string | null;
  member_name?: string | null;
  title?: string | null;
  address_line?: string | null;
  city?: string | null;
  state?: string | null;
  pincode?: string | null;
  locality?: string | null;
  phone?: string | null;
  instagram_url?: string | null;
  facebook_url?: string | null;
  whatsapp_number?: string | null;
  about_bio?: string | null;
  status?: string | null;
  business_slug?: string | null;
  theme_color?: string | null;
  delivery_info?: string | null;
  business_category?: string | null;
  established_year?: number | null;
  total_likes?: number | null;
  total_subscriptions?: number | null;
  average_rating?: number | null;
  business_hours?: unknown;
  created_at?: string | null;
  updated_at?: string | null;
  products_services?: unknown[];
};

/**
 * Fetches business card data for the authenticated user
 * @returns Business card data or error
 */
export async function getBusinessCardData(): Promise<{
  data?: BusinessCardData;
  error?: string;
}> {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { error: "User not authenticated." };
  }

  // Get business profile data via API
  const { data: { session } } = await supabase.auth.getSession();
  if (!session?.access_token) {
    return { error: "Authentication session not found." };
  }

  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business/me?include_metrics=true`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
      },
    });

    const result = await response.json();

    if (!response.ok) {
      if (response.status === 404) {
        return { data: undefined };
      }
      console.error("API Fetch Error:", result);
      return { error: result.error || "Failed to fetch profile" };
    }

    const data = result.business;
    if (!data) {
      return { data: undefined };
    }

    // Server-side check: Force offline if missing required fields
    if (data.status === "online") {
      // Check if all required fields are present
      const missingRequiredFields = requiredFieldsForOnline.filter(
        (field) => {
          const value = (data as Record<string, unknown>)[field];
          return !value || String(value).trim() === "";
        }
      );

      if (missingRequiredFields.length > 0) {
        console.log(
          `User ${user.id} card forced offline due to missing required fields: ${missingRequiredFields.join(", ")}.`
        );

        // Update status via API
        try {
          const updateResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business/${user.id}`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${session.access_token}`,
            },
            body: JSON.stringify({ status: "offline" }),
          });

          if (updateResponse.ok) {
            data.status = "offline";
          } else {
            console.error("Error forcing card offline via API");
          }
        } catch (updateError) {
          console.error("Network error forcing card offline:", updateError);
        }
      }
    }

    // Map data using the shared mapper
    const mappedData = mapBusinessCardData(data as RawBusinessCardData);
    return { data: mappedData };
  } catch (fetchError) {
    console.error("Network error fetching business profile:", fetchError);
    return { error: "Network error. Please try again." };
  }
}
