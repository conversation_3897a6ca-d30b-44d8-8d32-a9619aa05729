// Mock Redis client with proper separation - MUST come before importing the rate limiter
jest.mock('@upstash/redis', () => ({
  Redis: jest.fn().mockImplementation(() => ({
    get: jest.fn(),
    incr: jest.fn(),
    expire: jest.fn(),
  })),
}));

// Get the mock instance for testing
import { Redis } from '@upstash/redis';
const mockRedisConstructor = Redis as jest.MockedClass<typeof Redis>;
let mockRedisInstance: jest.Mocked<Redis>;

// Import AFTER the mock is set up
import { applyRateLimiting, RateLimitConfig } from '@/lib/middleware/rateLimiter';

// Store the mock instance globally to avoid timing issues
let globalMockRedisInstance: jest.Mocked<Redis>;

describe('Rate Limiting Middleware', () => {
  beforeAll(() => {
    // Get the Redis instance that was created during module import
    globalMockRedisInstance = mockRedisConstructor.mock.results[0]?.value as jest.Mocked<Redis>;
  });

  beforeEach(() => {
    // Use the global instance
    mockRedisInstance = globalMockRedisInstance;
    
    if (!mockRedisInstance) {
      throw new Error('Mock Redis instance not found');
    }
    
    // Clear call history but keep mock functions intact
    mockRedisInstance.get.mockClear();
    mockRedisInstance.incr.mockClear();
    mockRedisInstance.expire.mockClear();
    
    // Mock current time for consistent testing
    jest.spyOn(Date, 'now').mockReturnValue(1640995200000); // Fixed timestamp
    
    // Mock environment variables
    process.env.RATE_LIMIT_IP_MAX_REQUESTS = '10';
    process.env.RATE_LIMIT_IP_WINDOW_SECONDS = '60';
    process.env.RATE_LIMIT_DEVICE_MAX_REQUESTS = '20';
    process.env.RATE_LIMIT_DEVICE_WINDOW_SECONDS = '60';
    process.env.RATE_LIMIT_USER_MAX_REQUESTS = '50';
    process.env.RATE_LIMIT_USER_WINDOW_SECONDS = '60';
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  const createMockRequest = (headers: Record<string, string> = {}) => {
    return {
      headers: {
        get: (name: string) => headers[name.toLowerCase()] || null,
      },
    } as any;
  };

  describe('IP-based rate limiting', () => {
    it('should allow requests under the limit', async () => {
      const request = createMockRequest({
        'x-forwarded-for': '***********',
      });

      // Mock Redis responses
      mockRedisInstance.get.mockResolvedValue(5); // Current count: 5
      mockRedisInstance.incr.mockResolvedValue(6); // New count: 6

      const result = await applyRateLimiting(request, { byIP: true });

      expect(result.success).toBe(true);
      expect(result.remaining).toBe(4); // 10 - 6 = 4
      expect(mockRedisInstance.incr).toHaveBeenCalled();
    });

    it('should reject requests over the limit', async () => {
      const request = createMockRequest({
        'x-forwarded-for': '***********',
      });

      // Mock Redis responses - at limit
      mockRedisInstance.get.mockResolvedValue(10); // Already at limit

      const result = await applyRateLimiting(request, { byIP: true });

      expect(result.success).toBe(false);
      expect(result.status).toBe(429);
      expect(result.error).toBe('Rate limit exceeded');
      expect(result.remaining).toBe(0);
      expect(result.retryAfter).toBeGreaterThan(0);
      expect(mockRedisInstance.incr).not.toHaveBeenCalled();
    });

    it('should set expiration for first request in window', async () => {
      const request = createMockRequest({
        'x-forwarded-for': '***********',
      });

      // Mock Redis responses - first request
      mockRedisInstance.get.mockResolvedValue(0);
      mockRedisInstance.incr.mockResolvedValue(1);

      const result = await applyRateLimiting(request, { byIP: true });

      expect(result.success).toBe(true);
      expect(result.remaining).toBe(9); // 10 - 1 = 9
      expect(mockRedisInstance.expire).toHaveBeenCalledWith(expect.any(String), 60);
    });
  });

  describe('Device-based rate limiting', () => {
    it('should allow requests under device limit', async () => {
      const request = createMockRequest({});
      const deviceId = 'device-123';

      mockRedisInstance.get.mockResolvedValue(15); // Current count: 15
      mockRedisInstance.incr.mockResolvedValue(16); // New count: 16

      const result = await applyRateLimiting(request, {
        byDevice: true,
        deviceId,
      });

      expect(result.success).toBe(true);
      expect(result.remaining).toBe(4); // 20 - 16 = 4
    });

    it('should reject requests over device limit', async () => {
      const request = createMockRequest({});
      const deviceId = 'device-456';

      mockRedisInstance.get.mockResolvedValue(20); // At limit

      const result = await applyRateLimiting(request, {
        byDevice: true,
        deviceId,
      });

      expect(result.success).toBe(false);
      expect(result.status).toBe(429);
    });
  });

  describe('User-based rate limiting', () => {
    it('should allow requests under user limit', async () => {
      const request = createMockRequest({});
      const userId = 'user-789';

      mockRedisInstance.get.mockResolvedValue(25); // Current count: 25
      mockRedisInstance.incr.mockResolvedValue(26); // New count: 26

      const result = await applyRateLimiting(request, {
        byUser: true,
        userId,
        byIP: false, // Disable IP limiting for this test
      });

      expect(result.success).toBe(true);
      expect(result.remaining).toBe(24); // 50 - 26 = 24
    });

    it('should reject requests over user limit', async () => {
      const request = createMockRequest({});
      const userId = 'user-999';

      mockRedisInstance.get.mockResolvedValue(50); // At limit

      const result = await applyRateLimiting(request, {
        byUser: true,
        userId,
        byIP: false, // Disable IP limiting for this test
      });

      expect(result.success).toBe(false);
      expect(result.status).toBe(429);
    });
  });

  describe('Multi-strategy rate limiting', () => {
    it('should apply IP and device limits in sequence', async () => {
      const request = createMockRequest({
        'x-forwarded-for': '***********00',
      });
      const deviceId = 'device-multi';

      // Mock both checks to pass
      mockRedisInstance.get
        .mockResolvedValueOnce(5) // IP count
        .mockResolvedValueOnce(10); // Device count
      mockRedisInstance.incr
        .mockResolvedValueOnce(6) // IP increment
        .mockResolvedValueOnce(11); // Device increment

      const result = await applyRateLimiting(request, {
        byIP: true,
        byDevice: true,
        deviceId,
      });

      expect(result.success).toBe(true);
      expect(mockRedisInstance.get).toHaveBeenCalledTimes(2);
      expect(mockRedisInstance.incr).toHaveBeenCalledTimes(2);
    });

    it('should fail if any strategy exceeds limit', async () => {
      const request = createMockRequest({
        'x-forwarded-for': '***********01',
      });
      const deviceId = 'device-fail';

      // IP passes, device fails
      mockRedisInstance.get
        .mockResolvedValueOnce(5) // IP count - under limit
        .mockResolvedValueOnce(20); // Device count - at limit

      mockRedisInstance.incr.mockResolvedValueOnce(6); // IP increment

      const result = await applyRateLimiting(request, {
        byIP: true,
        byDevice: true,
        deviceId,
      });

      expect(result.success).toBe(false);
      expect(result.status).toBe(429);
      expect(mockRedisInstance.incr).toHaveBeenCalledTimes(1); // Only IP got incremented
    });
  });

  describe('Custom limits', () => {
    it('should use custom rate limit configurations', async () => {
      const request = createMockRequest({
        'x-forwarded-for': '***********00',
      });

      const customIPLimit: RateLimitConfig = {
        maxRequests: 5,
        windowSeconds: 30,
        identifier: 'ip',
      };

      mockRedisInstance.get.mockResolvedValue(3);
      mockRedisInstance.incr.mockResolvedValue(4);

      const result = await applyRateLimiting(request, {
        byIP: true,
        customLimits: {
          ip: customIPLimit,
        },
      });

      expect(result.success).toBe(true);
      expect(result.remaining).toBe(1); // 5 - 4 = 1
      expect(result.limit).toBe(5);
    });
  });

  describe('IP address extraction', () => {
    it('should extract IP from x-forwarded-for header', async () => {
      const request = createMockRequest({
        'x-forwarded-for': '***********, ***********',
      });

      mockRedisInstance.get.mockResolvedValue(0);
      mockRedisInstance.incr.mockResolvedValue(1);

      await applyRateLimiting(request, { byIP: true });

      // Should use first IP from forwarded header
      expect(mockRedisInstance.get).toHaveBeenCalledWith(
        expect.stringContaining('***********')
      );
    });

    it('should extract IP from x-real-ip header when forwarded is not available', async () => {
      const request = createMockRequest({
        'x-real-ip': '************',
      });

      mockRedisInstance.get.mockResolvedValue(0);
      mockRedisInstance.incr.mockResolvedValue(1);

      await applyRateLimiting(request, { byIP: true });

      expect(mockRedisInstance.get).toHaveBeenCalledWith(
        expect.stringContaining('************')
      );
    });

    it('should handle unknown IP gracefully', async () => {
      const request = createMockRequest({}); // No IP headers

      // Should succeed without calling Redis since IP is unknown
      const result = await applyRateLimiting(request, { byIP: true });

      expect(result.success).toBe(true);
      expect(mockRedisInstance.get).not.toHaveBeenCalled();
      // Should return default values since no strategies were actually applied
      expect(result.remaining).toBe(10); // Default IP limit from env
    });
  });

  describe('Redis error handling', () => {
    it('should allow requests when Redis is unavailable', async () => {
      const request = createMockRequest({
        'x-forwarded-for': '***********50',
      });

      // Mock Redis error
      mockRedisInstance.get.mockRejectedValue(new Error('Redis connection failed'));

      const result = await applyRateLimiting(request, { byIP: true });

      // Should allow request despite Redis error
      expect(result.success).toBe(true);
      expect(result.remaining).toBe(10); // Default fallback
    });

    it('should log Redis errors', async () => {
      const request = createMockRequest({
        'x-forwarded-for': '*************',
      });

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      mockRedisInstance.get.mockRejectedValue(new Error('Redis timeout'));

      await applyRateLimiting(request, { byIP: true });

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Rate limit check failed'),
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('Time window calculations', () => {
    it('should calculate correct window start times', async () => {
      const request = createMockRequest({
        'x-forwarded-for': '***********00',
      });

      // Current time: 1640995200000 (milliseconds)
      // In seconds: 1640995200
      // For 60-second window: floor(1640995200 / 60) * 60 = 27349920 * 60 = 1640995200
      
      mockRedisInstance.get.mockResolvedValue(0);
      mockRedisInstance.incr.mockResolvedValue(1);

      await applyRateLimiting(request, { byIP: true });

      // Should create key with correct window start
      expect(mockRedisInstance.get).toHaveBeenCalledWith(
        expect.stringContaining('1640995200') // Window start timestamp
      );
    });
  });
});