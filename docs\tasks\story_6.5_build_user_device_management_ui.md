### User Story 6.5: Build User-Facing UI for Device Management

*   **User Story:** As a security-conscious user, I want to see a list of all devices currently logged into my account and be able to log out a specific device, so that I can manage my own account security.
*   **Acceptance Criteria:**
    *   Users can view a list of their active devices in their profile settings.
    *   Users can successfully log out a specific device from this UI.

---

### Development Tasks

-   [ ] **1. Create API Endpoint for Listing User Devices**
    *   **Task:** Implement a new API endpoint to fetch a list of devices associated with the currently authenticated user.
    *   **File Path:** `dukancard/app/api/security/devices/route.ts` (GET method)
    *   **Logic:** This endpoint must be protected by JWT authentication. It should query the `devices` table for records associated with the `userId` extracted from the JWT. It should return a sanitized list of devices (e.g., `deviceId`, `deviceName`, `platform`, `last_seen_at`), ensuring no sensitive information like `device_secret_hash` is exposed.

-   [ ] **2. Create API Endpoint for User Self-Revocation**
    *   **Task:** Implement a new API endpoint that allows a user to revoke one of their own devices.
    *   **File Path:** `dukancard/app/api/security/devices/[deviceId]/revoke/route.ts` (POST method)
    *   **Logic:** This endpoint must be protected by JWT authentication. It should take a `deviceId` as a path parameter. The logic must verify that the `deviceId` belongs to the authenticated user before marking the device and its associated refresh tokens as `revoked`.

-   [ ] **3. Implement Web UI for Device Management**
    *   **Task:** Create a new page or component in the `dukancard` web application for device management.
    *   **Suggested Path:** `/profile/settings/devices` (or similar, within the user's profile settings).
    *   **Functionality:**
        *   Fetch the list of devices using the `GET /api/security/devices` endpoint.
        *   Display each device with its name, platform, and last seen time.
        *   Provide a "Log out" button next to each device (except the current one).
        *   Implement the "Log out" button to call the `POST /api/security/devices/[deviceId]/revoke` endpoint.

-   [ ] **4. Implement Mobile UI for Device Management**
    *   **Task:** Create a new screen or component in the `dukancard-app` mobile application for device management.
    *   **Suggested Path:** `/app/(tabs)/profile/settings/devices.tsx` (or similar, within the user's profile settings).
    *   **Functionality:**
        *   Fetch the list of devices using the `GET /api/security/devices` endpoint via the mobile API client (from Story 5.2).
        *   Display each device with its name, platform, and last seen time.
        *   Provide a "Log out" button next to each device (except the current one).
        *   Implement the "Log out" button to call the `POST /api/security/devices/[deviceId]/revoke` endpoint via the mobile API client.

-   [ ] **5. Write Integration Tests for New API Endpoints**
    *   **Task:** Create test files for the new API endpoints.
    *   **File Paths:**
        *   `dukancard/__tests__/app/api/security/devices/route.test.ts`
        *   `dukancard/__tests__/app/api/security/devices/[deviceId]/revoke/route.test.ts`
    *   **Test Cases:** Cover successful listing, successful self-revocation, and unauthorized attempts (e.g., trying to revoke another user's device).