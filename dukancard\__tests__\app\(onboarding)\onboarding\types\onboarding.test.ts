import { formSchema, OnboardingFormData, User, OnboardingClientProps, OnboardingState, ExistingBusinessProfileData, StepComponentProps } from '@/app/(onboarding)/onboarding/types/onboarding';
import { z } from 'zod';

// Mock external dependencies for formSchema
jest.mock('@/lib/schemas/authSchemas', () => {
  const actualZod = jest.requireActual('zod');
  return {
    IndianMobileSchema: actualZod.z.string().regex(/^\d{10}$/, { message: "Invalid Indian mobile number." }),
  };
});

describe('Onboarding Types and Schema', () => {
  describe('formSchema validation', () => {
    const validFormData: OnboardingFormData = {
      businessName: 'Test Business',
      email: '<EMAIL>',
      memberName: '<PERSON>',
      title: 'CEO',
      phone: '9876543210',
      businessCategory: 'Retail',
      businessSlug: 'test-business-slug',
      addressLine: '123 Main St',
      pincode: '123456',
      city: 'Test City',
      state: 'Test State',
      locality: 'Test Locality',
      businessStatus: 'online',
      planId: 'free',
    };

    it('should validate a complete and valid form data object', () => {
      const result = formSchema.safeParse(validFormData);
      expect(result.success).toBe(true);
    });

    it('should fail validation if businessName is too short', () => {
      const invalidData = { ...validFormData, businessName: 'a' };
      const result = formSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      expect(result.error?.flatten().fieldErrors.businessName).toEqual(['Business name must be at least 2 characters.']);
    });

    it('should fail validation if email is invalid', () => {
      const invalidData = { ...validFormData, email: 'invalid-email' };
      const result = formSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      expect(result.error?.flatten().fieldErrors.email).toEqual(['Please enter a valid email.']);
    });

    it('should fail validation if memberName is too short', () => {
      const invalidData = { ...validFormData, memberName: 'a' };
      const result = formSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      expect(result.error?.flatten().fieldErrors.memberName).toEqual(['Your name is required.']);
    });

    it('should fail validation if title is too short', () => {
      const invalidData = { ...validFormData, title: 'a' };
      const result = formSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      expect(result.error?.flatten().fieldErrors.title).toEqual(['Your title/designation is required.']);
    });

    it('should fail validation if phone is invalid', () => {
      const invalidData = { ...validFormData, phone: '123' };
      const result = formSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      expect(result.error?.flatten().fieldErrors.phone).toEqual(['Invalid Indian mobile number.']);
    });

    it('should fail validation if businessCategory is missing', () => {
      const invalidData = { ...validFormData, businessCategory: '' };
      const result = formSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      expect(result.error?.flatten().fieldErrors.businessCategory).toEqual(['Business category is required.']);
    });

    it('should fail validation if businessSlug is too short', () => {
      const invalidData = { ...validFormData, businessSlug: 'ab' };
      const result = formSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      expect(result.error?.flatten().fieldErrors.businessSlug).toEqual(['URL slug must be at least 3 characters.']);
    });

    it('should fail validation if businessSlug has invalid characters', () => {
      const invalidData = { ...validFormData, businessSlug: 'test slug!' };
      const result = formSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      expect(result.error?.flatten().fieldErrors.businessSlug).toEqual(['URL slug can only contain lowercase letters, numbers, and hyphens.']);
    });

    it('should fail validation if addressLine is missing', () => {
      const invalidData = { ...validFormData, addressLine: '' };
      const result = formSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      expect(result.error?.flatten().fieldErrors.addressLine).toEqual(['Address line is required.']);
    });

    it('should fail validation if pincode is too short', () => {
      const invalidData = { ...validFormData, pincode: '123' };
      const result = formSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      expect(result.error?.flatten().fieldErrors.pincode).toEqual(['Pincode must be 6 digits.']);
    });

    it('should fail validation if pincode is too long', () => {
      const invalidData = { ...validFormData, pincode: '1234567' };
      const result = formSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      expect(result.error?.flatten().fieldErrors.pincode).toEqual(['Pincode must be 6 digits.']);
    });

    it('should fail validation if pincode contains non-digits', () => {
      const invalidData = { ...validFormData, pincode: '123abc' };
      const result = formSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      expect(result.error?.flatten().fieldErrors.pincode).toEqual(['Pincode must contain only digits.']);
    });

    it('should fail validation if city is missing', () => {
      const invalidData = { ...validFormData, city: '' };
      const result = formSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      expect(result.error?.flatten().fieldErrors.city).toEqual(['City is required.']);
    });

    it('should fail validation if state is missing', () => {
      const invalidData = { ...validFormData, state: '' };
      const result = formSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      expect(result.error?.flatten().fieldErrors.state).toEqual(['State is required.']);
    });

    it('should fail validation if locality is missing', () => {
      const invalidData = { ...validFormData, locality: '' };
      const result = formSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      expect(result.error?.flatten().fieldErrors.locality).toEqual(['Locality/area is required.']);
    });

    it('defaults planId to "free" when not provided', () => {
      const { planId, ...dataWithoutPlan } = validFormData as any;
      const result = formSchema.safeParse(dataWithoutPlan);
      expect(result.success).toBe(true);
      expect(result.data?.planId).toBe('free');
    });

    it('should default businessStatus to online if not provided', () => {
      const { businessStatus, ...dataWithoutStatus } = validFormData; // Remove businessStatus using destructuring
      const result = formSchema.safeParse(dataWithoutStatus);
      expect(result.success).toBe(true);
      expect(result.data?.businessStatus).toBe('online');
    });
  });

  describe('Type Definitions', () => {
    it('OnboardingFormData should match formSchema inferred type', () => {
      type InferredType = z.infer<typeof formSchema>;
      // This test conceptually verifies type compatibility at compile time.
      // If there's a mismatch, TypeScript will throw an error during compilation.
      const testData: OnboardingFormData = {} as InferredType; // Assigning inferred type to declared type
      expect(true).toBe(true); // Placeholder assertion
    });

    it('User type should be assignable', () => {
      const testUser: User = {
        id: '123',
        email: '<EMAIL>',
        phone: '**********',
        app_metadata: { provider: 'google', someOtherField: 'value' },
        user_metadata: { display_name: 'Test User', full_name: 'Test User Full', name: 'Test' },
      };
      expect(testUser.id).toBe('123'); // Placeholder assertion
    });

    it('OnboardingClientProps type should be assignable', () => {
      const testProps: OnboardingClientProps = {
        redirectSlug: 'test-slug',
        message: 'test-message',
      };
      expect(testProps.redirectSlug).toBe('test-slug'); // Placeholder assertion
    });

    it('OnboardingState type should be assignable', () => {
      const testState: OnboardingState = {
        currentStep: 1,
        // Removed showPlans as we're now free for all users
        // Removed selectedPlan as we're now free for all users
        user: { id: '123' } as User,
        slugAvailable: true,
        slugToCheck: 'test-slug',
        cardRedirect: 'test-card',
        messageParam: 'test-msg',
        isSubmitIntended: false,
        isLoadingExistingData: false,
        existingData: { businessName: 'Test' } as ExistingBusinessProfileData,
      };
      expect(testState.currentStep).toBe(1); // Placeholder assertion
    });

    it('ExistingBusinessProfileData type should be assignable', () => {
      const testData: ExistingBusinessProfileData = {
        businessName: 'Biz',
        email: '<EMAIL>',
        // Removed hasExistingSubscription as we're now free for all users
        // Removed planId as we're now free for all users
      };
      expect(testData.businessName).toBe('Biz'); // Placeholder assertion
    });

    it('StepComponentProps type should be assignable', () => {
      const mockForm = { control: {}, setValue: jest.fn() } as any; // Minimal mock for UseFormReturn
      const testProps: StepComponentProps = {
        form: mockForm,
        isSubmitting: false,
        user: null,
        existingData: null,
        slugAvailable: true,
        isCheckingSlug: false,
        slugToCheck: 'slug',
        setSlugToCheck: jest.fn(),
        setSlugAvailable: jest.fn(),
        // Removed selectedPlan as we're now free for all users
        // Removed setSelectedPlan as we're now free for all users
        // Removed showPlans as we're now free for all users
        // Removed setShowPlans as we're now free for all users
        availableLocalities: ['loc1'],
        isPincodeLoading: false,
        handlePincodeChange: jest.fn(),
      };
      expect(testProps.isSubmitting).toBe(false); // Placeholder assertion
    });
  });
});
