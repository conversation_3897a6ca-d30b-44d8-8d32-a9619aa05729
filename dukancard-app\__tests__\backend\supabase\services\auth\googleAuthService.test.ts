/**
 * @jest-environment node
 */
import {
  signInWithGoogle,
  signInWithGoogleDeepLink,
  GoogleAuthResponse,
} from '@/backend/supabase/services/auth/googleAuthService';
import { supabase } from '@/lib/supabase';
import * as Linking from 'expo-linking';
import * as WebBrowser from 'expo-web-browser';

// Mock dependencies
jest.mock('@/lib/supabase');
jest.mock('expo-linking');
jest.mock('expo-web-browser');

const mockSupabase = supabase as jest.Mocked<typeof supabase>;
const mockLinking = Linking as jest.Mocked<typeof Linking>;
const mockWebBrowser = WebBrowser as jest.Mocked<typeof WebBrowser>;

describe('GoogleAuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mocks
    mockLinking.createURL.mockReturnValue('dukancardapp://auth/callback');
    mockWebBrowser.maybeCompleteAuthSession.mockImplementation(() => {});
  });

  describe('signInWithGoogle', () => {
    it('should successfully sign in with Google using in-app browser', async () => {
      // Mock successful OAuth URL generation
      const mockOAuthUrl = 'https://accounts.google.com/oauth/authorize?...';
      mockSupabase.auth.signInWithOAuth.mockResolvedValue({
        data: { url: mockOAuthUrl },
        error: null,
      });

      // Mock successful web browser session
      const mockSuccessUrl = 'dukancardapp://auth/callback#access_token=mock_token&refresh_token=mock_refresh';
      mockWebBrowser.openAuthSessionAsync.mockResolvedValue({
        type: 'success',
        url: mockSuccessUrl,
      });

      // Mock successful session setting
      mockSupabase.auth.setSession.mockResolvedValue({
        data: { user: { id: 'user123' }, session: {} },
        error: null,
      });

      const result = await signInWithGoogle();

      expect(mockWebBrowser.maybeCompleteAuthSession).toHaveBeenCalled();
      expect(mockSupabase.auth.signInWithOAuth).toHaveBeenCalledWith({
        provider: 'google',
        options: {
          redirectTo: 'dukancardapp://auth/callback',
          queryParams: {
            access_type: 'offline',
            prompt: 'select_account',
            approval_prompt: 'force',
          },
        },
      });
      expect(mockWebBrowser.openAuthSessionAsync).toHaveBeenCalledWith(
        mockOAuthUrl,
        'dukancardapp://auth/callback',
        {
          showTitle: true,
          showInRecents: false,
          enableBarCollapsing: true,
          controlsColor: '#C29D5B',
          toolbarColor: '#ffffff',
          createTask: false,
        }
      );
      expect(mockSupabase.auth.setSession).toHaveBeenCalledWith({
        access_token: 'mock_token',
        refresh_token: 'mock_refresh',
      });
      expect(result).toEqual({
        success: true,
        message: 'Successfully signed in with Google',
      });
    });

    it('should handle OAuth initialization failure', async () => {
      const error = { message: 'Failed to initialize OAuth' };
      mockSupabase.auth.signInWithOAuth.mockResolvedValue({
        data: null,
        error,
      });

      const result = await signInWithGoogle();

      expect(result).toEqual({
        success: false,
        message: 'Failed to initialize Google sign-in',
        error,
      });
      expect(mockWebBrowser.openAuthSessionAsync).not.toHaveBeenCalled();
    });

    it('should handle missing OAuth URL', async () => {
      mockSupabase.auth.signInWithOAuth.mockResolvedValue({
        data: { url: null },
        error: null,
      });

      const result = await signInWithGoogle();

      expect(result).toEqual({
        success: false,
        message: 'Failed to get Google sign-in URL',
      });
      expect(mockWebBrowser.openAuthSessionAsync).not.toHaveBeenCalled();
    });

    it('should handle user cancellation', async () => {
      const mockOAuthUrl = 'https://accounts.google.com/oauth/authorize?...';
      mockSupabase.auth.signInWithOAuth.mockResolvedValue({
        data: { url: mockOAuthUrl },
        error: null,
      });

      mockWebBrowser.openAuthSessionAsync.mockResolvedValue({
        type: 'cancel',
      });

      const result = await signInWithGoogle();

      expect(result).toEqual({
        success: false,
        message: 'Google sign-in was cancelled',
      });
      expect(mockSupabase.auth.setSession).not.toHaveBeenCalled();
    });

    it('should handle missing access token in response', async () => {
      const mockOAuthUrl = 'https://accounts.google.com/oauth/authorize?...';
      mockSupabase.auth.signInWithOAuth.mockResolvedValue({
        data: { url: mockOAuthUrl },
        error: null,
      });

      const mockSuccessUrl = 'dukancardapp://auth/callback#some_other_param=value';
      mockWebBrowser.openAuthSessionAsync.mockResolvedValue({
        type: 'success',
        url: mockSuccessUrl,
      });

      const result = await signInWithGoogle();

      expect(result).toEqual({
        success: false,
        message: 'Google sign-in failed',
      });
      expect(mockSupabase.auth.setSession).not.toHaveBeenCalled();
    });

    it('should handle session setting failure', async () => {
      const mockOAuthUrl = 'https://accounts.google.com/oauth/authorize?...';
      mockSupabase.auth.signInWithOAuth.mockResolvedValue({
        data: { url: mockOAuthUrl },
        error: null,
      });

      const mockSuccessUrl = 'dukancardapp://auth/callback#access_token=mock_token';
      mockWebBrowser.openAuthSessionAsync.mockResolvedValue({
        type: 'success',
        url: mockSuccessUrl,
      });

      const sessionError = { message: 'Failed to set session' };
      mockSupabase.auth.setSession.mockResolvedValue({
        data: null,
        error: sessionError,
      });

      const result = await signInWithGoogle();

      expect(result).toEqual({
        success: false,
        message: 'Failed to establish session',
        error: sessionError,
      });
    });

    it('should parse URL parameters from hash fragment', async () => {
      const mockOAuthUrl = 'https://accounts.google.com/oauth/authorize?...';
      mockSupabase.auth.signInWithOAuth.mockResolvedValue({
        data: { url: mockOAuthUrl },
        error: null,
      });

      const mockSuccessUrl = 'dukancardapp://auth/callback#access_token=hash_token&refresh_token=hash_refresh';
      mockWebBrowser.openAuthSessionAsync.mockResolvedValue({
        type: 'success',
        url: mockSuccessUrl,
      });

      mockSupabase.auth.setSession.mockResolvedValue({
        data: { user: { id: 'user123' }, session: {} },
        error: null,
      });

      await signInWithGoogle();

      expect(mockSupabase.auth.setSession).toHaveBeenCalledWith({
        access_token: 'hash_token',
        refresh_token: 'hash_refresh',
      });
    });

    it('should parse URL parameters from query string', async () => {
      const mockOAuthUrl = 'https://accounts.google.com/oauth/authorize?...';
      mockSupabase.auth.signInWithOAuth.mockResolvedValue({
        data: { url: mockOAuthUrl },
        error: null,
      });

      const mockSuccessUrl = 'dukancardapp://auth/callback?access_token=query_token&refresh_token=query_refresh';
      mockWebBrowser.openAuthSessionAsync.mockResolvedValue({
        type: 'success',
        url: mockSuccessUrl,
      });

      mockSupabase.auth.setSession.mockResolvedValue({
        data: { user: { id: 'user123' }, session: {} },
        error: null,
      });

      await signInWithGoogle();

      expect(mockSupabase.auth.setSession).toHaveBeenCalledWith({
        access_token: 'query_token',
        refresh_token: 'query_refresh',
      });
    });

    it('should handle access token without refresh token', async () => {
      const mockOAuthUrl = 'https://accounts.google.com/oauth/authorize?...';
      mockSupabase.auth.signInWithOAuth.mockResolvedValue({
        data: { url: mockOAuthUrl },
        error: null,
      });

      const mockSuccessUrl = 'dukancardapp://auth/callback#access_token=token_only';
      mockWebBrowser.openAuthSessionAsync.mockResolvedValue({
        type: 'success',
        url: mockSuccessUrl,
      });

      mockSupabase.auth.setSession.mockResolvedValue({
        data: { user: { id: 'user123' }, session: {} },
        error: null,
      });

      await signInWithGoogle();

      expect(mockSupabase.auth.setSession).toHaveBeenCalledWith({
        access_token: 'token_only',
        refresh_token: '',
      });
    });

    it('should handle unexpected errors during sign-in process', async () => {
      const error = new Error('Unexpected error');
      mockSupabase.auth.signInWithOAuth.mockRejectedValue(error);

      const result = await signInWithGoogle();

      expect(result).toEqual({
        success: false,
        message: 'An unexpected error occurred during Google sign-in',
        error,
      });
    });

    it('should handle web browser errors', async () => {
      const mockOAuthUrl = 'https://accounts.google.com/oauth/authorize?...';
      mockSupabase.auth.signInWithOAuth.mockResolvedValue({
        data: { url: mockOAuthUrl },
        error: null,
      });

      mockWebBrowser.openAuthSessionAsync.mockRejectedValue(new Error('Browser error'));

      const result = await signInWithGoogle();

      expect(result).toEqual({
        success: false,
        message: 'An unexpected error occurred during Google sign-in',
        error: expect.any(Error),
      });
    });
  });

  describe('signInWithGoogleDeepLink', () => {
    it('should successfully initiate Google sign-in with deep linking', async () => {
      mockSupabase.auth.signInWithOAuth.mockResolvedValue({
        data: { url: 'https://accounts.google.com/oauth/authorize?...' },
        error: null,
      });

      const result = await signInWithGoogleDeepLink();

      expect(mockSupabase.auth.signInWithOAuth).toHaveBeenCalledWith({
        provider: 'google',
        options: {
          redirectTo: 'dukancardapp://auth/callback',
          queryParams: {
            access_type: 'offline',
            prompt: 'select_account',
          },
        },
      });
      expect(result).toEqual({
        success: true,
        message: 'Google sign-in initiated',
      });
    });

    it('should handle OAuth initialization failure in deep link mode', async () => {
      const error = { message: 'OAuth init failed' };
      mockSupabase.auth.signInWithOAuth.mockResolvedValue({
        data: null,
        error,
      });

      const result = await signInWithGoogleDeepLink();

      expect(result).toEqual({
        success: false,
        message: 'Failed to initialize Google sign-in',
        error,
      });
    });

    it('should handle unexpected errors in deep link mode', async () => {
      const error = new Error('Network error');
      mockSupabase.auth.signInWithOAuth.mockRejectedValue(error);

      const result = await signInWithGoogleDeepLink();

      expect(result).toEqual({
        success: false,
        message: 'An unexpected error occurred during Google sign-in',
        error,
      });
    });

    it('should use correct deep link URL scheme', async () => {
      mockSupabase.auth.signInWithOAuth.mockResolvedValue({
        data: { url: 'https://accounts.google.com/oauth/authorize?...' },
        error: null,
      });

      await signInWithGoogleDeepLink();

      expect(mockSupabase.auth.signInWithOAuth).toHaveBeenCalledWith(
        expect.objectContaining({
          options: expect.objectContaining({
            redirectTo: 'dukancardapp://auth/callback',
          }),
        })
      );
    });
  });

  describe('Error handling and edge cases', () => {
    it('should handle malformed callback URLs', async () => {
      const mockOAuthUrl = 'https://accounts.google.com/oauth/authorize?...';
      mockSupabase.auth.signInWithOAuth.mockResolvedValue({
        data: { url: mockOAuthUrl },
        error: null,
      });

      const malformedUrl = 'not-a-valid-url';
      mockWebBrowser.openAuthSessionAsync.mockResolvedValue({
        type: 'success',
        url: malformedUrl,
      });

      const result = await signInWithGoogle();

      expect(result).toEqual({
        success: false,
        message: 'Google sign-in failed',
      });
    });

    it('should handle empty URL parameters', async () => {
      const mockOAuthUrl = 'https://accounts.google.com/oauth/authorize?...';
      mockSupabase.auth.signInWithOAuth.mockResolvedValue({
        data: { url: mockOAuthUrl },
        error: null,
      });

      const emptyParamsUrl = 'dukancardapp://auth/callback#';
      mockWebBrowser.openAuthSessionAsync.mockResolvedValue({
        type: 'success',
        url: emptyParamsUrl,
      });

      const result = await signInWithGoogle();

      expect(result).toEqual({
        success: false,
        message: 'Google sign-in failed',
      });
    });

    it('should handle unknown WebBrowser result types', async () => {
      const mockOAuthUrl = 'https://accounts.google.com/oauth/authorize?...';
      mockSupabase.auth.signInWithOAuth.mockResolvedValue({
        data: { url: mockOAuthUrl },
        error: null,
      });

      mockWebBrowser.openAuthSessionAsync.mockResolvedValue({
        type: 'dismiss' as any,
      });

      const result = await signInWithGoogle();

      expect(result).toEqual({
        success: false,
        message: 'Google sign-in failed',
      });
    });
  });

  describe('Console logging', () => {
    it('should log errors when sign-in fails', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      const error = new Error('Test error');
      
      mockSupabase.auth.signInWithOAuth.mockRejectedValue(error);

      await signInWithGoogle();

      expect(consoleSpy).toHaveBeenCalledWith('Google sign-in error:', error);
      
      consoleSpy.mockRestore();
    });

    it('should not log errors for successful operations', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      
      const mockOAuthUrl = 'https://accounts.google.com/oauth/authorize?...';
      mockSupabase.auth.signInWithOAuth.mockResolvedValue({
        data: { url: mockOAuthUrl },
        error: null,
      });

      const mockSuccessUrl = 'dukancardapp://auth/callback#access_token=mock_token';
      mockWebBrowser.openAuthSessionAsync.mockResolvedValue({
        type: 'success',
        url: mockSuccessUrl,
      });

      mockSupabase.auth.setSession.mockResolvedValue({
        data: { user: { id: 'user123' }, session: {} },
        error: null,
      });

      await signInWithGoogle();

      expect(consoleSpy).not.toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });
  });

  describe('Configuration validation', () => {
    it('should use correct OAuth configuration for in-app browser', async () => {
      mockSupabase.auth.signInWithOAuth.mockResolvedValue({
        data: { url: 'https://accounts.google.com/oauth/authorize?...' },
        error: null,
      });

      await signInWithGoogle();

      expect(mockSupabase.auth.signInWithOAuth).toHaveBeenCalledWith({
        provider: 'google',
        options: {
          redirectTo: expect.stringContaining('dukancardapp://auth/callback'),
          queryParams: {
            access_type: 'offline',
            prompt: 'select_account',
            approval_prompt: 'force',
          },
        },
      });
    });

    it('should use correct WebBrowser configuration', async () => {
      const mockOAuthUrl = 'https://accounts.google.com/oauth/authorize?...';
      mockSupabase.auth.signInWithOAuth.mockResolvedValue({
        data: { url: mockOAuthUrl },
        error: null,
      });

      mockWebBrowser.openAuthSessionAsync.mockResolvedValue({
        type: 'cancel',
      });

      await signInWithGoogle();

      expect(mockWebBrowser.openAuthSessionAsync).toHaveBeenCalledWith(
        mockOAuthUrl,
        expect.stringContaining('dukancardapp://auth/callback'),
        {
          showTitle: true,
          showInRecents: false,
          enableBarCollapsing: true,
          controlsColor: '#C29D5B',
          toolbarColor: '#ffffff',
          createTask: false,
        }
      );
    });
  });
});