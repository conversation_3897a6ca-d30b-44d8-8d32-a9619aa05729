### User Story 1.1: Create `devices` Table Schema

**Description:** This story creates the foundational `devices` table in the database, which is essential for tracking the clients that connect to the API and for the HMAC security model.

**Acceptance Criteria:**
- A new, reversible SQL migration for the `devices` table is created.
- The table schema matches the architecture specification.
- The migration successfully applies to a local development database.

---

### Development Tasks

-   [ ] **1. Create a new migration file**
    *   In the `dukancard` project directory, run the following Supabase CLI command to generate a new migration file. Name it descriptively.
    *   **Command:** `supabase migration new create_devices_table`

-   [ ] **2. Define the 'Up' Migration (Create Table)**
    *   Open the newly generated SQL file in `supabase/migrations/`.
    *   Add the following SQL script to define the `devices` table and its index. Note the foreign key reference to `auth.users(id)`.

    ```sql
    -- supabase/migrations/{timestamp}_create_devices_table.sql

    CREATE TABLE public.devices (
        device_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
        device_name text,
        platform text,
        device_secret_hash text NOT NULL,
        app_signature_hash text,
        revoked boolean NOT NULL DEFAULT false,
        last_seen_at timestamptz,
        created_at timestamptz NOT NULL DEFAULT now()
    );

    -- Add index for faster lookups on user_id
    CREATE INDEX idx_devices_user_id ON public.devices(user_id);

    -- Add comments for clarity
    COMMENT ON TABLE public.devices IS 'Stores information about user devices for authentication and security.';
    COMMENT ON COLUMN public.devices.device_secret_hash IS 'Hashed device secret using bcrypt or argon2id.';
    ```

-   [ ] **3. Document the 'Down' Migration (Drop Table)**
    *   It is critical that every migration can be reverted. While the Supabase CLI workflow primarily uses `db reset` for local development, documenting the `down` migration is crucial for understanding and for potential manual rollbacks in a production environment.
    *   Add this as a comment in the same migration file:
    ```sql
    -- Down Migration
    -- DROP TABLE public.devices;
    ```

-   [ ] **4. Unit Test the Migration**
    *   **Task:** Apply the migration to your local development database to ensure it is valid.
    *   **Command:** `supabase db reset`
    *   **Verification:**
        *   After the command completes, connect to your local database using the Supabase Studio UI or another SQL client.
        *   Verify that the `public.devices` table exists and that all columns, constraints, and indexes were created correctly.
        *   Confirm that the foreign key to `auth.users` is in place.