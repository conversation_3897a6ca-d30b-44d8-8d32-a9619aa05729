"use server";

import { BusinessProfilePublicData, BusinessSortBy } from "./types";

/**
 * Securely fetch business profiles by location using the service role key
 */
export async function getSecureBusinessProfilesByLocation(
  location: {
    pincode?: string;
    city?: string;
    state?: string;
    locality?: string;
  },
  page: number = 1,
  limit: number = 20,
  sortBy: BusinessSortBy = "created_desc"
): Promise<{
  data?: BusinessProfilePublicData[];
  count?: number;
  error?: string;
}> {
  try {
    // Build query parameters
    const queryParams = new URLSearchParams();
    queryParams.set('page', page.toString());
    queryParams.set('limit', limit.toString());
    queryParams.set('status', 'online');

    // Add location filters if provided
    if (location.pincode) {
      queryParams.set('pincode', location.pincode);
    }
    if (location.city) {
      queryParams.set('city', location.city);
    }
    if (location.state) {
      queryParams.set('state', location.state);
    }
    if (location.locality) {
      queryParams.set('locality', location.locality);
    }

    // Convert sortBy to API format
    let apiSortBy = 'created_desc';
    switch (sortBy) {
      case 'name_asc':
        apiSortBy = 'name_asc';
        break;
      case 'name_desc':
        apiSortBy = 'name_desc';
        break;
      case 'created_asc':
        apiSortBy = 'created_asc';
        break;
      case 'created_desc':
        apiSortBy = 'created_desc';
        break;
      case 'likes_asc':
        apiSortBy = 'likes_asc';
        break;
      case 'likes_desc':
        apiSortBy = 'likes_desc';
        break;
      case 'subscriptions_asc':
        apiSortBy = 'subscriptions_asc';
        break;
      case 'subscriptions_desc':
        apiSortBy = 'subscriptions_desc';
        break;
      case 'rating_asc':
        apiSortBy = 'rating_asc';
        break;
      case 'rating_desc':
        apiSortBy = 'rating_desc';
        break;
    }
    queryParams.set('sort_by', apiSortBy);

    // Use the business profile API endpoint
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business?${queryParams.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result = await response.json();

    if (!response.ok) {
      console.error("API Fetch Error:", result);
      return {
        error: result.error || "Failed to fetch business profiles by location",
      };
    }

    const businesses = result.businesses || [];
    const count = result.pagination?.total || 0;

    // Transform data to match expected format
    const safeData: BusinessProfilePublicData[] = businesses.map((profile: any) => {
      return {
        ...profile,
        // Add missing fields with default values if not present
        total_visits: profile.total_visits || 0,
        today_visits: profile.today_visits || 0,
        yesterday_visits: profile.yesterday_visits || 0,
        visits_7_days: profile.visits_7_days || 0,
        visits_30_days: profile.visits_30_days || 0,
        city_slug: profile.city_slug || null,
        state_slug: profile.state_slug || null,
        locality_slug: profile.locality_slug || null,
        gallery: profile.gallery || null,
        latitude: profile.latitude || null,
        longitude: profile.longitude || null,
      } as BusinessProfilePublicData;
    });

    return { data: safeData, count };
  } catch (error) {
    console.error(
      "Unexpected error in getSecureBusinessProfilesByLocation:",
      error
    );
    return { error: "An unexpected error occurred." };
  }
}
