import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { createServiceRoleClient } from '@/utils/supabase/service-role';
import { 
  bruteForceProtectionMiddleware, 
  getClientIP 
} from '@/lib/middleware/bruteForceProtection';

const sendOtpSchema = z.object({
  email: z.string().email("Invalid email format"),
});

export async function POST(req: NextRequest) {
  try {
    // 1. Parse and validate request body
    const body = await req.json();
    const validation = sendOtpSchema.safeParse(body);
    if (!validation.success) {
      return new NextResponse(JSON.stringify({ 
        error: 'Validation failed',
        details: validation.error.issues 
      }), { 
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const { email } = validation.data;

    // 2. Apply brute-force protection
    const ipAddress = getClientIP(req);

    const bruteForceCheck = await bruteForceProtectionMiddleware(req, {
      operation: 'send_otp',
      email,
      ipAddress,
    });

    if (bruteForceCheck) {
      return bruteForceCheck; // Rate limited
    }

    // 3. Send OTP using Supabase Auth
    const supabase = createServiceRoleClient();
    
    const { error } = await supabase.auth.signInWithOtp({
      email: email,
      options: {
        shouldCreateUser: true,
        data: {
          auth_type: "email",
        },
      },
    });

    if (error) {
      // Handle specific Supabase errors
      if (error.message.includes('Email rate limit exceeded')) {
        return new NextResponse(JSON.stringify({ 
          error: 'Email rate limit exceeded. Please wait before requesting another OTP.',
          isConfigurationError: true
        }), { 
          status: 429,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      return new NextResponse(JSON.stringify({ 
        error: error.message || 'Failed to send OTP'
      }), { 
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 4. Return success response
    return NextResponse.json({
      success: true,
      message: "OTP sent to your email address. Please check your inbox.",
    });

  } catch (error) {
    console.error('Unexpected error in send-otp:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), { 
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}