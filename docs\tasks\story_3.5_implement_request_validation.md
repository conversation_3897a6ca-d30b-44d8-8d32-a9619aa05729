### User Story 3.5: Implement Reusable Request Validation

**Description:** This story establishes a standardized, reusable pattern for validating all incoming API requests. Using a schema-based library like <PERSON><PERSON> ensures data integrity and provides clear error messages to clients, preventing a wide class of bugs.

**Acceptance Criteria:**
- A clear, documented pattern for request validation is established.
- At least one existing endpoint is refactored to use the new pattern.
- Failed validations result in a `400 Bad Request` response with detailed error messages.

---

### Development Tasks

-   [ ] **1. Install Zod**
    *   **Task:** Add the Zod library to the `dukancard` project.
    *   **Command:** `npm install zod`

-   [ ] **2. Establish a Central Directory for Schemas**
    *   **Task:** To promote reusability, create a centralized directory to store all Zod validation schemas.
    *   **Action:** Create a new directory: `dukancard/lib/schemas`.
    *   **Example:** Create a file like `dukancard/lib/schemas/auth.ts` to hold the schemas for login, refresh, etc.

-   [ ] **3. Define the Validation Pattern**
    *   **Task:** The pattern is to define a Zod schema and use it at the beginning of every API route handler to validate the request.
    *   **Example Implementation in a Route:**

    ```typescript
    // In an API route file, e.g., /api/devices/register/route.ts
    import { registerDeviceSchema } from '@/lib/schemas/device'; // Assuming schema is defined here

    export async function POST(req: NextRequest) {
        const body = await req.json();
        const validation = registerDeviceSchema.safeParse(body);

        if (!validation.success) {
            return new NextResponse(
                JSON.stringify({ 
                    message: 'Invalid request body', 
                    errors: validation.error.flatten().fieldErrors 
                }), 
                { status: 400 }
            );
        }

        // Proceed with the validated data: validation.data
        // ... rest of the handler logic
    }
    ```

-   [ ] **4. Refactor an Existing Endpoint**
    *   **Task:** Apply this new validation pattern to one of the previously created endpoints.
    *   **Action:** Refactor the `POST /api/devices/register` endpoint (from Story 2.1) to use a Zod schema for validation instead of any manual checks.

-   [ ] **5. Document the Convention**
    *   **Task:** Create a brief document explaining this convention for all developers.
    *   **File Path:** `dukancard/docs/api_validation_pattern.md`
    *   **Content:** The document should state that all API routes must validate their inputs (body, params, query) using a Zod schema defined in `lib/schemas`. It should include the example code from Step 3.

-   [ ] **6. Update Unit Tests**
    *   **Task:** Update the tests for the refactored endpoint (`/api/devices/register`).
    *   **File Path:** `dukancard/__tests__/app/api/devices/register/route.test.ts`
    *   **Action:** Add a new test case that sends a request with multiple validation errors (e.g., wrong data type for one field, another field missing). Assert that the response is `400` and that the `errors` object in the response body accurately reflects the specific failures, as formatted by Zod.