### User Story 4.5: Refactor All Remaining Features to Use the API

**User Story:** As a developer, I want to ensure all remaining features of the web application are migrated to the new API architecture, so that we can completely eliminate direct Supabase client access from the `dukancard` project.

**Acceptance Criteria:**
- All remaining application features (posts, comments, likes, etc.) are fully functional.
- The entire `dukancard` web application no longer contains any instance of the Supabase client being used for data queries or modifications. All data flows through the new internal API.
- The application is ready to serve as a stable and secure backend for the mobile app migration in the next epic.

---

### Development Tasks

-   [ ] **1. Comprehensive Codebase Audit for Supabase Calls**
    *   **Developer's Task:** Perform a thorough search across the entire `dukancard` codebase to identify any and all remaining direct calls to the Supabase client (e.g., `supabase.from(...)`, `supabase.auth(...)` outside of the new authentication flow). Document these findings.

-   [ ] **2. Group and Prioritize Remaining Features**
    *   **Developer's Task:** Group the identified remaining features into logical categories (e.g., posts, comments, likes, subscriptions, ratings, notifications, etc.). Prioritize them based on usage or complexity.

-   [ ] **3. Design and Implement Remaining API Routes**
    *   **Developer's Task:** For each identified feature group, design and implement the necessary API routes (CRUD operations as needed). Ensure these routes are protected by the security middleware and implement appropriate authorization checks (e.g., a user can only like their own post, or view comments on public posts).

-   [ ] **4. Refactor Client-Side Code for Remaining Features**
    *   **Developer's Task:** Refactor the corresponding frontend components and pages to use the newly created API routes. This involves updating data fetching, form submissions, and any other interactions that previously used the Supabase client directly.

-   [ ] **5. Final Supabase Client Removal**
    *   **Developer's Task:** Once all direct Supabase client calls have been replaced by calls to the new internal API, remove the Supabase client library from the `dukancard` project's dependencies.
    *   **Action:** Remove `@supabase/supabase-js` (and any related Supabase client libraries) from `dukancard/package.json`.
    *   **Verification:** Ensure the application still builds and runs correctly without the Supabase client dependency.