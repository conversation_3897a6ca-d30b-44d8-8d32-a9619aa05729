import { type NextRequest, NextResponse } from "next/server";
import { updateSession } from "./utils/supabase/middleware";
import { updateSessionWithJWT } from "./utils/auth/jwt-middleware";
import { Ratelimit } from "@upstash/ratelimit";
import { Redis } from "@upstash/redis";

// Initialize Redis client for rate limiting only
const redisUrl = process.env.UPSTASH_REDIS_REST_URL;
const redisToken = process.env.UPSTASH_REDIS_REST_TOKEN;

if (!redisUrl || !redisToken) {
  console.error("Upstash Redis URL or Token is not defined in environment variables.");
}

const redis = redisUrl && redisToken ? new Redis({ url: redisUrl, token: redisToken }) : null;

// Initialize Rate Limiter
const maxRequests = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || "10");
const windowSeconds = parseInt(process.env.RATE_LIMIT_WINDOW_SECONDS || "10");

const ratelimit = redis
  ? new Ratelimit({
      redis: redis,
      limiter: Ratelimit.slidingWindow(maxRequests, `${windowSeconds} s`),
      analytics: true,
      prefix: "@upstash/ratelimit/dukancard",
    })
  : null;


export async function middleware(request: NextRequest) {
  // --- Test Environment Bypass START ---
  // Check if we're in a test environment (Playwright E2E tests)
  const isTestEnvironment = process.env.NODE_ENV === 'test' ||
                           process.env.PLAYWRIGHT_TESTING === 'true' ||
                           request.headers.get('x-playwright-testing') === 'true';

  if (isTestEnvironment) {
    // For test environment, we'll handle auth differently
    // This allows us to test business logic without real authentication
    return await handleTestEnvironment(request);
  }
  // --- Test Environment Bypass END ---

  // --- Domain and HTTPS Redirect Logic START ---
  const url = request.nextUrl.clone();
  const hostname = url.hostname;
  const protocol = url.protocol;

  // Only apply redirects in production environment and exclude development/testing domains
  const isDevelopmentDomain = hostname.includes('localhost') ||
                              hostname.includes('ngrok.io') ||
                              hostname.includes('ngrok-free.app') ||
                              hostname.includes('127.0.0.1');

  if (process.env.NODE_ENV === 'production' && !isDevelopmentDomain) {
    let shouldRedirect = false;

    // Check for www redirect (www.dukancard.in -> dukancard.in)
    if (hostname.startsWith('www.')) {
      url.hostname = hostname.replace('www.', '');
      shouldRedirect = true;
    }

    // Check for HTTPS redirect (http:// -> https://)
    if (protocol === 'http:') {
      url.protocol = 'https:';
      shouldRedirect = true;
    }

    // Perform redirect if needed
    if (shouldRedirect) {
      return NextResponse.redirect(url.toString(), 301); // Permanent redirect
    }
  }
  // --- Domain and HTTPS Redirect Logic END ---

  // --- Rate Limiting Logic START ---
  // Apply rate limiting to API routes only (skip webhooks)
  if (request.nextUrl.pathname.startsWith("/api/") && !request.nextUrl.pathname.startsWith("/api/webhooks/")) {
    // Skip rate limiting if Redis is not configured
    if (!ratelimit) {
      console.warn("Rate limiting skipped: Redis not configured");
    } else {
      // Get IP address: Check 'x-forwarded-for' header first, then fallback.
      const forwardedFor = request.headers.get('x-forwarded-for');
      // The header can contain multiple IPs (client, proxy1, proxy2). The client IP is usually the first one.
      const ip = forwardedFor ? forwardedFor.split(',')[0].trim() : "127.0.0.1";

      try {
        // Use Upstash rate limiting
        const { success, limit, remaining, reset } = await ratelimit.limit(ip);

        if (!success) {
          // Rate limit exceeded, return 429
          return new NextResponse("Too Many Requests", {
            status: 429,
            headers: {
              "X-RateLimit-Limit": limit.toString(),
              "X-RateLimit-Remaining": remaining.toString(),
              "X-RateLimit-Reset": new Date(reset * 1000).toISOString(),
            },
          });
        }
      } catch (error) {
        console.error("Rate limiting error:", error);
        // If rate limiting fails, allow the request to proceed
      }
    }
  }
  // --- Rate Limiting Logic END ---


  // Use JWT-based session validation instead of Supabase
  return await updateSessionWithJWT(request);
}

/**
 * Handle requests in test environment
 * This replicates the complete Supabase middleware authentication flow
 * without making actual Supabase calls, allowing us to test business logic
 */
async function handleTestEnvironment(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Check for test authentication headers
  const testAuthState = request.headers.get('x-test-auth-state');
  const testUserType = request.headers.get('x-test-user-type');
  const testHasProfile = request.headers.get('x-test-has-profile') === 'true';
  const testBusinessSlug = request.headers.get('x-test-business-slug');
  const testPlanId = request.headers.get('x-test-plan-id') || 'free';

  // Define protected path prefixes (same as Supabase middleware)
  const protectedPrefixes = [
    "/dashboard",
    "/onboarding",
    "/choose-role",
  ];

  const isProtectedRoute = protectedPrefixes.some((prefix) => pathname.startsWith(prefix));

  // Check if user just logged out (same logic as Supabase middleware)
  const justLoggedOut = request.nextUrl.searchParams.get('logged_out') === 'true';

  // === UNAUTHENTICATED USER LOGIC ===
  if (testAuthState === 'unauthenticated' || testAuthState !== 'authenticated') {
    // Redirect to login if user is not authenticated AND accessing a protected path
    if (isProtectedRoute) {
      const url = request.nextUrl.clone();
      url.pathname = "/login";
      url.searchParams.set("next", pathname);
      return NextResponse.redirect(url);
    }
    return NextResponse.next();
  }

  // === AUTHENTICATED USER LOGIC ===
  if (testAuthState === 'authenticated') {
    // === NO PROFILE LOGIC ===
    if (!testHasProfile) {
      // No profile found - first time user
      // Allow access ONLY to choose-role page OR onboarding page
      // EXCEPTION: If user just logged out, allow them to reach login page
      if (pathname !== "/choose-role" && pathname !== "/onboarding" && !justLoggedOut) {
        const url = request.nextUrl.clone();
        url.pathname = "/choose-role";
        return NextResponse.redirect(url);
      }
      // If already on choose-role or onboarding, allow the request
      return NextResponse.next();
    }

    // === PROFILE EXISTS LOGIC ===
    if (testHasProfile && testUserType) {
      // === BUSINESS USER ONBOARDING CHECK ===
      // If business user hasn't completed onboarding (no slug), redirect to onboarding
      if (testUserType === "business" && !testBusinessSlug && pathname !== "/onboarding" && !justLoggedOut) {
        const url = request.nextUrl.clone();
        url.pathname = "/onboarding";
        return NextResponse.redirect(url);
      }

      // === FREE TIER FEATURE ACCESS CHECKS ===
      if (testUserType === "business") {
        // Free plan restrictions
        if (testPlanId === "free") {
          // Check for analytics access
          if (pathname.startsWith("/dashboard/business/analytics")) {
            // Redirect free tier users away from analytics pages
            const url = request.nextUrl.clone();
            url.pathname = "/dashboard/business/plan";
            url.searchParams.set("upgrade", "analytics");
            return NextResponse.redirect(url);
          }
        }
      }

      // === REDIRECT AWAY FROM AUTH PAGES ===
      // Redirect away from public auth pages if logged in and profile exists
      if (
        (pathname === "/login" || pathname === "/choose-role") &&
        !(pathname === "/login" && justLoggedOut)
      ) {
        const redirectPath = testUserType === "business"
          ? "/dashboard/business"
          : "/dashboard/customer";
        const url = request.nextUrl.clone();
        url.pathname = redirectPath;
        return NextResponse.redirect(url);
      }

      // === ONBOARDING REDIRECT FOR CUSTOMERS ===
      // Redirect away from onboarding if user is a customer
      if (pathname === "/onboarding" && testUserType === "customer" && !justLoggedOut) {
        const url = request.nextUrl.clone();
        url.pathname = "/dashboard/customer";
        return NextResponse.redirect(url);
      }

      // === DASHBOARD ROUTE PROTECTION ===
      // Protect dashboard routes based on user type
      if (
        pathname.startsWith("/dashboard/customer") &&
        testUserType !== "customer" &&
        !justLoggedOut
      ) {
        const url = request.nextUrl.clone();
        url.pathname = "/dashboard/business";
        return NextResponse.redirect(url);
      }
      if (
        pathname.startsWith("/dashboard/business") &&
        testUserType !== "business" &&
        !justLoggedOut
      ) {
        const url = request.nextUrl.clone();
        url.pathname = "/dashboard/customer";
        return NextResponse.redirect(url);
      }
    }
  }

  // Default: allow request to proceed
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except forhe ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Feel free to modify this pattern to include more paths.
     */
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};