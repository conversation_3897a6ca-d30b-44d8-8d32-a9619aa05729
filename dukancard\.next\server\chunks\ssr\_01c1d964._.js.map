{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/schemas/authSchemas.ts"], "sourcesContent": ["import { z } from \"zod\";\n\nexport const IndianMobileSchema = z\n  .string()\n  .trim()\n  .min(10, { message: \"Mobile number must be 10 digits\" })\n  .max(10, { message: \"Mobile number must be 10 digits\" })\n  .regex(/^[6-9]\\d{9}$/, { message: \"Please enter a valid Indian mobile number\" });\n\nexport const EmailOTPSchema = z.object({\n  email: z\n    .string()\n    .trim()\n    .min(1, { message: \"Email is required\" })\n    .email({ message: \"Please enter a valid email address\" }),\n});\n\nexport const VerifyOTPSchema = z.object({\n  email: z\n    .string()\n    .trim()\n    .min(1, { message: \"Email is required\" })\n    .email({ message: \"Please enter a valid email address\" }),\n  otp: z\n    .string()\n    .trim()\n    .min(6, { message: \"OTP must be 6 digits\" })\n    .max(6, { message: \"OTP must be 6 digits\" })\n    .regex(/^\\d{6}$/, { message: \"OTP must be 6 digits\" }),\n});\n\nexport const PasswordComplexitySchema = z\n  .string()\n  .min(6, \"Password must be at least 6 characters long\")\n  .regex(/[A-Z]/, \"Password must contain at least one uppercase letter\")\n  .regex(/[a-z]/, \"Password must contain at least one lowercase letter\")\n  .regex(/\\d/, \"Password must contain at least one number\")\n  .regex(/[^a-zA-Z0-9]/, \"Password must contain at least one special character\");\n\nexport const MobilePasswordLoginSchema = z.object({\n  mobile: IndianMobileSchema,\n  password: z.string().trim().min(1, { message: \"Password is required\" }),\n});"], "names": [], "mappings": ";;;;;;;AAAA;;AAEO,MAAM,qBAAqB,oIAAA,CAAA,IAAC,CAChC,MAAM,GACN,IAAI,GACJ,GAAG,CAAC,IAAI;IAAE,SAAS;AAAkC,GACrD,GAAG,CAAC,IAAI;IAAE,SAAS;AAAkC,GACrD,KAAK,CAAC,gBAAgB;IAAE,SAAS;AAA4C;AAEzE,MAAM,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,OAAO,oIAAA,CAAA,IAAC,CACL,MAAM,GACN,IAAI,GACJ,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoB,GACtC,KAAK,CAAC;QAAE,SAAS;IAAqC;AAC3D;AAEO,MAAM,kBAAkB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,OAAO,oIAAA,CAAA,IAAC,CACL,MAAM,GACN,IAAI,GACJ,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoB,GACtC,KAAK,CAAC;QAAE,SAAS;IAAqC;IACzD,KAAK,oIAAA,CAAA,IAAC,CACH,MAAM,GACN,IAAI,GACJ,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuB,GACzC,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuB,GACzC,KAAK,CAAC,WAAW;QAAE,SAAS;IAAuB;AACxD;AAEO,MAAM,2BAA2B,oIAAA,CAAA,IAAC,CACtC,MAAM,GACN,GAAG,CAAC,GAAG,+CACP,KAAK,CAAC,SAAS,uDACf,KAAK,CAAC,SAAS,uDACf,KAAK,CAAC,MAAM,6CACZ,KAAK,CAAC,gBAAgB;AAElB,MAAM,4BAA4B,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChD,QAAQ;IACR,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuB;AACvE", "debugId": null}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/login/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { z } from \"zod\";\r\nimport { EmailOTPSchema, VerifyOTPSchema, MobilePasswordLoginSchema } from \"@/lib/schemas/authSchemas\";\r\n\r\n// Validate email format\r\nfunction validateEmail(email: string): { isValid: boolean; message?: string } {\r\n  if (!email) {\r\n    return { isValid: false, message: 'Email is required' };\r\n  }\r\n  \r\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n  if (!emailRegex.test(email)) {\r\n    return { isValid: false, message: 'Please enter a valid email address' };\r\n  }\r\n  \r\n  return { isValid: true };\r\n}\r\n\r\n// Validate OTP format\r\nfunction validateOTP(otp: string): { isValid: boolean; message?: string } {\r\n  if (!otp) {\r\n    return { isValid: false, message: 'OTP is required' };\r\n  }\r\n  \r\n  if (otp.length !== 6) {\r\n    return { isValid: false, message: 'OTP must be 6 digits' };\r\n  }\r\n  \r\n  if (!/^\\d{6}$/.test(otp)) {\r\n    return { isValid: false, message: 'OTP must contain only numbers' };\r\n  }\r\n  \r\n  return { isValid: true };\r\n}\r\n\r\n// Send OTP to email using new secure API\r\nexport async function sendOTP(values: z.infer<typeof EmailOTPSchema>) {\r\n  const { email } = values;\r\n  const emailValidation = validateEmail(email);\r\n  if (!emailValidation.isValid) {\r\n    return {\r\n      success: false,\r\n      error: emailValidation.message,\r\n    };\r\n  }\r\n\r\n  try {\r\n    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');\r\n    const response = await fetch(`${baseUrl}/api/auth/send-otp`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({ email }),\r\n    });\r\n\r\n    const data = await response.json();\r\n\r\n    if (!response.ok) {\r\n      // Check if this is a configuration error (email rate limit)\r\n      if ('isConfigurationError' in data && data.isConfigurationError) {\r\n        return {\r\n          success: false,\r\n          error: data.error,\r\n          isConfigurationError: true,\r\n        };\r\n      }\r\n\r\n      return {\r\n        success: false,\r\n        error: data.error || 'Failed to send OTP',\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      message: data.message || \"OTP sent to your email address. Please check your inbox.\",\r\n    };\r\n  } catch (error) {\r\n    console.error('Send OTP error:', error);\r\n    return {\r\n      success: false,\r\n      error: 'An unexpected error occurred. Please try again.',\r\n    };\r\n  }\r\n}\r\n\r\n// Verify OTP and sign in using new secure API\r\nexport async function verifyOTP(values: z.infer<typeof VerifyOTPSchema>) {\r\n  const { email, otp } = values;\r\n  const otpValidation = validateOTP(otp);\r\n  if (!otpValidation.isValid) {\r\n    return {\r\n      success: false,\r\n      error: otpValidation.message,\r\n    };\r\n  }\r\n\r\n  try {\r\n    const deviceInfo = {\r\n      deviceName: `Web Browser - ${new Date().toISOString().slice(0, 10)}`,\r\n      platform: 'web' as const,\r\n    };\r\n\r\n    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');\r\n    const response = await fetch(`${baseUrl}/api/auth/verify-otp`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        email,\r\n        otp,\r\n        deviceInfo,\r\n      }),\r\n    });\r\n\r\n    const data = await response.json();\r\n\r\n    if (!response.ok) {\r\n      return {\r\n        success: false,\r\n        error: data.error || 'OTP verification failed',\r\n      };\r\n    }\r\n\r\n    // Return the API response data including tokens\r\n    return {\r\n      success: true,\r\n      data: {\r\n        accessToken: data.accessToken,\r\n        refreshToken: data.refreshToken,\r\n        deviceId: data.deviceId,\r\n        deviceSecret: data.deviceSecret,\r\n        hmacKey: data.hmacKey,\r\n        // Legacy format for compatibility (will be removed later)\r\n        user: { id: 'temp-user-id' },\r\n        session: { access_token: data.accessToken }\r\n      },\r\n      message: data.message || \"Successfully signed in!\",\r\n    };\r\n  } catch (error) {\r\n    console.error('OTP verification error:', error);\r\n    return {\r\n      success: false,\r\n      error: 'An unexpected error occurred. Please try again.',\r\n    };\r\n  }\r\n}\r\n\r\n// Mobile + password login using secure API endpoint\r\nexport async function loginWithMobilePassword(values: z.infer<typeof MobilePasswordLoginSchema>) {\r\n  const validatedFields = MobilePasswordLoginSchema.safeParse(values);\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      success: false,\r\n      error: \"Invalid mobile number or password format\",\r\n    };\r\n  }\r\n\r\n  const { mobile, password } = validatedFields.data;\r\n\r\n  try {\r\n    // Format mobile number with +91 prefix as email for our API\r\n    const phoneNumber = `+91${mobile}`;\r\n    \r\n    const deviceInfo = {\r\n      deviceName: `Web Browser - ${new Date().toISOString().slice(0, 10)}`,\r\n      platform: 'web' as const,\r\n    };\r\n\r\n    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');\r\n    const response = await fetch(`${baseUrl}/api/auth/login`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        email: phoneNumber, // Use phone as email\r\n        password: password,\r\n        deviceInfo,\r\n      }),\r\n    });\r\n\r\n    const data = await response.json();\r\n\r\n    if (!response.ok) {\r\n      return {\r\n        success: false,\r\n        error: data.error || 'Login failed',\r\n      };\r\n    }\r\n\r\n    // Return the API response data including tokens\r\n    return {\r\n      success: true,\r\n      data: {\r\n        accessToken: data.accessToken,\r\n        refreshToken: data.refreshToken,\r\n        deviceId: data.deviceId,\r\n        deviceSecret: data.deviceSecret,\r\n        hmacKey: data.hmacKey,\r\n        // Legacy format for compatibility (will be removed later)\r\n        user: { id: 'temp-user-id' },\r\n        session: { access_token: data.accessToken }\r\n      },\r\n      message: \"Successfully signed in!\",\r\n    };\r\n  } catch (error) {\r\n    console.error('Mobile login error:', error);\r\n    return {\r\n      success: false,\r\n      error: 'An unexpected error occurred. Please try again.',\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAGA;;;;;AAEA,wBAAwB;AACxB,SAAS,cAAc,KAAa;IAClC,IAAI,CAAC,OAAO;QACV,OAAO;YAAE,SAAS;YAAO,SAAS;QAAoB;IACxD;IAEA,MAAM,aAAa;IACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ;QAC3B,OAAO;YAAE,SAAS;YAAO,SAAS;QAAqC;IACzE;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAEA,sBAAsB;AACtB,SAAS,YAAY,GAAW;IAC9B,IAAI,CAAC,KAAK;QACR,OAAO;YAAE,SAAS;YAAO,SAAS;QAAkB;IACtD;IAEA,IAAI,IAAI,MAAM,KAAK,GAAG;QACpB,OAAO;YAAE,SAAS;YAAO,SAAS;QAAuB;IAC3D;IAEA,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM;QACxB,OAAO;YAAE,SAAS;YAAO,SAAS;QAAgC;IACpE;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,eAAe,QAAQ,MAAsC;IAClE,MAAM,EAAE,KAAK,EAAE,GAAG;IAClB,MAAM,kBAAkB,cAAc;IACtC,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC5B,OAAO;YACL,SAAS;YACT,OAAO,gBAAgB,OAAO;QAChC;IACF;IAEA,IAAI;QACF,MAAM,UAAU,6DAAoC,CAAC,6EAAyD,uBAAuB;QACrI,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,kBAAkB,CAAC,EAAE;YAC3D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAM;QAC/B;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,4DAA4D;YAC5D,IAAI,0BAA0B,QAAQ,KAAK,oBAAoB,EAAE;gBAC/D,OAAO;oBACL,SAAS;oBACT,OAAO,KAAK,KAAK;oBACjB,sBAAsB;gBACxB;YACF;YAEA,OAAO;gBACL,SAAS;gBACT,OAAO,KAAK,KAAK,IAAI;YACvB;QACF;QAEA,OAAO;YACL,SAAS;YACT,SAAS,KAAK,OAAO,IAAI;QAC3B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAGO,eAAe,UAAU,MAAuC;IACrE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG;IACvB,MAAM,gBAAgB,YAAY;IAClC,IAAI,CAAC,cAAc,OAAO,EAAE;QAC1B,OAAO;YACL,SAAS;YACT,OAAO,cAAc,OAAO;QAC9B;IACF;IAEA,IAAI;QACF,MAAM,aAAa;YACjB,YAAY,CAAC,cAAc,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,GAAG,KAAK;YACpE,UAAU;QACZ;QAEA,MAAM,UAAU,6DAAoC,CAAC,6EAAyD,uBAAuB;QACrI,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,oBAAoB,CAAC,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB;gBACA;gBACA;YACF;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO;gBACL,SAAS;gBACT,OAAO,KAAK,KAAK,IAAI;YACvB;QACF;QAEA,gDAAgD;QAChD,OAAO;YACL,SAAS;YACT,MAAM;gBACJ,aAAa,KAAK,WAAW;gBAC7B,cAAc,KAAK,YAAY;gBAC/B,UAAU,KAAK,QAAQ;gBACvB,cAAc,KAAK,YAAY;gBAC/B,SAAS,KAAK,OAAO;gBACrB,0DAA0D;gBAC1D,MAAM;oBAAE,IAAI;gBAAe;gBAC3B,SAAS;oBAAE,cAAc,KAAK,WAAW;gBAAC;YAC5C;YACA,SAAS,KAAK,OAAO,IAAI;QAC3B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAGO,eAAe,wBAAwB,MAAiD;IAC7F,MAAM,kBAAkB,6HAAA,CAAA,4BAAyB,CAAC,SAAS,CAAC;IAE5D,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC5B,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,gBAAgB,IAAI;IAEjD,IAAI;QACF,4DAA4D;QAC5D,MAAM,cAAc,CAAC,GAAG,EAAE,QAAQ;QAElC,MAAM,aAAa;YACjB,YAAY,CAAC,cAAc,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,GAAG,KAAK;YACpE,UAAU;QACZ;QAEA,MAAM,UAAU,6DAAoC,CAAC,6EAAyD,uBAAuB;QACrI,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,eAAe,CAAC,EAAE;YACxD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO;gBACP,UAAU;gBACV;YACF;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO;gBACL,SAAS;gBACT,OAAO,KAAK,KAAK,IAAI;YACvB;QACF;QAEA,gDAAgD;QAChD,OAAO;YACL,SAAS;YACT,MAAM;gBACJ,aAAa,KAAK,WAAW;gBAC7B,cAAc,KAAK,YAAY;gBAC/B,UAAU,KAAK,QAAQ;gBACvB,cAAc,KAAK,YAAY;gBAC/B,SAAS,KAAK,OAAO;gBACrB,0DAA0D;gBAC1D,MAAM;oBAAE,IAAI;gBAAe;gBAC3B,SAAS;oBAAE,cAAc,KAAK,WAAW;gBAAC;YAC5C;YACA,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;;;IApLsB;IAoDA;IA+DA;;AAnHA,+OAAA;AAoDA,+OAAA;AA+DA,+OAAA", "debugId": null}}, {"offset": {"line": 296, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/.next-internal/server/app/%28main%29/login/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {sendOTP as '40d6a9ae93402e0d28dbc39a1da61f9a25a9080013'} from 'ACTIONS_MODULE0'\nexport {verifyOTP as '40618e9fcbfa39ef9ffb9cb228c1ace1d6627d146c'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 363, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/login/LoginForm.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LoginForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call LoginForm() from the server but LoginForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(main)/login/LoginForm.tsx <module evaluation>\",\n    \"LoginForm\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,gEACA", "debugId": null}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/login/LoginForm.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LoginForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call LoginForm() from the server but LoginForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(main)/login/LoginForm.tsx\",\n    \"LoginForm\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,4CACA", "debugId": null}}, {"offset": {"line": 391, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 401, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/auth/AuthPageBackground.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(main)/components/auth/AuthPageBackground.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(main)/components/auth/AuthPageBackground.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqT,GAClV,mFACA", "debugId": null}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/auth/AuthPageBackground.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(main)/components/auth/AuthPageBackground.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(main)/components/auth/AuthPageBackground.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiS,GAC9T,+DACA", "debugId": null}}, {"offset": {"line": 429, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 439, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/login/page.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\nimport { LoginForm } from \"./LoginForm\";\nimport { Suspense } from \"react\";\nimport AuthPageBackground from \"../components/auth/AuthPageBackground\";\n\nexport async function generateMetadata(): Promise<Metadata> {\n  const title = \"Sign In\";\n  const description =\n    \"Sign in to your Dukancard account or create a new account with just your email address.\";\n\n  return {\n    title, // Uses template: \"Sign In - Dukancard\"\n    description,\n    robots: \"noindex, follow\", // Prevent indexing\n    // Keywords are generally not needed for noindex pages\n  };\n}\n\nexport default function LoginPage() {\n  return (\n    // Use semantic background and add top padding\n    <div className=\"w-full min-h-[calc(100vh-80px)] md:min-h-[calc(100vh-64px)] flex items-center justify-center bg-neutral-50 dark:bg-neutral-950 p-2 sm:p-4 pt-6 pb-20 md:pb-6 relative overflow-hidden\">\n      {/* Add animated background */}\n      <AuthPageBackground />\n\n      <Suspense\n        fallback={\n          <div className=\"flex flex-col justify-center items-center min-h-screen gap-2 relative z-10\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[var(--brand-gold)]\"></div>\n            <p className=\"text-muted-foreground\">Loading sign in form...</p>\n          </div>\n        }\n      >\n        <LoginForm />\n      </Suspense>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;;;;;AAEO,eAAe;IACpB,MAAM,QAAQ;IACd,MAAM,cACJ;IAEF,OAAO;QACL;QACA;QACA,QAAQ;IAEV;AACF;AAEe,SAAS;IACtB,OACE,8CAA8C;kBAC9C,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,4JAAA,CAAA,UAAkB;;;;;0BAEnB,8OAAC,qMAAA,CAAA,WAAQ;gBACP,wBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;0BAIzC,cAAA,8OAAC,sIAAA,CAAA,YAAS;;;;;;;;;;;;;;;;AAIlB", "debugId": null}}]}