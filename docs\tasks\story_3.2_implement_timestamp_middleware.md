### User Story 3.2: Implement Timestamp Freshness & Replay Attack Middleware

**Description:** This story adds a crucial time-based check to the security middleware. By ensuring a request was sent very recently, we can mitigate replay attacks where an attacker tries to reuse a valid, intercepted request.

**Acceptance Criteria:**
- The security middleware rejects requests with timestamps that are too old or too far in the future.
- The validity window is configurable.

---

### Development Tasks

-   [ ] **1. Implement Timestamp Validation Logic**
    *   **Task:** In your main security middleware (`middleware.ts`), add the timestamp validation logic. This check should run **before** the more computationally expensive HMAC signature check.
    *   **Logic Steps:**
        1.  **Extract Header:** Get the `X-Timestamp` value from the request headers. If it's missing, reject with a `400 Bad Request`.
        2.  **Parse Timestamp:** Convert the timestamp string to a number. Handle potential parsing errors (e.g., if the header contains non-numeric characters) by rejecting with a `400 Bad Request`.
        3.  **Get Current Time:** Get the current server timestamp using `Date.now()`.
        4.  **Calculate Difference:** Compute the absolute difference between the server time and the request timestamp.
        5.  **Compare with Window:** Compare the difference against the allowed validity window (see next task). If the difference is greater than the window, the request is stale.
        6.  **Reject or Proceed:** If the request is stale, reject immediately with a `408 Request Timeout` and an error message like "Request has expired". Otherwise, allow the request to proceed to the next middleware.

-   [ ] **2. Make Time Window Configurable**
    *   **Task:** Do not hardcode the validity window (e.g., 120 seconds) in the code.
    *   **Action:**
        *   Create a new environment variable, e.g., `REQUEST_TIMESTAMP_VALIDITY_SECONDS`.
        *   In your middleware code, read this environment variable. Use a sensible default value (e.g., `120`) if the variable is not set.
        *   Remember to multiply by 1000 in your code to convert seconds to milliseconds for comparison with `Date.now()`.
        *   Update your `.env.example` file with this new variable.

-   [ ] **3. Write Integration Tests**
    *   **Task:** Add tests for the timestamp validation logic to your middleware test suite.
    *   **File Path:** `dukancard/__tests__/middleware.test.ts`
    *   **Test Cases:**
        *   **Test 1: Valid Timestamp:** Mock a request with an `X-Timestamp` that is 30 seconds in the past. Assert that the middleware allows the request to pass.
        *   **Test 2: Stale (Old) Timestamp:** Mock a request with an `X-Timestamp` that is 3 minutes in the past (assuming a 120s window). Assert that the middleware rejects with a `408` status code.
        *   **Test 3: Stale (Future) Timestamp:** Mock a request with an `X-Timestamp` that is 3 minutes in the future. Assert that the middleware rejects with a `408` status code.
        *   **Test 4: Missing Timestamp:** Mock a request that is missing the `X-Timestamp` header. Assert that the middleware rejects with a `400` status code.
        *   **Test 5: Malformed Timestamp:** Mock a request where `X-Timestamp` is an invalid string (e.g., `"not-a-number"`). Assert that the middleware rejects with a `400` status code.