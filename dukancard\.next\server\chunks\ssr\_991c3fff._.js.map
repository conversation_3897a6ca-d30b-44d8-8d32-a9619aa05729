{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/login/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { z } from \"zod\";\r\nimport { EmailOTPSchema, VerifyOTPSchema, MobilePasswordLoginSchema } from \"@/lib/schemas/authSchemas\";\r\n\r\n// Validate email format\r\nfunction validateEmail(email: string): { isValid: boolean; message?: string } {\r\n  if (!email) {\r\n    return { isValid: false, message: 'Email is required' };\r\n  }\r\n  \r\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n  if (!emailRegex.test(email)) {\r\n    return { isValid: false, message: 'Please enter a valid email address' };\r\n  }\r\n  \r\n  return { isValid: true };\r\n}\r\n\r\n// Validate OTP format\r\nfunction validateOTP(otp: string): { isValid: boolean; message?: string } {\r\n  if (!otp) {\r\n    return { isValid: false, message: 'OTP is required' };\r\n  }\r\n  \r\n  if (otp.length !== 6) {\r\n    return { isValid: false, message: 'OTP must be 6 digits' };\r\n  }\r\n  \r\n  if (!/^\\d{6}$/.test(otp)) {\r\n    return { isValid: false, message: 'OTP must contain only numbers' };\r\n  }\r\n  \r\n  return { isValid: true };\r\n}\r\n\r\n// Send OTP to email using new secure API\r\nexport async function sendOTP(values: z.infer<typeof EmailOTPSchema>) {\r\n  const { email } = values;\r\n  const emailValidation = validateEmail(email);\r\n  if (!emailValidation.isValid) {\r\n    return {\r\n      success: false,\r\n      error: emailValidation.message,\r\n    };\r\n  }\r\n\r\n  try {\r\n    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');\r\n    const response = await fetch(`${baseUrl}/api/auth/send-otp`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({ email }),\r\n    });\r\n\r\n    const data = await response.json();\r\n\r\n    if (!response.ok) {\r\n      // Check if this is a configuration error (email rate limit)\r\n      if ('isConfigurationError' in data && data.isConfigurationError) {\r\n        return {\r\n          success: false,\r\n          error: data.error,\r\n          isConfigurationError: true,\r\n        };\r\n      }\r\n\r\n      return {\r\n        success: false,\r\n        error: data.error || 'Failed to send OTP',\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      message: data.message || \"OTP sent to your email address. Please check your inbox.\",\r\n    };\r\n  } catch (error) {\r\n    console.error('Send OTP error:', error);\r\n    return {\r\n      success: false,\r\n      error: 'An unexpected error occurred. Please try again.',\r\n    };\r\n  }\r\n}\r\n\r\n// Verify OTP and sign in using new secure API\r\nexport async function verifyOTP(values: z.infer<typeof VerifyOTPSchema>) {\r\n  const { email, otp } = values;\r\n  const otpValidation = validateOTP(otp);\r\n  if (!otpValidation.isValid) {\r\n    return {\r\n      success: false,\r\n      error: otpValidation.message,\r\n    };\r\n  }\r\n\r\n  try {\r\n    const deviceInfo = {\r\n      deviceName: `Web Browser - ${new Date().toISOString().slice(0, 10)}`,\r\n      platform: 'web' as const,\r\n    };\r\n\r\n    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');\r\n    const response = await fetch(`${baseUrl}/api/auth/verify-otp`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        email,\r\n        otp,\r\n        deviceInfo,\r\n      }),\r\n    });\r\n\r\n    const data = await response.json();\r\n\r\n    if (!response.ok) {\r\n      return {\r\n        success: false,\r\n        error: data.error || 'OTP verification failed',\r\n      };\r\n    }\r\n\r\n    // Return the API response data including tokens\r\n    return {\r\n      success: true,\r\n      data: {\r\n        accessToken: data.accessToken,\r\n        refreshToken: data.refreshToken,\r\n        deviceId: data.deviceId,\r\n        deviceSecret: data.deviceSecret,\r\n        hmacKey: data.hmacKey,\r\n        // Legacy format for compatibility (will be removed later)\r\n        user: { id: 'temp-user-id' },\r\n        session: { access_token: data.accessToken }\r\n      },\r\n      message: data.message || \"Successfully signed in!\",\r\n    };\r\n  } catch (error) {\r\n    console.error('OTP verification error:', error);\r\n    return {\r\n      success: false,\r\n      error: 'An unexpected error occurred. Please try again.',\r\n    };\r\n  }\r\n}\r\n\r\n// Mobile + password login using secure API endpoint\r\nexport async function loginWithMobilePassword(values: z.infer<typeof MobilePasswordLoginSchema>) {\r\n  const validatedFields = MobilePasswordLoginSchema.safeParse(values);\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      success: false,\r\n      error: \"Invalid mobile number or password format\",\r\n    };\r\n  }\r\n\r\n  const { mobile, password } = validatedFields.data;\r\n\r\n  try {\r\n    // Format mobile number with +91 prefix as email for our API\r\n    const phoneNumber = `+91${mobile}`;\r\n    \r\n    const deviceInfo = {\r\n      deviceName: `Web Browser - ${new Date().toISOString().slice(0, 10)}`,\r\n      platform: 'web' as const,\r\n    };\r\n\r\n    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');\r\n    const response = await fetch(`${baseUrl}/api/auth/login`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        email: phoneNumber, // Use phone as email\r\n        password: password,\r\n        deviceInfo,\r\n      }),\r\n    });\r\n\r\n    const data = await response.json();\r\n\r\n    if (!response.ok) {\r\n      return {\r\n        success: false,\r\n        error: data.error || 'Login failed',\r\n      };\r\n    }\r\n\r\n    // Return the API response data including tokens\r\n    return {\r\n      success: true,\r\n      data: {\r\n        accessToken: data.accessToken,\r\n        refreshToken: data.refreshToken,\r\n        deviceId: data.deviceId,\r\n        deviceSecret: data.deviceSecret,\r\n        hmacKey: data.hmacKey,\r\n        // Legacy format for compatibility (will be removed later)\r\n        user: { id: 'temp-user-id' },\r\n        session: { access_token: data.accessToken }\r\n      },\r\n      message: \"Successfully signed in!\",\r\n    };\r\n  } catch (error) {\r\n    console.error('Mobile login error:', error);\r\n    return {\r\n      success: false,\r\n      error: 'An unexpected error occurred. Please try again.',\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAqCsB,UAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/login/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { z } from \"zod\";\r\nimport { EmailOTPSchema, VerifyOTPSchema, MobilePasswordLoginSchema } from \"@/lib/schemas/authSchemas\";\r\n\r\n// Validate email format\r\nfunction validateEmail(email: string): { isValid: boolean; message?: string } {\r\n  if (!email) {\r\n    return { isValid: false, message: 'Email is required' };\r\n  }\r\n  \r\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n  if (!emailRegex.test(email)) {\r\n    return { isValid: false, message: 'Please enter a valid email address' };\r\n  }\r\n  \r\n  return { isValid: true };\r\n}\r\n\r\n// Validate OTP format\r\nfunction validateOTP(otp: string): { isValid: boolean; message?: string } {\r\n  if (!otp) {\r\n    return { isValid: false, message: 'OTP is required' };\r\n  }\r\n  \r\n  if (otp.length !== 6) {\r\n    return { isValid: false, message: 'OTP must be 6 digits' };\r\n  }\r\n  \r\n  if (!/^\\d{6}$/.test(otp)) {\r\n    return { isValid: false, message: 'OTP must contain only numbers' };\r\n  }\r\n  \r\n  return { isValid: true };\r\n}\r\n\r\n// Send OTP to email using new secure API\r\nexport async function sendOTP(values: z.infer<typeof EmailOTPSchema>) {\r\n  const { email } = values;\r\n  const emailValidation = validateEmail(email);\r\n  if (!emailValidation.isValid) {\r\n    return {\r\n      success: false,\r\n      error: emailValidation.message,\r\n    };\r\n  }\r\n\r\n  try {\r\n    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');\r\n    const response = await fetch(`${baseUrl}/api/auth/send-otp`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({ email }),\r\n    });\r\n\r\n    const data = await response.json();\r\n\r\n    if (!response.ok) {\r\n      // Check if this is a configuration error (email rate limit)\r\n      if ('isConfigurationError' in data && data.isConfigurationError) {\r\n        return {\r\n          success: false,\r\n          error: data.error,\r\n          isConfigurationError: true,\r\n        };\r\n      }\r\n\r\n      return {\r\n        success: false,\r\n        error: data.error || 'Failed to send OTP',\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      message: data.message || \"OTP sent to your email address. Please check your inbox.\",\r\n    };\r\n  } catch (error) {\r\n    console.error('Send OTP error:', error);\r\n    return {\r\n      success: false,\r\n      error: 'An unexpected error occurred. Please try again.',\r\n    };\r\n  }\r\n}\r\n\r\n// Verify OTP and sign in using new secure API\r\nexport async function verifyOTP(values: z.infer<typeof VerifyOTPSchema>) {\r\n  const { email, otp } = values;\r\n  const otpValidation = validateOTP(otp);\r\n  if (!otpValidation.isValid) {\r\n    return {\r\n      success: false,\r\n      error: otpValidation.message,\r\n    };\r\n  }\r\n\r\n  try {\r\n    const deviceInfo = {\r\n      deviceName: `Web Browser - ${new Date().toISOString().slice(0, 10)}`,\r\n      platform: 'web' as const,\r\n    };\r\n\r\n    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');\r\n    const response = await fetch(`${baseUrl}/api/auth/verify-otp`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        email,\r\n        otp,\r\n        deviceInfo,\r\n      }),\r\n    });\r\n\r\n    const data = await response.json();\r\n\r\n    if (!response.ok) {\r\n      return {\r\n        success: false,\r\n        error: data.error || 'OTP verification failed',\r\n      };\r\n    }\r\n\r\n    // Return the API response data including tokens\r\n    return {\r\n      success: true,\r\n      data: {\r\n        accessToken: data.accessToken,\r\n        refreshToken: data.refreshToken,\r\n        deviceId: data.deviceId,\r\n        deviceSecret: data.deviceSecret,\r\n        hmacKey: data.hmacKey,\r\n        // Legacy format for compatibility (will be removed later)\r\n        user: { id: 'temp-user-id' },\r\n        session: { access_token: data.accessToken }\r\n      },\r\n      message: data.message || \"Successfully signed in!\",\r\n    };\r\n  } catch (error) {\r\n    console.error('OTP verification error:', error);\r\n    return {\r\n      success: false,\r\n      error: 'An unexpected error occurred. Please try again.',\r\n    };\r\n  }\r\n}\r\n\r\n// Mobile + password login using secure API endpoint\r\nexport async function loginWithMobilePassword(values: z.infer<typeof MobilePasswordLoginSchema>) {\r\n  const validatedFields = MobilePasswordLoginSchema.safeParse(values);\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      success: false,\r\n      error: \"Invalid mobile number or password format\",\r\n    };\r\n  }\r\n\r\n  const { mobile, password } = validatedFields.data;\r\n\r\n  try {\r\n    // Format mobile number with +91 prefix as email for our API\r\n    const phoneNumber = `+91${mobile}`;\r\n    \r\n    const deviceInfo = {\r\n      deviceName: `Web Browser - ${new Date().toISOString().slice(0, 10)}`,\r\n      platform: 'web' as const,\r\n    };\r\n\r\n    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');\r\n    const response = await fetch(`${baseUrl}/api/auth/login`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        email: phoneNumber, // Use phone as email\r\n        password: password,\r\n        deviceInfo,\r\n      }),\r\n    });\r\n\r\n    const data = await response.json();\r\n\r\n    if (!response.ok) {\r\n      return {\r\n        success: false,\r\n        error: data.error || 'Login failed',\r\n      };\r\n    }\r\n\r\n    // Return the API response data including tokens\r\n    return {\r\n      success: true,\r\n      data: {\r\n        accessToken: data.accessToken,\r\n        refreshToken: data.refreshToken,\r\n        deviceId: data.deviceId,\r\n        deviceSecret: data.deviceSecret,\r\n        hmacKey: data.hmacKey,\r\n        // Legacy format for compatibility (will be removed later)\r\n        user: { id: 'temp-user-id' },\r\n        session: { access_token: data.accessToken }\r\n      },\r\n      message: \"Successfully signed in!\",\r\n    };\r\n  } catch (error) {\r\n    console.error('Mobile login error:', error);\r\n    return {\r\n      success: false,\r\n      error: 'An unexpected error occurred. Please try again.',\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAyFsB,YAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/form.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport {\r\n  Controller,\r\n  FormProvider,\r\n  useFormContext,\r\n  useFormState,\r\n  type ControllerProps,\r\n  type FieldPath,\r\n  type FieldValues,\r\n} from \"react-hook-form\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Label } from \"@/components/ui/label\"\r\n\r\nconst Form = FormProvider\r\n\r\ntype FormFieldContextValue<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n> = {\r\n  name: TName\r\n}\r\n\r\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\r\n  {} as FormFieldContextValue\r\n)\r\n\r\nconst FormField = <\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n>({\r\n  ...props\r\n}: ControllerProps<TFieldValues, TName>) => {\r\n  return (\r\n    <FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>\r\n  )\r\n}\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext)\r\n  const itemContext = React.useContext(FormItemContext)\r\n  const { getFieldState } = useFormContext()\r\n  const formState = useFormState({ name: fieldContext.name })\r\n  const fieldState = getFieldState(fieldContext.name, formState)\r\n\r\n  if (!fieldContext) {\r\n    throw new Error(\"useFormField should be used within <FormField>\")\r\n  }\r\n\r\n  const { id } = itemContext\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState,\r\n  }\r\n}\r\n\r\ntype FormItemContextValue = {\r\n  id: string\r\n}\r\n\r\nconst FormItemContext = React.createContext<FormItemContextValue>(\r\n  {} as FormItemContextValue\r\n)\r\n\r\nfunction FormItem({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  const id = React.useId()\r\n\r\n  return (\r\n    <FormItemContext.Provider value={{ id }}>\r\n      <div\r\n        data-slot=\"form-item\"\r\n        className={cn(\"grid gap-2\", className)}\r\n        {...props}\r\n      />\r\n    </FormItemContext.Provider>\r\n  )\r\n}\r\n\r\nfunction FormLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  const { error, formItemId } = useFormField()\r\n\r\n  return (\r\n    <Label\r\n      data-slot=\"form-label\"\r\n      data-error={!!error}\r\n      className={cn(\"data-[error=true]:text-destructive\", className)}\r\n      htmlFor={formItemId}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\r\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\r\n\r\n  return (\r\n    <Slot\r\n      data-slot=\"form-control\"\r\n      id={formItemId}\r\n      aria-describedby={\r\n        !error\r\n          ? `${formDescriptionId}`\r\n          : `${formDescriptionId} ${formMessageId}`\r\n      }\r\n      aria-invalid={!!error}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormDescription({ className, ...props }: React.ComponentProps<\"p\">) {\r\n  const { formDescriptionId } = useFormField()\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-description\"\r\n      id={formDescriptionId}\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormMessage({ className, ...props }: React.ComponentProps<\"p\">) {\r\n  const { error, formMessageId } = useFormField()\r\n  const body = error ? String(error?.message ?? \"\") : props.children\r\n\r\n  if (!body) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-message\"\r\n      id={formMessageId}\r\n      className={cn(\"text-destructive text-sm\", className)}\r\n      {...props}\r\n    >\r\n      {body}\r\n    </p>\r\n  )\r\n}\r\n\r\nexport {\r\n  useFormField,\r\n  Form,\r\n  FormItem,\r\n  FormLabel,\r\n  FormControl,\r\n  FormDescription,\r\n  FormMessage,\r\n  FormField,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;AAhBA;;;;;;;AAkBA,MAAM,OAAO,8JAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACzC,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,8OAAC,8JAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,8JAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACxC,CAAC;AAGH,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAErB,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OAC8C;IACjD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,8OAAC,0HAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,GAAG,OAA0C;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,8OAAC,gKAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 336, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/input-otp.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { OTPInput, OTPInputContext } from \"input-otp\"\r\nimport { MinusIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction InputOTP({\r\n  className,\r\n  containerClassName,\r\n  ...props\r\n}: React.ComponentProps<typeof OTPInput> & {\r\n  containerClassName?: string\r\n}) {\r\n  return (\r\n    <OTPInput\r\n      data-slot=\"input-otp\"\r\n      containerClassName={cn(\r\n        \"flex items-center gap-2 has-disabled:opacity-50\",\r\n        containerClassName\r\n      )}\r\n      className={cn(\"disabled:cursor-not-allowed\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction InputOTPGroup({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"input-otp-group\"\r\n      className={cn(\"flex items-center\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction InputOTPSlot({\r\n  index,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  index: number\r\n}) {\r\n  const inputOTPContext = React.useContext(OTPInputContext)\r\n  const { char, hasFakeCaret, isActive } = inputOTPContext?.slots[index] ?? {}\r\n\r\n  return (\r\n    <div\r\n      data-slot=\"input-otp-slot\"\r\n      data-active={isActive}\r\n      className={cn(\r\n        \"data-[active=true]:border-ring data-[active=true]:ring-ring/50 data-[active=true]:aria-invalid:ring-destructive/20 dark:data-[active=true]:aria-invalid:ring-destructive/40 aria-invalid:border-destructive data-[active=true]:aria-invalid:border-destructive dark:bg-input/30 border-input relative flex h-9 w-9 items-center justify-center border-y border-r text-sm shadow-xs transition-all outline-none first:rounded-l-md first:border-l last:rounded-r-md data-[active=true]:z-10 data-[active=true]:ring-[3px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {char}\r\n      {hasFakeCaret && (\r\n        <div className=\"pointer-events-none absolute inset-0 flex items-center justify-center\">\r\n          <div className=\"animate-caret-blink bg-foreground h-4 w-px duration-1000\" />\r\n        </div>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction InputOTPSeparator({ ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div data-slot=\"input-otp-separator\" role=\"separator\" {...props}>\r\n      <MinusIcon />\r\n    </div>\r\n  )\r\n}\r\n\r\nexport { InputOTP, InputOTPGroup, InputOTPSlot, InputOTPSeparator }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,kBAAkB,EAClB,GAAG,OAGJ;IACC,qBACE,8OAAC,8IAAA,CAAA,WAAQ;QACP,aAAU;QACV,oBAAoB,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACnB,mDACA;QAEF,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,qBAAqB;QAClC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,KAAK,EACL,SAAS,EACT,GAAG,OAGJ;IACC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,8IAAA,CAAA,kBAAe;IACxD,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,GAAG,iBAAiB,KAAK,CAAC,MAAM,IAAI,CAAC;IAE3E,qBACE,8OAAC;QACC,aAAU;QACV,eAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4fACA;QAED,GAAG,KAAK;;YAER;YACA,8BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAKzB;AAEA,SAAS,kBAAkB,EAAE,GAAG,OAAoC;IAClE,qBACE,8OAAC;QAAI,aAAU;QAAsB,MAAK;QAAa,GAAG,KAAK;kBAC7D,cAAA,8OAAC,wMAAA,CAAA,YAAS;;;;;;;;;;AAGhB", "debugId": null}}, {"offset": {"line": 430, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/login/components/EmailOTPForm.tsx"], "sourcesContent": ["\"use client\"\nimport { zod<PERSON><PERSON>olver } from \"@hookform/resolvers/zod\"\nimport { useForm } from \"react-hook-form\"\nimport { z } from \"zod\"\nimport { Button } from \"@/components/ui/button\"\nimport {\n  Form,\n  FormControl,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage\n} from \"@/components/ui/form\"\nimport { Input } from \"@/components/ui/input\"\nimport {\n  InputOTP,\n  InputOTPGroup,\n  InputOTPSlot\n} from \"@/components/ui/input-otp\"\nimport { ArrowRight, Loader2 } from \"lucide-react\"\n// Email schema for step \nconst emailSchema = z.object({\n  email: z\n    .string()\n    .min(1, { message: \"Email is required\" })\n    .email({ message: \"Please enter a valid email address\" })\n})\n// OTP schema for step \nconst otpSchema = z.object({\n  otp: z\n    .string()\n    .min(6, { message: \"OTP must be 6 digits\" })\n    .max(6, { message: \"OTP must be 6 digits\" })\n    .regex(/^\\d{6}$/, { message: \"OTP must be 6 digits\" })\n})\ninterface EmailOTPFormProps {\n  step: 'email' | 'otp'\n  email: string\n  countdown: number\n  isPending: boolean\n  onEmailSubmit: (_values: z.infer<typeof emailSchema>) => void\n  onOTPSubmit: (_values: { email: string; otp: string }) => void\n  onResendOTP: () => void\n  onBackToEmail: () => void\n}\n\nexport function EmailOTPForm({\n  step,\n  email,\n  countdown,\n  isPending,\n  onEmailSubmit,\n  onOTPSubmit,\n  onResendOTP,\n  onBackToEmail,\n}: EmailOTPFormProps) {\n  const emailForm = useForm<z.infer<typeof emailSchema>>({\n    resolver: zodResolver(emailSchema),\n    defaultValues: {\n      email: \"\"\n    }\n  })\n  const otpForm = useForm<z.infer<typeof otpSchema>>({\n    resolver: zodResolver(otpSchema),\n    defaultValues: {\n      otp: \"\"\n    }\n  })\n  if (step === 'email') {\n    return (\n      <Form {...emailForm}>\n        <form\n          onSubmit={emailForm.handleSubmit(onEmailSubmit)}\n          className=\"space-y-4 sm:space-y-6\"\n        >\n          <FormField\n            control={emailForm.control}\n            name=\"email\"\n            render={({ field }) => (\n              <FormItem>\n                <FormLabel className=\"text-foreground text-sm sm:text-base\">\n                  Email Address\n                </FormLabel>\n                <FormControl>\n                  <Input\n                    id=\"email-login-field\"\n                    placeholder=\"<EMAIL>\"\n                    type=\"email\"\n                    {...field}\n                    className=\"bg-background border-border focus:border-primary dark:bg-neutral-800 dark:border-neutral-700 dark:focus:border-[var(--brand-gold)] h-10 sm:h-11 text-sm sm:text-base\"\n                  />\n                </FormControl>\n                <FormMessage />\n              </FormItem>\n            )}\n          />\n          <Button\n            type=\"submit\"\n            className=\"cursor-pointer w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 py-5 sm:py-6 rounded-lg sm:rounded-xl font-medium text-sm sm:text-base\"\n            disabled={isPending}\n          >\n            {isPending ? (\n              <>\n                <Loader2 className=\"w-5 h-5 mr-2 animate-spin\" />\n                Sending OTP...\n              </>\n            ) : (\n              <>\n                Continue <ArrowRight className=\"w-5 h-5 ml-2\" />\n              </>\n            )}\n          </Button>\n          <div className=\"text-center mt-4 text-xs sm:text-sm\">\n            <span className=\"text-muted-foreground\">\n              New to Dukancard? No worries! We&apos;ll create your account automatically.\n            </span>\n          </div>\n        </form>\n      </Form>\n    )\n  }\n  return (\n    <Form {...otpForm}>\n      <form\n        onSubmit={otpForm.handleSubmit((values) => {\n          onOTPSubmit({ email, otp: values.otp });\n        })}\n        className=\"space-y-4 sm:space-y-6\"\n      >\n        <div className=\"text-center mb-4\">\n          <p className=\"text-sm text-muted-foreground\">\n            We&apos;ve sent a 6-digit code to\n          </p>\n          <p className=\"text-sm font-medium text-foreground\">{email}</p>\n          <p className=\"text-xs text-muted-foreground mt-2\">\n            Code expires in 24 hours\n          </p>\n        </div>\n        <FormField\n          control={otpForm.control}\n          name=\"otp\"\n          render={({ field }) => (\n            <FormItem>\n              <FormLabel className=\"text-foreground text-sm sm:text-base text-center block\">\n                Enter Verification Code\n              </FormLabel>\n              <FormControl>\n                <div className=\"flex justify-center\">\n                  <InputOTP\n                    id=\"otp-login-field\"\n                    maxLength={6}\n                    value={field.value} // Ensure value is controlled\n                    onChange={(value) => field.onChange(value)} // Pass value directly\n                    className=\"gap-2\"\n                  >\n                    <InputOTPGroup>\n                      <InputOTPSlot\n                        index={0}\n                        className=\"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]\"\n                      />\n                      <InputOTPSlot\n                        index={1}\n                        className=\"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]\"\n                      />\n                      <InputOTPSlot\n                        index={2}\n                        className=\"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]\"\n                      />\n                      <InputOTPSlot\n                        index={3}\n                        className=\"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]\"\n                      />\n                      <InputOTPSlot\n                        index={4}\n                        className=\"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]\"\n                      />\n                      <InputOTPSlot\n                        index={5}\n                        className=\"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]\"\n                      />\n                    </InputOTPGroup>\n                  </InputOTP>\n                </div>\n              </FormControl>\n              <FormMessage />\n            </FormItem>\n          )}\n        />\n        <Button\n          type=\"submit\"\n          className=\"cursor-pointer w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 py-5 sm:py-6 rounded-lg sm:rounded-xl font-medium text-sm sm:text-base\"\n          disabled={isPending}\n        >\n          {isPending ? (\n            <>\n              <Loader2 className=\"w-5 h-5 mr-2 animate-spin\" />\n              Verifying...\n            </>\n          ) : (\n            <>\n              Verify & Sign In <ArrowRight className=\"w-5 h-5 ml-2\" />\n            </>\n          )}\n        </Button>\n        <div className=\"flex flex-col items-center space-y-3 mt-4 text-xs sm:text-sm\">\n          <button\n            type=\"button\"\n            onClick={onResendOTP}\n            disabled={countdown > 0}\n            className={`${\n              countdown > 0\n                ? \"text-muted-foreground cursor-not-allowed\"\n                : \"text-primary dark:text-[var(--brand-gold)] hover:underline cursor-pointer\"\n            }`}\n          >\n            {countdown > 0 ? `Resend OTP in ${countdown}s` : \"Resend OTP\"}\n          </button>\n          <button\n            type=\"button\"\n            onClick={onBackToEmail}\n            className=\"text-muted-foreground hover:text-foreground cursor-pointer\"\n          >\n            ← Change email address\n          </button>\n        </div>\n      </form>\n    </Form>\n  )\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;AAKA;AAAA;AAnBA;;;;;;;;;;AAoBA,yBAAyB;AACzB,MAAM,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3B,OAAO,oIAAA,CAAA,IAAC,CACL,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoB,GACtC,KAAK,CAAC;QAAE,SAAS;IAAqC;AAC3D;AACA,uBAAuB;AACvB,MAAM,YAAY,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzB,KAAK,oIAAA,CAAA,IAAC,CACH,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuB,GACzC,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuB,GACzC,KAAK,CAAC,WAAW;QAAE,SAAS;IAAuB;AACxD;AAYO,SAAS,aAAa,EAC3B,IAAI,EACJ,KAAK,EACL,SAAS,EACT,SAAS,EACT,aAAa,EACb,WAAW,EACX,WAAW,EACX,aAAa,EACK;IAClB,MAAM,YAAY,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAA+B;QACrD,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,OAAO;QACT;IACF;IACA,MAAM,UAAU,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAA6B;QACjD,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,KAAK;QACP;IACF;IACA,IAAI,SAAS,SAAS;QACpB,qBACE,8OAAC,yHAAA,CAAA,OAAI;YAAE,GAAG,SAAS;sBACjB,cAAA,8OAAC;gBACC,UAAU,UAAU,YAAY,CAAC;gBACjC,WAAU;;kCAEV,8OAAC,yHAAA,CAAA,YAAS;wBACR,SAAS,UAAU,OAAO;wBAC1B,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;;kDACP,8OAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAuC;;;;;;kDAG5D,8OAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,0HAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,aAAY;4CACZ,MAAK;4CACJ,GAAG,KAAK;4CACT,WAAU;;;;;;;;;;;kDAGd,8OAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kCAIlB,8OAAC,2HAAA,CAAA,SAAM;wBACL,MAAK;wBACL,WAAU;wBACV,UAAU;kCAET,0BACC;;8CACE,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAA8B;;yDAInD;;gCAAE;8CACS,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;kCAIrC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;IAOlD;IACA,qBACE,8OAAC,yHAAA,CAAA,OAAI;QAAE,GAAG,OAAO;kBACf,cAAA,8OAAC;YACC,UAAU,QAAQ,YAAY,CAAC,CAAC;gBAC9B,YAAY;oBAAE;oBAAO,KAAK,OAAO,GAAG;gBAAC;YACvC;YACA,WAAU;;8BAEV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;sCAG7C,8OAAC;4BAAE,WAAU;sCAAuC;;;;;;sCACpD,8OAAC;4BAAE,WAAU;sCAAqC;;;;;;;;;;;;8BAIpD,8OAAC,yHAAA,CAAA,YAAS;oBACR,SAAS,QAAQ,OAAO;oBACxB,MAAK;oBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;;8CACP,8OAAC,yHAAA,CAAA,YAAS;oCAAC,WAAU;8CAAyD;;;;;;8CAG9E,8OAAC,yHAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,iIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,WAAW;4CACX,OAAO,MAAM,KAAK;4CAClB,UAAU,CAAC,QAAU,MAAM,QAAQ,CAAC;4CACpC,WAAU;sDAEV,cAAA,8OAAC,iIAAA,CAAA,gBAAa;;kEACZ,8OAAC,iIAAA,CAAA,eAAY;wDACX,OAAO;wDACP,WAAU;;;;;;kEAEZ,8OAAC,iIAAA,CAAA,eAAY;wDACX,OAAO;wDACP,WAAU;;;;;;kEAEZ,8OAAC,iIAAA,CAAA,eAAY;wDACX,OAAO;wDACP,WAAU;;;;;;kEAEZ,8OAAC,iIAAA,CAAA,eAAY;wDACX,OAAO;wDACP,WAAU;;;;;;kEAEZ,8OAAC,iIAAA,CAAA,eAAY;wDACX,OAAO;wDACP,WAAU;;;;;;kEAEZ,8OAAC,iIAAA,CAAA,eAAY;wDACX,OAAO;wDACP,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAMpB,8OAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8BAIlB,8OAAC,2HAAA,CAAA,SAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;8BAET,0BACC;;0CACE,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAA8B;;qDAInD;;4BAAE;0CACiB,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;8BAI7C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,MAAK;4BACL,SAAS;4BACT,UAAU,YAAY;4BACtB,WAAW,GACT,YAAY,IACR,6CACA,6EACJ;sCAED,YAAY,IAAI,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,GAAG;;;;;;sCAEnD,8OAAC;4BACC,MAAK;4BACL,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 829, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/schemas/authSchemas.ts"], "sourcesContent": ["import { z } from \"zod\";\n\nexport const IndianMobileSchema = z\n  .string()\n  .trim()\n  .min(10, { message: \"Mobile number must be 10 digits\" })\n  .max(10, { message: \"Mobile number must be 10 digits\" })\n  .regex(/^[6-9]\\d{9}$/, { message: \"Please enter a valid Indian mobile number\" });\n\nexport const EmailOTPSchema = z.object({\n  email: z\n    .string()\n    .trim()\n    .min(1, { message: \"Email is required\" })\n    .email({ message: \"Please enter a valid email address\" }),\n});\n\nexport const VerifyOTPSchema = z.object({\n  email: z\n    .string()\n    .trim()\n    .min(1, { message: \"Email is required\" })\n    .email({ message: \"Please enter a valid email address\" }),\n  otp: z\n    .string()\n    .trim()\n    .min(6, { message: \"OTP must be 6 digits\" })\n    .max(6, { message: \"OTP must be 6 digits\" })\n    .regex(/^\\d{6}$/, { message: \"OTP must be 6 digits\" }),\n});\n\nexport const PasswordComplexitySchema = z\n  .string()\n  .min(6, \"Password must be at least 6 characters long\")\n  .regex(/[A-Z]/, \"Password must contain at least one uppercase letter\")\n  .regex(/[a-z]/, \"Password must contain at least one lowercase letter\")\n  .regex(/\\d/, \"Password must contain at least one number\")\n  .regex(/[^a-zA-Z0-9]/, \"Password must contain at least one special character\");\n\nexport const MobilePasswordLoginSchema = z.object({\n  mobile: IndianMobileSchema,\n  password: z.string().trim().min(1, { message: \"Password is required\" }),\n});"], "names": [], "mappings": ";;;;;;;AAAA;;AAEO,MAAM,qBAAqB,oIAAA,CAAA,IAAC,CAChC,MAAM,GACN,IAAI,GACJ,GAAG,CAAC,IAAI;IAAE,SAAS;AAAkC,GACrD,GAAG,CAAC,IAAI;IAAE,SAAS;AAAkC,GACrD,KAAK,CAAC,gBAAgB;IAAE,SAAS;AAA4C;AAEzE,MAAM,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,OAAO,oIAAA,CAAA,IAAC,CACL,MAAM,GACN,IAAI,GACJ,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoB,GACtC,KAAK,CAAC;QAAE,SAAS;IAAqC;AAC3D;AAEO,MAAM,kBAAkB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,OAAO,oIAAA,CAAA,IAAC,CACL,MAAM,GACN,IAAI,GACJ,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoB,GACtC,KAAK,CAAC;QAAE,SAAS;IAAqC;IACzD,KAAK,oIAAA,CAAA,IAAC,CACH,MAAM,GACN,IAAI,GACJ,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuB,GACzC,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuB,GACzC,KAAK,CAAC,WAAW;QAAE,SAAS;IAAuB;AACxD;AAEO,MAAM,2BAA2B,oIAAA,CAAA,IAAC,CACtC,MAAM,GACN,GAAG,CAAC,GAAG,+CACP,KAAK,CAAC,SAAS,uDACf,KAAK,CAAC,SAAS,uDACf,KAAK,CAAC,MAAM,6CACZ,KAAK,CAAC,gBAAgB;AAElB,MAAM,4BAA4B,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChD,QAAQ;IACR,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuB;AACvE", "debugId": null}}, {"offset": {"line": 879, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/login/components/MobilePasswordForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport { z } from \"zod\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { MobilePasswordLoginSchema } from \"@/lib/schemas/authSchemas\";\nimport { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from \"@/components/ui/form\";\nimport { Input } from \"@/components/ui/input\";\nimport { Button } from \"@/components/ui/button\";\nimport { Loader2, ArrowRight } from \"lucide-react\";\nimport Link from \"next/link\";\n\ninterface MobilePasswordFormProps {\n  isPending: boolean;\n  onSubmit: (_values: z.infer<typeof MobilePasswordLoginSchema>) => void;\n}\n\nexport function MobilePasswordForm({ isPending, onSubmit }: MobilePasswordFormProps) {\n  const form = useForm<z.infer<typeof MobilePasswordLoginSchema>>({\n    resolver: zodResolver(MobilePasswordLoginSchema),\n    defaultValues: {\n      mobile: \"\",\n      password: \"\",\n    },\n  });\n\n  return (\n    <Form {...form}>\n      <form\n        onSubmit={form.handleSubmit(onSubmit)}\n        className=\"space-y-4 sm:space-y-6\"\n      >\n        <FormField\n          control={form.control}\n          name=\"mobile\"\n          render={({ field }) => (\n            <FormItem>\n              <FormLabel className=\"text-foreground text-sm sm:text-base\">\n                Mobile Number\n              </FormLabel>\n              <FormControl>\n                <div className=\"relative\">\n                  <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-sm text-muted-foreground\">\n                    +91\n                  </div>\n                  <Input\n                    placeholder=\"9876543210\"\n                    type=\"tel\"\n                    {...field}\n                    onChange={(e) => {\n                      let value = e.target.value;\n                      // Remove any +91 prefix if user enters it\n                      value = value.replace(/^\\+91/, '');\n                      // Only allow numeric input\n                      value = value.replace(/\\D/g, '');\n                      // Limit to 10 digits for mobile numbers\n                      if (value.length > 10) {\n                        value = value.slice(0, 10);\n                      }\n                      field.onChange(value);\n                    }}\n                    onKeyDown={(e) => {\n                      const isNumeric = /^[0-9]$/.test(e.key);\n                      const isControl = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(e.key);\n                      if (!isNumeric && !isControl) {\n                        e.preventDefault();\n                      }\n                    }}\n                    className=\"pl-12 bg-background border-border focus:border-primary dark:bg-neutral-800 dark:border-neutral-700 dark:focus:border-[var(--brand-gold)] h-10 sm:h-11 text-sm sm:text-base\"\n                    maxLength={10}\n                  />\n                </div>\n              </FormControl>\n              <FormMessage />\n            </FormItem>\n          )}\n        />\n\n        <FormField\n          control={form.control}\n          name=\"password\"\n          render={({ field }) => (\n            <FormItem>\n              <FormLabel className=\"text-foreground text-sm sm:text-base\">\n                Password\n              </FormLabel>\n              <FormControl>\n                <Input\n                  placeholder=\"••••••••\"\n                  type=\"password\"\n                  {...field}\n                  className=\"bg-background border-border focus:border-primary dark:bg-neutral-800 dark:border-neutral-700 dark:focus:border-[var(--brand-gold)] h-10 sm:h-11 text-sm sm:text-base\"\n                />\n              </FormControl>\n              <FormMessage />\n            </FormItem>\n          )}\n        />\n\n        <Button\n          type=\"submit\"\n          className=\"cursor-pointer w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 py-5 sm:py-6 rounded-lg sm:rounded-xl font-medium text-sm sm:text-base\"\n          disabled={isPending}\n        >\n          {isPending ? (\n            <>\n              <Loader2 className=\"w-5 h-5 mr-2 animate-spin\" />\n              Signing in...\n            </>\n          ) : (\n            <>\n              Sign In <ArrowRight className=\"w-5 h-5 ml-2\" />\n            </>\n          )}\n        </Button>\n\n        <div className=\"text-center mt-4 text-xs sm:text-sm\">\n          <span className=\"text-muted-foreground\">\n            Don&apos;t have an account?{\" \"}\n            <Link\n              href=\"/register\"\n              className=\"text-primary dark:text-[var(--brand-gold)] hover:underline font-medium\"\n            >\n              Register here\n            </Link>\n          </span>\n        </div>\n      </form>\n    </Form>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAVA;;;;;;;;;;AAiBO,SAAS,mBAAmB,EAAE,SAAS,EAAE,QAAQ,EAA2B;IACjF,MAAM,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAA6C;QAC9D,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE,6HAAA,CAAA,4BAAyB;QAC/C,eAAe;YACb,QAAQ;YACR,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC,yHAAA,CAAA,OAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,UAAU,KAAK,YAAY,CAAC;YAC5B,WAAU;;8BAEV,8OAAC,yHAAA,CAAA,YAAS;oBACR,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;;8CACP,8OAAC,yHAAA,CAAA,YAAS;oCAAC,WAAU;8CAAuC;;;;;;8CAG5D,8OAAC,yHAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAmF;;;;;;0DAGlG,8OAAC,0HAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,MAAK;gDACJ,GAAG,KAAK;gDACT,UAAU,CAAC;oDACT,IAAI,QAAQ,EAAE,MAAM,CAAC,KAAK;oDAC1B,0CAA0C;oDAC1C,QAAQ,MAAM,OAAO,CAAC,SAAS;oDAC/B,2BAA2B;oDAC3B,QAAQ,MAAM,OAAO,CAAC,OAAO;oDAC7B,wCAAwC;oDACxC,IAAI,MAAM,MAAM,GAAG,IAAI;wDACrB,QAAQ,MAAM,KAAK,CAAC,GAAG;oDACzB;oDACA,MAAM,QAAQ,CAAC;gDACjB;gDACA,WAAW,CAAC;oDACV,MAAM,YAAY,UAAU,IAAI,CAAC,EAAE,GAAG;oDACtC,MAAM,YAAY;wDAAC;wDAAa;wDAAU;wDAAa;wDAAc;qDAAM,CAAC,QAAQ,CAAC,EAAE,GAAG;oDAC1F,IAAI,CAAC,aAAa,CAAC,WAAW;wDAC5B,EAAE,cAAc;oDAClB;gDACF;gDACA,WAAU;gDACV,WAAW;;;;;;;;;;;;;;;;;8CAIjB,8OAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8BAKlB,8OAAC,yHAAA,CAAA,YAAS;oBACR,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;;8CACP,8OAAC,yHAAA,CAAA,YAAS;oCAAC,WAAU;8CAAuC;;;;;;8CAG5D,8OAAC,yHAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,0HAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,MAAK;wCACJ,GAAG,KAAK;wCACT,WAAU;;;;;;;;;;;8CAGd,8OAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8BAKlB,8OAAC,2HAAA,CAAA,SAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;8BAET,0BACC;;0CACE,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAA8B;;qDAInD;;4BAAE;0CACQ,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;8BAKpC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,WAAU;;4BAAwB;4BACV;0CAC5B,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 1126, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/login/components/AuthMethodToggle.tsx"], "sourcesContent": ["\"use client\";\r\n\r\ninterface AuthMethodToggleProps {\r\n  authMethod: 'email-otp' | 'mobile-password';\r\n  step: 'email' | 'otp';\r\n  onMethodChange: (_method: 'email-otp' | 'mobile-password') => void;\r\n}\r\n\r\nexport function AuthMethodToggle({ authMethod, step, onMethodChange }: AuthMethodToggleProps) {\r\n  // Only show toggle when on email step or mobile-password method\r\n  if (!(authMethod === 'email-otp' && step === 'email') && authMethod !== 'mobile-password') {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex rounded-lg bg-muted p-1 mb-6\">\r\n      <button\r\n        type=\"button\"\r\n        onClick={() => onMethodChange('email-otp')}\r\n        className={`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ${\r\n          authMethod === 'email-otp'\r\n            ? 'bg-background text-foreground shadow-sm'\r\n            : 'text-muted-foreground hover:text-foreground'\r\n        }`}\r\n      >\r\n        Email OTP\r\n      </button>\r\n      <button\r\n        type=\"button\"\r\n        onClick={() => onMethodChange('mobile-password')}\r\n        className={`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ${\r\n          authMethod === 'mobile-password'\r\n            ? 'bg-background text-foreground shadow-sm'\r\n            : 'text-muted-foreground hover:text-foreground'\r\n        }`}\r\n      >\r\n        Mobile + Password\r\n      </button>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;AAQO,SAAS,iBAAiB,EAAE,UAAU,EAAE,IAAI,EAAE,cAAc,EAAyB;IAC1F,gEAAgE;IAChE,IAAI,CAAC,CAAC,eAAe,eAAe,SAAS,OAAO,KAAK,eAAe,mBAAmB;QACzF,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,MAAK;gBACL,SAAS,IAAM,eAAe;gBAC9B,WAAW,CAAC,kEAAkE,EAC5E,eAAe,cACX,4CACA,+CACJ;0BACH;;;;;;0BAGD,8OAAC;gBACC,MAAK;gBACL,SAAS,IAAM,eAAe;gBAC9B,WAAW,CAAC,kEAAkE,EAC5E,eAAe,oBACX,4CACA,+CACJ;0BACH;;;;;;;;;;;;AAKP", "debugId": null}}, {"offset": {"line": 1173, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/login/components/SocialLoginButton.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Loader2 } from \"lucide-react\";\r\nimport { toast } from \"sonner\";\r\nimport { createClient } from \"@/utils/supabase/client\";\r\n\r\ninterface SocialLoginButtonProps {\r\n  redirectSlug?: string | null;\r\n  message?: string | null;\r\n  disabled?: boolean;\r\n}\r\n\r\nexport function SocialLoginButton({ redirectSlug, message, disabled }: SocialLoginButtonProps) {\r\n  const [isSocialLoading, setIsSocialLoading] = useState<\"google\" | null>(null);\r\n\r\n  // Initiates the Supabase OAuth flow for social login.\r\n  // This function gets the authorization URL and opens it in a new tab.\r\n  // After successful authentication, Supabase will redirect to /auth/callback,\r\n  // where the unified post-login redirection logic is executed.\r\n  async function handleSocialLogin(provider: \"google\") {\r\n    try {\r\n      setIsSocialLoading(provider);\r\n      const supabase = createClient();\r\n\r\n      // Construct the callback URL with the redirect and message parameters if available\r\n      // Add closeWindow=true to indicate this window should close after auth\r\n      let callbackUrl = `${window.location.origin}/auth/callback?closeWindow=true`;\r\n\r\n      // Add redirect parameter if available\r\n      if (redirectSlug) {\r\n        callbackUrl += `&redirect=${encodeURIComponent(redirectSlug)}`;\r\n      }\r\n\r\n      // Add message parameter if available\r\n      if (message) {\r\n        callbackUrl += `&message=${encodeURIComponent(message)}`;\r\n      }\r\n\r\n      // Get the authorization URL but don't redirect automatically\r\n      const { data, error } = await supabase.auth.signInWithOAuth({\r\n        provider,\r\n        options: {\r\n          redirectTo: callbackUrl,\r\n          skipBrowserRedirect: true, // Don't redirect automatically\r\n          queryParams: {\r\n            // Add access_type=offline to request a refresh token\r\n            access_type: \"offline\",\r\n            // Prompt user to select account to avoid auto-selection issues\r\n            prompt: \"select_account\",\r\n          },\r\n        },\r\n      });\r\n\r\n      if (error) {\r\n        toast.error(\"Login failed\", {\r\n          description: error.message,\r\n        });\r\n        setIsSocialLoading(null);\r\n        return;\r\n      }\r\n\r\n      // If we have the URL, open it in a new tab\r\n      if (data?.url) {\r\n        // Open the authorization URL in a new tab\r\n        window.open(data.url, \"_blank\");\r\n\r\n        // Show a toast to guide the user\r\n        toast.info(\"Google sign-in opened in a new tab\", {\r\n          description: \"Please complete the sign-in process in the new tab.\",\r\n          duration: 5000,\r\n        });\r\n\r\n        // Reset loading state after a short delay\r\n        setTimeout(() => {\r\n          setIsSocialLoading(null);\r\n        }, 1000);\r\n      } else {\r\n        toast.error(\"Failed to start Google sign-in\", {\r\n          description: \"Please try again or use email login.\",\r\n        });\r\n        setIsSocialLoading(null);\r\n      }\r\n    } catch (error: unknown) {\r\n      const errorMessage =\r\n        error instanceof Error\r\n          ? error.message\r\n          : \"An unexpected error occurred. Please try again.\";\r\n      toast.error(\"Login failed\", {\r\n        description: errorMessage,\r\n      });\r\n      setIsSocialLoading(null);\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex justify-center mb-5 sm:mb-6\">\r\n      <Button\r\n        variant=\"outline\"\r\n        className=\"cursor-pointer bg-background hover:bg-muted border-border dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:border-neutral-800 w-full py-5 sm:py-6 rounded-lg sm:rounded-xl font-medium text-foreground\"\r\n        onClick={() => handleSocialLogin(\"google\")}\r\n        disabled={!!isSocialLoading || disabled}\r\n      >\r\n        {isSocialLoading === \"google\" ? (\r\n          <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\r\n        ) : (\r\n          <>\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              viewBox=\"0 0 24 24\"\r\n              className=\"w-4 h-4 mr-2\"\r\n            >\r\n              <path\r\n                fill=\"#4285F4\"\r\n                d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\r\n              />\r\n              <path\r\n                fill=\"#34A853\"\r\n                d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\r\n              />\r\n              <path\r\n                fill=\"#FBBC05\"\r\n                d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\r\n              />\r\n              <path\r\n                fill=\"#EA4335\"\r\n                d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\r\n              />\r\n            </svg>\r\n            Login with Google\r\n          </>\r\n        )}\r\n      </Button>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAcO,SAAS,kBAAkB,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,EAA0B;IAC3F,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAExE,sDAAsD;IACtD,sEAAsE;IACtE,6EAA6E;IAC7E,8DAA8D;IAC9D,eAAe,kBAAkB,QAAkB;QACjD,IAAI;YACF,mBAAmB;YACnB,MAAM,WAAW,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;YAE5B,mFAAmF;YACnF,uEAAuE;YACvE,IAAI,cAAc,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,+BAA+B,CAAC;YAE5E,sCAAsC;YACtC,IAAI,cAAc;gBAChB,eAAe,CAAC,UAAU,EAAE,mBAAmB,eAAe;YAChE;YAEA,qCAAqC;YACrC,IAAI,SAAS;gBACX,eAAe,CAAC,SAAS,EAAE,mBAAmB,UAAU;YAC1D;YAEA,6DAA6D;YAC7D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,eAAe,CAAC;gBAC1D;gBACA,SAAS;oBACP,YAAY;oBACZ,qBAAqB;oBACrB,aAAa;wBACX,qDAAqD;wBACrD,aAAa;wBACb,+DAA+D;wBAC/D,QAAQ;oBACV;gBACF;YACF;YAEA,IAAI,OAAO;gBACT,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gBAAgB;oBAC1B,aAAa,MAAM,OAAO;gBAC5B;gBACA,mBAAmB;gBACnB;YACF;YAEA,2CAA2C;YAC3C,IAAI,MAAM,KAAK;gBACb,0CAA0C;gBAC1C,OAAO,IAAI,CAAC,KAAK,GAAG,EAAE;gBAEtB,iCAAiC;gBACjC,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC,sCAAsC;oBAC/C,aAAa;oBACb,UAAU;gBACZ;gBAEA,0CAA0C;gBAC1C,WAAW;oBACT,mBAAmB;gBACrB,GAAG;YACL,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,kCAAkC;oBAC5C,aAAa;gBACf;gBACA,mBAAmB;YACrB;QACF,EAAE,OAAO,OAAgB;YACvB,MAAM,eACJ,iBAAiB,QACb,MAAM,OAAO,GACb;YACN,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gBAAgB;gBAC1B,aAAa;YACf;YACA,mBAAmB;QACrB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;YACL,SAAQ;YACR,WAAU;YACV,SAAS,IAAM,kBAAkB;YACjC,UAAU,CAAC,CAAC,mBAAmB;sBAE9B,oBAAoB,yBACnB,8OAAC,iNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;qCAEnB;;kCACE,8OAAC;wBACC,OAAM;wBACN,SAAQ;wBACR,WAAU;;0CAEV,8OAAC;gCACC,MAAK;gCACL,GAAE;;;;;;0CAEJ,8OAAC;gCACC,MAAK;gCACL,GAAE;;;;;;0CAEJ,8OAAC;gCACC,MAAK;gCACL,GAAE;;;;;;0CAEJ,8OAAC;gCACC,MAAK;gCACL,GAAE;;;;;;;;;;;;oBAEA;;;;;;;;;;;;;AAOlB", "debugId": null}}, {"offset": {"line": 1336, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/redirectAfterLogin.ts"], "sourcesContent": ["/**\r\n * Shared utility to determine the correct post-login redirect path for a user.\r\n * Checks both customer_profiles and business_profiles, and returns the appropriate dashboard, onboarding, or choose-role path.\r\n * Returns \"/\" as a fallback in case of errors.\r\n */\r\n\r\nimport { SupabaseClient } from \"@supabase/supabase-js\";\r\n\r\nexport async function getPostLoginRedirectPath(\r\n  supabase: SupabaseClient,\r\n  userId: string\r\n): Promise<string> {\r\n  try {\r\n    // Check both profiles concurrently\r\n    const [customerRes, businessRes] = await Promise.all([\r\n      supabase\r\n        .from(\"customer_profiles\")\r\n        .select(\"id\")\r\n        .eq(\"id\", userId),\r\n      supabase\r\n        .from(\"business_profiles\")\r\n        .select(\"id, business_slug\")\r\n        .eq(\"id\", userId),\r\n    ]);\r\n\r\n    if (customerRes.error || businessRes.error) {\r\n      // Query error, fallback to choose-role for new users, only fallback to \"/\" for critical errors\r\n      console.error(\"[redirectAfterLogin] Supabase query error:\", customerRes.error, businessRes.error);\r\n      // If both errors are \"no rows found\" or similar, treat as new user\r\n      if (\r\n        customerRes.error?.code === \"PGRST116\" || // PostgREST \"No rows found\"\r\n        businessRes.error?.code === \"PGRST116\" ||\r\n        customerRes.error?.message?.toLowerCase().includes(\"no rows\") ||\r\n        businessRes.error?.message?.toLowerCase().includes(\"no rows\")\r\n      ) {\r\n        return \"/choose-role\";\r\n      }\r\n      return \"/?view=home\";\r\n    }\r\n\r\n    if (customerRes.data && Array.isArray(customerRes.data) && customerRes.data.length > 0) {\r\n      // Customer profile exists\r\n      return \"/dashboard/customer\";\r\n    }\r\n\r\n    if (businessRes.data && Array.isArray(businessRes.data) && businessRes.data.length > 0) {\r\n      // Business profile exists\r\n      const businessProfile = businessRes.data[0];\r\n      if (businessProfile.business_slug) {\r\n        // Onboarding complete\r\n        return \"/dashboard/business\";\r\n      } else {\r\n        // Onboarding needed\r\n        return \"/onboarding\";\r\n      }\r\n    }\r\n\r\n    // No profile exists yet, needs role selection\r\n    return \"/choose-role\";\r\n  } catch (err) {\r\n    console.error(\"[redirectAfterLogin] Unexpected error:\", err);\r\n    // Unexpected error, fallback to home\r\n    return \"/?view=home\";\r\n  }\r\n}"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAIM,eAAe,yBACpB,QAAwB,EACxB,MAAc;IAEd,IAAI;QACF,mCAAmC;QACnC,MAAM,CAAC,aAAa,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;YACnD,SACG,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM;YACZ,SACG,IAAI,CAAC,qBACL,MAAM,CAAC,qBACP,EAAE,CAAC,MAAM;SACb;QAED,IAAI,YAAY,KAAK,IAAI,YAAY,KAAK,EAAE;YAC1C,+FAA+F;YAC/F,QAAQ,KAAK,CAAC,8CAA8C,YAAY,KAAK,EAAE,YAAY,KAAK;YAChG,mEAAmE;YACnE,IACE,YAAY,KAAK,EAAE,SAAS,cAAc,4BAA4B;YACtE,YAAY,KAAK,EAAE,SAAS,cAC5B,YAAY,KAAK,EAAE,SAAS,cAAc,SAAS,cACnD,YAAY,KAAK,EAAE,SAAS,cAAc,SAAS,YACnD;gBACA,OAAO;YACT;YACA,OAAO;QACT;QAEA,IAAI,YAAY,IAAI,IAAI,MAAM,OAAO,CAAC,YAAY,IAAI,KAAK,YAAY,IAAI,CAAC,MAAM,GAAG,GAAG;YACtF,0BAA0B;YAC1B,OAAO;QACT;QAEA,IAAI,YAAY,IAAI,IAAI,MAAM,OAAO,CAAC,YAAY,IAAI,KAAK,YAAY,IAAI,CAAC,MAAM,GAAG,GAAG;YACtF,0BAA0B;YAC1B,MAAM,kBAAkB,YAAY,IAAI,CAAC,EAAE;YAC3C,IAAI,gBAAgB,aAAa,EAAE;gBACjC,sBAAsB;gBACtB,OAAO;YACT,OAAO;gBACL,oBAAoB;gBACpB,OAAO;YACT;QACF;QAEA,8CAA8C;QAC9C,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,0CAA0C;QACxD,qCAAqC;QACrC,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1389, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/stores/authStore.ts"], "sourcesContent": ["import { create } from 'zustand';\r\nimport { devtools, persist } from 'zustand/middleware';\r\nimport { immer } from 'zustand/middleware/immer';\r\n\r\n// Types for authentication state\r\ninterface AuthTokens {\r\n  accessToken: string;\r\n  refreshToken: string;\r\n  deviceId: string;\r\n  deviceSecret: string;\r\n  hmacKey: string;\r\n}\r\n\r\ninterface AuthUser {\r\n  id: string;\r\n  email?: string;\r\n  phone?: string;\r\n  // Add other user properties as needed\r\n}\r\n\r\ninterface AuthState {\r\n  // Authentication state\r\n  isAuthenticated: boolean;\r\n  isLoading: boolean;\r\n  user: AuthUser | null;\r\n  tokens: AuthTokens | null;\r\n  \r\n  // Error states\r\n  error: string | null;\r\n  isRefreshing: boolean;\r\n  \r\n  // Actions\r\n  login: (email: string, password: string, deviceInfo: any) => Promise<{ success: boolean; error?: string }>;\r\n  logout: () => Promise<void>;\r\n  refreshTokens: () => Promise<boolean>;\r\n  setTokens: (tokens: AuthTokens) => void;\r\n  setUser: (user: AuthUser | null) => void;\r\n  clearAuth: () => void;\r\n  clearError: () => void;\r\n  \r\n  // Utility functions\r\n  isTokenValid: () => boolean;\r\n  shouldRefreshToken: () => boolean;\r\n  getAuthHeaders: () => Record<string, string>;\r\n  getHMACHeaders: (method: string, path: string, body?: string) => Record<string, string>;\r\n}\r\n\r\n// API wrapper for authenticated requests\r\nexport class AuthenticatedApiClient {\r\n  private static instance: AuthenticatedApiClient;\r\n  private refreshPromise: Promise<boolean> | null = null;\r\n  \r\n  static getInstance(): AuthenticatedApiClient {\r\n    if (!AuthenticatedApiClient.instance) {\r\n      AuthenticatedApiClient.instance = new AuthenticatedApiClient();\r\n    }\r\n    return AuthenticatedApiClient.instance;\r\n  }\r\n  \r\n  async fetch(url: string, options: RequestInit = {}): Promise<Response> {\r\n    const store = useAuthStore.getState();\r\n\r\n    // Proactively refresh token if needed (expires within 5 minutes)\r\n    if (store.shouldRefreshToken() && !store.isRefreshing && !this.refreshPromise) {\r\n      this.refreshPromise = store.refreshTokens();\r\n      await this.refreshPromise;\r\n      this.refreshPromise = null;\r\n    }\r\n\r\n    // Get updated store state after potential refresh\r\n    const updatedStore = useAuthStore.getState();\r\n\r\n    // Extract method and path for HMAC signing\r\n    const method = options.method || 'GET';\r\n    const urlObj = new URL(url);\r\n    const path = urlObj.pathname;\r\n    const body = options.body ? (typeof options.body === 'string' ? options.body : JSON.stringify(options.body)) : '';\r\n\r\n    // Add authorization and HMAC headers\r\n    const headers = {\r\n      ...options.headers,\r\n      ...updatedStore.getAuthHeaders(),\r\n      ...updatedStore.getHMACHeaders(method, path, body),\r\n    };\r\n\r\n    let response = await fetch(url, {\r\n      ...options,\r\n      headers,\r\n    });\r\n    \r\n    // If unauthorized and we have tokens, try to refresh\r\n    if (response.status === 401 && updatedStore.tokens) {\r\n      // If there's already a refresh in progress, wait for it\r\n      if (this.refreshPromise) {\r\n        await this.refreshPromise;\r\n      } else if (!updatedStore.isRefreshing) {\r\n        // Start a new refresh\r\n        this.refreshPromise = updatedStore.refreshTokens();\r\n        const refreshSuccess = await this.refreshPromise;\r\n        this.refreshPromise = null;\r\n        \r\n        if (!refreshSuccess) {\r\n          // Refresh failed, return the 401 response\r\n          return response;\r\n        }\r\n      }\r\n      \r\n      // Get the updated store state and retry the request\r\n      const finalStore = useAuthStore.getState();\r\n      if (finalStore.tokens) {\r\n        const newHeaders = {\r\n          ...options.headers,\r\n          ...finalStore.getAuthHeaders(),\r\n        };\r\n        \r\n        response = await fetch(url, {\r\n          ...options,\r\n          headers: newHeaders,\r\n        });\r\n      }\r\n    }\r\n    \r\n    return response;\r\n  }\r\n}\r\n\r\n// Helper function to decode JWT payload (basic decode, not for verification)\r\nfunction decodeJWTPayload(token: string): any {\r\n  try {\r\n    const parts = token.split('.');\r\n    if (parts.length !== 3) return null;\r\n    \r\n    const payload = parts[1];\r\n    // Add padding if needed for base64 decoding\r\n    const paddedPayload = payload + '='.repeat((4 - payload.length % 4) % 4);\r\n    const decoded = atob(paddedPayload.replace(/-/g, '+').replace(/_/g, '/'));\r\n    return JSON.parse(decoded);\r\n  } catch (error) {\r\n    console.error('Error decoding JWT:', error);\r\n    return null;\r\n  }\r\n}\r\n\r\nexport const useAuthStore = create<AuthState>()(\r\n  devtools(\r\n    persist(\r\n      immer((set: (fn: (draft: AuthState) => void) => void, get: () => AuthState) => ({\r\n        // Initial state\r\n        isAuthenticated: false,\r\n        isLoading: false,\r\n        user: null,\r\n        tokens: null,\r\n        error: null,\r\n        isRefreshing: false,\r\n\r\n        // Login action\r\n        login: async (email: string, password: string, deviceInfo: any) => {\r\n          set((state: AuthState) => {\r\n            state.isLoading = true;\r\n            state.error = null;\r\n          });\r\n\r\n          try {\r\n            const response = await fetch('/api/auth/login', {\r\n              method: 'POST',\r\n              headers: {\r\n                'Content-Type': 'application/json',\r\n              },\r\n              body: JSON.stringify({\r\n                email,\r\n                password,\r\n                deviceInfo,\r\n              }),\r\n            });\r\n\r\n            const data = await response.json();\r\n\r\n            if (!response.ok) {\r\n              set((state: AuthState) => {\r\n                state.isLoading = false;\r\n                state.error = data.error || 'Login failed';\r\n              });\r\n              \r\n              return { success: false, error: data.error || 'Login failed' };\r\n            }\r\n\r\n            // Extract user info from access token\r\n            const payload = decodeJWTPayload(data.accessToken);\r\n            const user: AuthUser = {\r\n              id: payload?.user_id || '',\r\n              email: email,\r\n              // Add phone if it's a phone-based login\r\n              phone: email.startsWith('+91') ? email : undefined,\r\n            };\r\n\r\n            const tokens: AuthTokens = {\r\n              accessToken: data.accessToken,\r\n              refreshToken: data.refreshToken,\r\n              deviceId: data.deviceId,\r\n              deviceSecret: data.deviceSecret,\r\n              hmacKey: data.hmacKey,\r\n            };\r\n\r\n            set((state: AuthState) => {\r\n              state.isAuthenticated = true;\r\n              state.isLoading = false;\r\n              state.user = user;\r\n              state.tokens = tokens;\r\n              state.error = null;\r\n            });\r\n\r\n            return { success: true };\r\n          } catch (error) {\r\n            console.error('Login error:', error);\r\n            set((state: AuthState) => {\r\n              state.isLoading = false;\r\n              state.error = 'An unexpected error occurred';\r\n            });\r\n            \r\n            return { success: false, error: 'An unexpected error occurred' };\r\n          }\r\n        },\r\n\r\n        // Logout action\r\n        logout: async () => {\r\n          const currentTokens = get().tokens;\r\n          \r\n          if (currentTokens) {\r\n            try {\r\n              // Call logout API\r\n              const response = await fetch('/api/auth/logout', {\r\n                method: 'POST',\r\n                headers: {\r\n                  'Content-Type': 'application/json',\r\n                  'Authorization': `Bearer ${currentTokens.accessToken}`,\r\n                },\r\n                body: JSON.stringify({\r\n                  refreshToken: currentTokens.refreshToken,\r\n                }),\r\n              });\r\n\r\n              // Continue with logout even if API call fails\r\n              if (!response.ok) {\r\n                console.warn('Logout API call failed, but continuing with local logout');\r\n              }\r\n            } catch (error) {\r\n              console.warn('Error calling logout API:', error);\r\n            }\r\n          }\r\n\r\n          set((state: AuthState) => {\r\n            state.isAuthenticated = false;\r\n            state.user = null;\r\n            state.tokens = null;\r\n            state.error = null;\r\n            state.isRefreshing = false;\r\n          });\r\n\r\n          // Clear access token cookie\r\n          if (typeof document !== 'undefined') {\r\n            document.cookie = 'accessToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; secure; samesite=strict';\r\n          }\r\n        },\r\n\r\n        // Refresh tokens action\r\n        refreshTokens: async () => {\r\n          const currentTokens = get().tokens;\r\n          \r\n          if (!currentTokens || get().isRefreshing) {\r\n            return false;\r\n          }\r\n\r\n          set((state: AuthState) => {\r\n            state.isRefreshing = true;\r\n          });\r\n\r\n          try {\r\n            const response = await fetch('/api/auth/refresh', {\r\n              method: 'POST',\r\n              headers: {\r\n                'Content-Type': 'application/json',\r\n              },\r\n              body: JSON.stringify({\r\n                refreshToken: currentTokens.refreshToken,\r\n                deviceId: currentTokens.deviceId,\r\n              }),\r\n            });\r\n\r\n            const data = await response.json();\r\n\r\n            if (!response.ok) {\r\n              // Refresh failed, clear authentication\r\n              set((state: AuthState) => {\r\n                state.isAuthenticated = false;\r\n                state.user = null;\r\n                state.tokens = null;\r\n                state.isRefreshing = false;\r\n                state.error = 'Session expired. Please log in again.';\r\n              });\r\n              \r\n              return false;\r\n            }\r\n\r\n            // Update tokens\r\n            set((state: AuthState) => {\r\n              if (state.tokens) {\r\n                state.tokens.accessToken = data.accessToken;\r\n                state.tokens.refreshToken = data.refreshToken;\r\n              }\r\n              state.isRefreshing = false;\r\n            });\r\n\r\n            // Update access token cookie\r\n            if (typeof document !== 'undefined') {\r\n              document.cookie = `accessToken=${data.accessToken}; path=/; max-age=${15 * 60}; secure; samesite=strict`;\r\n            }\r\n\r\n            return true;\r\n          } catch (error) {\r\n            console.error('Token refresh error:', error);\r\n            set((state: AuthState) => {\r\n              state.isAuthenticated = false;\r\n              state.user = null;\r\n              state.tokens = null;\r\n              state.isRefreshing = false;\r\n              state.error = 'Session expired. Please log in again.';\r\n            });\r\n            \r\n            return false;\r\n          }\r\n        },\r\n\r\n        // Set tokens (for external use)\r\n        setTokens: (tokens: AuthTokens) => {\r\n          set((state: AuthState) => {\r\n            state.tokens = tokens;\r\n            state.isAuthenticated = true;\r\n          });\r\n          \r\n          // For web client session persistence, store access token in cookie\r\n          // This allows the middleware to validate the session\r\n          if (typeof document !== 'undefined') {\r\n            document.cookie = `accessToken=${tokens.accessToken}; path=/; max-age=${15 * 60}; secure; samesite=strict`;\r\n          }\r\n        },\r\n\r\n        // Set user (for external use)\r\n        setUser: (user: AuthUser | null) => {\r\n          set((state: AuthState) => {\r\n            state.user = user;\r\n          });\r\n        },\r\n\r\n        // Clear authentication\r\n        clearAuth: () => {\r\n          set((state: AuthState) => {\r\n            state.isAuthenticated = false;\r\n            state.user = null;\r\n            state.tokens = null;\r\n            state.error = null;\r\n            state.isRefreshing = false;\r\n          });\r\n\r\n          // Clear access token cookie\r\n          if (typeof document !== 'undefined') {\r\n            document.cookie = 'accessToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; secure; samesite=strict';\r\n          }\r\n        },\r\n\r\n        // Clear error\r\n        clearError: () => {\r\n          set((state: AuthState) => {\r\n            state.error = null;\r\n          });\r\n        },\r\n\r\n        // Check if current token is valid (not expired)\r\n        isTokenValid: () => {\r\n          const tokens = get().tokens;\r\n          if (!tokens?.accessToken) return false;\r\n\r\n          const payload = decodeJWTPayload(tokens.accessToken);\r\n          if (!payload || !payload.exp) return false;\r\n\r\n          const now = Math.floor(Date.now() / 1000);\r\n          return payload.exp > now;\r\n        },\r\n\r\n        // Check if token needs refresh (expires within 5 minutes)\r\n        shouldRefreshToken: () => {\r\n          const tokens = get().tokens;\r\n          if (!tokens?.accessToken) return false;\r\n\r\n          const payload = decodeJWTPayload(tokens.accessToken);\r\n          if (!payload || !payload.exp) return false;\r\n\r\n          const now = Math.floor(Date.now() / 1000);\r\n          const fiveMinutesFromNow = now + (5 * 60); // 5 minutes in seconds\r\n          return payload.exp < fiveMinutesFromNow;\r\n        },\r\n\r\n        // Get auth headers for API requests\r\n        getAuthHeaders: (): Record<string, string> => {\r\n          const tokens = get().tokens;\r\n          if (!tokens?.accessToken) return {};\r\n\r\n          return {\r\n            'Authorization': `Bearer ${tokens.accessToken}`,\r\n          };\r\n        },\r\n\r\n        // Get HMAC headers for API requests\r\n        getHMACHeaders: (method: string, path: string, body: string = ''): Record<string, string> => {\r\n          const tokens = get().tokens;\r\n          if (!tokens?.deviceId || !tokens?.hmacKey) return {};\r\n\r\n          const timestamp = Date.now().toString();\r\n\r\n          // Import crypto for browser environment\r\n          if (typeof window !== 'undefined') {\r\n            // For browser environment, we'll need to use Web Crypto API or a polyfill\r\n            // For now, return empty headers and we'll implement this properly\r\n            console.warn('HMAC signing not yet implemented for browser environment');\r\n            return {\r\n              'X-Device-Id': tokens.deviceId,\r\n              'X-Timestamp': timestamp,\r\n              'X-Signature': 'browser-not-implemented', // TODO: Implement browser-compatible HMAC signing\r\n            };\r\n          }\r\n\r\n          // For server-side rendering, we can use Node.js crypto\r\n          try {\r\n            const crypto = require('crypto');\r\n\r\n            // Create SHA256 hash of the request body\r\n            const bodyHash = crypto\r\n              .createHash('sha256')\r\n              .update(body || '')\r\n              .digest('hex');\r\n\r\n            // Create the string to be signed: method + path + timestamp + bodyHash\r\n            const stringToSign = `${method.toUpperCase()}${path}${timestamp}${bodyHash}`;\r\n\r\n            // Generate HMAC-SHA256 signature using HMAC key\r\n            const signature = crypto\r\n              .createHmac('sha256', tokens.hmacKey)\r\n              .update(stringToSign)\r\n              .digest('base64');\r\n\r\n            return {\r\n              'X-Device-Id': tokens.deviceId,\r\n              'X-Timestamp': timestamp,\r\n              'X-Signature': signature,\r\n            };\r\n          } catch (error) {\r\n            console.error('Error generating HMAC signature:', error);\r\n            return {};\r\n          }\r\n        },\r\n      })),\r\n      {\r\n        name: 'auth-store',\r\n        partialize: (state: AuthState) => ({\r\n          // Persist authentication state but not loading states\r\n          isAuthenticated: state.isAuthenticated,\r\n          user: state.user,\r\n          tokens: state.tokens,\r\n          // Don't persist error, isLoading, or isRefreshing\r\n        }),\r\n      }\r\n    ),\r\n    {\r\n      name: 'auth-store',\r\n    }\r\n  )\r\n);\r\n\r\n// Helper hook for authenticated API calls\r\nexport const useAuthenticatedApi = () => {\r\n  return AuthenticatedApiClient.getInstance();\r\n};"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AA8CO,MAAM;IACX,OAAe,SAAiC;IACxC,iBAA0C,KAAK;IAEvD,OAAO,cAAsC;QAC3C,IAAI,CAAC,uBAAuB,QAAQ,EAAE;YACpC,uBAAuB,QAAQ,GAAG,IAAI;QACxC;QACA,OAAO,uBAAuB,QAAQ;IACxC;IAEA,MAAM,MAAM,GAAW,EAAE,UAAuB,CAAC,CAAC,EAAqB;QACrE,MAAM,QAAQ,aAAa,QAAQ;QAEnC,iEAAiE;QACjE,IAAI,MAAM,kBAAkB,MAAM,CAAC,MAAM,YAAY,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YAC7E,IAAI,CAAC,cAAc,GAAG,MAAM,aAAa;YACzC,MAAM,IAAI,CAAC,cAAc;YACzB,IAAI,CAAC,cAAc,GAAG;QACxB;QAEA,kDAAkD;QAClD,MAAM,eAAe,aAAa,QAAQ;QAE1C,2CAA2C;QAC3C,MAAM,SAAS,QAAQ,MAAM,IAAI;QACjC,MAAM,SAAS,IAAI,IAAI;QACvB,MAAM,OAAO,OAAO,QAAQ;QAC5B,MAAM,OAAO,QAAQ,IAAI,GAAI,OAAO,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,GAAG,KAAK,SAAS,CAAC,QAAQ,IAAI,IAAK;QAE/G,qCAAqC;QACrC,MAAM,UAAU;YACd,GAAG,QAAQ,OAAO;YAClB,GAAG,aAAa,cAAc,EAAE;YAChC,GAAG,aAAa,cAAc,CAAC,QAAQ,MAAM,KAAK;QACpD;QAEA,IAAI,WAAW,MAAM,MAAM,KAAK;YAC9B,GAAG,OAAO;YACV;QACF;QAEA,qDAAqD;QACrD,IAAI,SAAS,MAAM,KAAK,OAAO,aAAa,MAAM,EAAE;YAClD,wDAAwD;YACxD,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvB,MAAM,IAAI,CAAC,cAAc;YAC3B,OAAO,IAAI,CAAC,aAAa,YAAY,EAAE;gBACrC,sBAAsB;gBACtB,IAAI,CAAC,cAAc,GAAG,aAAa,aAAa;gBAChD,MAAM,iBAAiB,MAAM,IAAI,CAAC,cAAc;gBAChD,IAAI,CAAC,cAAc,GAAG;gBAEtB,IAAI,CAAC,gBAAgB;oBACnB,0CAA0C;oBAC1C,OAAO;gBACT;YACF;YAEA,oDAAoD;YACpD,MAAM,aAAa,aAAa,QAAQ;YACxC,IAAI,WAAW,MAAM,EAAE;gBACrB,MAAM,aAAa;oBACjB,GAAG,QAAQ,OAAO;oBAClB,GAAG,WAAW,cAAc,EAAE;gBAChC;gBAEA,WAAW,MAAM,MAAM,KAAK;oBAC1B,GAAG,OAAO;oBACV,SAAS;gBACX;YACF;QACF;QAEA,OAAO;IACT;AACF;AAEA,6EAA6E;AAC7E,SAAS,iBAAiB,KAAa;IACrC,IAAI;QACF,MAAM,QAAQ,MAAM,KAAK,CAAC;QAC1B,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;QAE/B,MAAM,UAAU,KAAK,CAAC,EAAE;QACxB,4CAA4C;QAC5C,MAAM,gBAAgB,UAAU,IAAI,MAAM,CAAC,CAAC,IAAI,QAAQ,MAAM,GAAG,CAAC,IAAI;QACtE,MAAM,UAAU,KAAK,cAAc,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM;QACpE,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;IACT;AACF;AAEO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAA,GAAA,sJAAA,CAAA,QAAK,AAAD,EAAE,CAAC,KAA+C,MAAyB,CAAC;QAC9E,gBAAgB;QAChB,iBAAiB;QACjB,WAAW;QACX,MAAM;QACN,QAAQ;QACR,OAAO;QACP,cAAc;QAEd,eAAe;QACf,OAAO,OAAO,OAAe,UAAkB;YAC7C,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG;gBAClB,MAAM,KAAK,GAAG;YAChB;YAEA,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;oBAC9C,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB;wBACA;wBACA;oBACF;gBACF;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,IAAI,CAAC;wBACH,MAAM,SAAS,GAAG;wBAClB,MAAM,KAAK,GAAG,KAAK,KAAK,IAAI;oBAC9B;oBAEA,OAAO;wBAAE,SAAS;wBAAO,OAAO,KAAK,KAAK,IAAI;oBAAe;gBAC/D;gBAEA,sCAAsC;gBACtC,MAAM,UAAU,iBAAiB,KAAK,WAAW;gBACjD,MAAM,OAAiB;oBACrB,IAAI,SAAS,WAAW;oBACxB,OAAO;oBACP,wCAAwC;oBACxC,OAAO,MAAM,UAAU,CAAC,SAAS,QAAQ;gBAC3C;gBAEA,MAAM,SAAqB;oBACzB,aAAa,KAAK,WAAW;oBAC7B,cAAc,KAAK,YAAY;oBAC/B,UAAU,KAAK,QAAQ;oBACvB,cAAc,KAAK,YAAY;oBAC/B,SAAS,KAAK,OAAO;gBACvB;gBAEA,IAAI,CAAC;oBACH,MAAM,eAAe,GAAG;oBACxB,MAAM,SAAS,GAAG;oBAClB,MAAM,IAAI,GAAG;oBACb,MAAM,MAAM,GAAG;oBACf,MAAM,KAAK,GAAG;gBAChB;gBAEA,OAAO;oBAAE,SAAS;gBAAK;YACzB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gBAAgB;gBAC9B,IAAI,CAAC;oBACH,MAAM,SAAS,GAAG;oBAClB,MAAM,KAAK,GAAG;gBAChB;gBAEA,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAA+B;YACjE;QACF;QAEA,gBAAgB;QAChB,QAAQ;YACN,MAAM,gBAAgB,MAAM,MAAM;YAElC,IAAI,eAAe;gBACjB,IAAI;oBACF,kBAAkB;oBAClB,MAAM,WAAW,MAAM,MAAM,oBAAoB;wBAC/C,QAAQ;wBACR,SAAS;4BACP,gBAAgB;4BAChB,iBAAiB,CAAC,OAAO,EAAE,cAAc,WAAW,EAAE;wBACxD;wBACA,MAAM,KAAK,SAAS,CAAC;4BACnB,cAAc,cAAc,YAAY;wBAC1C;oBACF;oBAEA,8CAA8C;oBAC9C,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,QAAQ,IAAI,CAAC;oBACf;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,IAAI,CAAC,6BAA6B;gBAC5C;YACF;YAEA,IAAI,CAAC;gBACH,MAAM,eAAe,GAAG;gBACxB,MAAM,IAAI,GAAG;gBACb,MAAM,MAAM,GAAG;gBACf,MAAM,KAAK,GAAG;gBACd,MAAM,YAAY,GAAG;YACvB;YAEA,4BAA4B;YAC5B,IAAI,OAAO,aAAa,aAAa;gBACnC,SAAS,MAAM,GAAG;YACpB;QACF;QAEA,wBAAwB;QACxB,eAAe;YACb,MAAM,gBAAgB,MAAM,MAAM;YAElC,IAAI,CAAC,iBAAiB,MAAM,YAAY,EAAE;gBACxC,OAAO;YACT;YAEA,IAAI,CAAC;gBACH,MAAM,YAAY,GAAG;YACvB;YAEA,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;oBAChD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,cAAc,cAAc,YAAY;wBACxC,UAAU,cAAc,QAAQ;oBAClC;gBACF;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,uCAAuC;oBACvC,IAAI,CAAC;wBACH,MAAM,eAAe,GAAG;wBACxB,MAAM,IAAI,GAAG;wBACb,MAAM,MAAM,GAAG;wBACf,MAAM,YAAY,GAAG;wBACrB,MAAM,KAAK,GAAG;oBAChB;oBAEA,OAAO;gBACT;gBAEA,gBAAgB;gBAChB,IAAI,CAAC;oBACH,IAAI,MAAM,MAAM,EAAE;wBAChB,MAAM,MAAM,CAAC,WAAW,GAAG,KAAK,WAAW;wBAC3C,MAAM,MAAM,CAAC,YAAY,GAAG,KAAK,YAAY;oBAC/C;oBACA,MAAM,YAAY,GAAG;gBACvB;gBAEA,6BAA6B;gBAC7B,IAAI,OAAO,aAAa,aAAa;oBACnC,SAAS,MAAM,GAAG,CAAC,YAAY,EAAE,KAAK,WAAW,CAAC,kBAAkB,EAAE,KAAK,GAAG,yBAAyB,CAAC;gBAC1G;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,IAAI,CAAC;oBACH,MAAM,eAAe,GAAG;oBACxB,MAAM,IAAI,GAAG;oBACb,MAAM,MAAM,GAAG;oBACf,MAAM,YAAY,GAAG;oBACrB,MAAM,KAAK,GAAG;gBAChB;gBAEA,OAAO;YACT;QACF;QAEA,gCAAgC;QAChC,WAAW,CAAC;YACV,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG;gBACf,MAAM,eAAe,GAAG;YAC1B;YAEA,mEAAmE;YACnE,qDAAqD;YACrD,IAAI,OAAO,aAAa,aAAa;gBACnC,SAAS,MAAM,GAAG,CAAC,YAAY,EAAE,OAAO,WAAW,CAAC,kBAAkB,EAAE,KAAK,GAAG,yBAAyB,CAAC;YAC5G;QACF;QAEA,8BAA8B;QAC9B,SAAS,CAAC;YACR,IAAI,CAAC;gBACH,MAAM,IAAI,GAAG;YACf;QACF;QAEA,uBAAuB;QACvB,WAAW;YACT,IAAI,CAAC;gBACH,MAAM,eAAe,GAAG;gBACxB,MAAM,IAAI,GAAG;gBACb,MAAM,MAAM,GAAG;gBACf,MAAM,KAAK,GAAG;gBACd,MAAM,YAAY,GAAG;YACvB;YAEA,4BAA4B;YAC5B,IAAI,OAAO,aAAa,aAAa;gBACnC,SAAS,MAAM,GAAG;YACpB;QACF;QAEA,cAAc;QACd,YAAY;YACV,IAAI,CAAC;gBACH,MAAM,KAAK,GAAG;YAChB;QACF;QAEA,gDAAgD;QAChD,cAAc;YACZ,MAAM,SAAS,MAAM,MAAM;YAC3B,IAAI,CAAC,QAAQ,aAAa,OAAO;YAEjC,MAAM,UAAU,iBAAiB,OAAO,WAAW;YACnD,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG,EAAE,OAAO;YAErC,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;YACpC,OAAO,QAAQ,GAAG,GAAG;QACvB;QAEA,0DAA0D;QAC1D,oBAAoB;YAClB,MAAM,SAAS,MAAM,MAAM;YAC3B,IAAI,CAAC,QAAQ,aAAa,OAAO;YAEjC,MAAM,UAAU,iBAAiB,OAAO,WAAW;YACnD,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG,EAAE,OAAO;YAErC,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;YACpC,MAAM,qBAAqB,MAAO,IAAI,IAAK,uBAAuB;YAClE,OAAO,QAAQ,GAAG,GAAG;QACvB;QAEA,oCAAoC;QACpC,gBAAgB;YACd,MAAM,SAAS,MAAM,MAAM;YAC3B,IAAI,CAAC,QAAQ,aAAa,OAAO,CAAC;YAElC,OAAO;gBACL,iBAAiB,CAAC,OAAO,EAAE,OAAO,WAAW,EAAE;YACjD;QACF;QAEA,oCAAoC;QACpC,gBAAgB,CAAC,QAAgB,MAAc,OAAe,EAAE;YAC9D,MAAM,SAAS,MAAM,MAAM;YAC3B,IAAI,CAAC,QAAQ,YAAY,CAAC,QAAQ,SAAS,OAAO,CAAC;YAEnD,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ;YAErC,wCAAwC;YACxC,uCAAmC;;YASnC;YAEA,uDAAuD;YACvD,IAAI;gBACF,MAAM;gBAEN,yCAAyC;gBACzC,MAAM,WAAW,OACd,UAAU,CAAC,UACX,MAAM,CAAC,QAAQ,IACf,MAAM,CAAC;gBAEV,uEAAuE;gBACvE,MAAM,eAAe,GAAG,OAAO,WAAW,KAAK,OAAO,YAAY,UAAU;gBAE5E,gDAAgD;gBAChD,MAAM,YAAY,OACf,UAAU,CAAC,UAAU,OAAO,OAAO,EACnC,MAAM,CAAC,cACP,MAAM,CAAC;gBAEV,OAAO;oBACL,eAAe,OAAO,QAAQ;oBAC9B,eAAe;oBACf,eAAe;gBACjB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,OAAO,CAAC;YACV;QACF;IACF,CAAC,IACD;IACE,MAAM;IACN,YAAY,CAAC,QAAqB,CAAC;YACjC,sDAAsD;YACtD,iBAAiB,MAAM,eAAe;YACtC,MAAM,MAAM,IAAI;YAChB,QAAQ,MAAM,MAAM;QAEtB,CAAC;AACH,IAEF;IACE,MAAM;AACR;AAKG,MAAM,sBAAsB;IACjC,OAAO,uBAAuB,WAAW;AAC3C", "debugId": null}}, {"offset": {"line": 1760, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/login/LoginForm.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useRouter, useSearch<PERSON>arams } from \"next/navigation\";\r\nimport { useState, useEffect, useTransition } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { Card } from \"@/components/ui/card\";\r\nimport { toast } from \"sonner\";\r\nimport { sendOTP, verifyOTP } from \"./actions\";\r\nimport { EmailOTPForm } from \"./components/EmailOTPForm\";\r\nimport { MobilePasswordForm } from \"./components/MobilePasswordForm\";\r\nimport { AuthMethodToggle } from \"./components/AuthMethodToggle\";\r\nimport { SocialLoginButton } from \"./components/SocialLoginButton\";\r\nimport { createClient } from \"@/utils/supabase/client\";\r\nimport { getPostLoginRedirectPath } from \"@/lib/actions/redirectAfterLogin\";\r\nimport { useAuthStore } from \"@/lib/stores/authStore\";\r\n\r\n// Helper function to decode JWT payload (basic decode, not for verification)\r\nfunction decodeJWTPayload(token: string | undefined): any {\r\n  if (!token) return null;\r\n  \r\n  try {\r\n    const parts = token.split('.');\r\n    if (parts.length !== 3) return null;\r\n    \r\n    const payload = parts[1];\r\n    // Add padding if needed for base64 decoding\r\n    const paddedPayload = payload + '='.repeat((4 - payload.length % 4) % 4);\r\n    const decoded = atob(paddedPayload.replace(/-/g, '+').replace(/_/g, '/'));\r\n    return JSON.parse(decoded);\r\n  } catch (error) {\r\n    console.error('Error decoding JWT:', error);\r\n    return null;\r\n  }\r\n}\r\n\r\nexport function LoginForm() {\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n  const [isPending, startTransition] = useTransition();\r\n  const [redirectSlug, setRedirectSlug] = useState<string | null>(null);\r\n  const [message, setMessage] = useState<string | null>(null);\r\n  const [authMethod, setAuthMethod] = useState<'email-otp' | 'mobile-password'>('email-otp');\r\n  const [step, setStep] = useState<'email' | 'otp'>('email');\r\n  const [email, setEmail] = useState<string>('');\r\n  const [countdown, setCountdown] = useState<number>(0);\r\n\r\n  // Auth store hooks\r\n  const { login, isLoading: authLoading, error: authError, clearError } = useAuthStore();\r\n\r\n  // Get the redirect and message parameters from the URL\r\n  useEffect(() => {\r\n    const redirect = searchParams.get(\"redirect\");\r\n    if (redirect) {\r\n      setRedirectSlug(redirect);\r\n    }\r\n\r\n    const messageParam = searchParams.get(\"message\");\r\n    if (messageParam) {\r\n      setMessage(messageParam);\r\n    }\r\n  }, [searchParams]);\r\n\r\n  // Countdown timer for resend OTP\r\n  useEffect(() => {\r\n    if (countdown > 0) {\r\n      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [countdown]);\r\n\r\n  // Display auth errors as toast\r\n  useEffect(() => {\r\n    if (authError) {\r\n      toast.error(\"Authentication Error\", {\r\n        description: authError,\r\n      });\r\n      clearError();\r\n    }\r\n  }, [authError, clearError]);\r\n\r\n  // Helper function to handle post-login redirect with token-based system\r\n  async function handlePostLoginRedirect() {\r\n    try {\r\n      const authState = useAuthStore.getState();\r\n      \r\n      if (!authState.user) {\r\n        console.error(\"No user found after login\");\r\n        router.push(\"/?view=home\");\r\n        return;\r\n      }\r\n\r\n      // For token-based system, we still need to query user profiles to determine redirect\r\n      // We'll use the authenticated API client for this\r\n      const supabase = createClient();\r\n      const redirectPath = await getPostLoginRedirectPath(supabase, authState.user.id);\r\n\r\n      // Handle redirect logic\r\n      if (redirectSlug) {\r\n        // IMPORTANT: Prevent open redirect vulnerabilities.\r\n        // Ensure the redirectSlug is a relative path and not an external URL.\r\n        if (redirectSlug.includes('://') || redirectSlug.startsWith('//')) {\r\n          console.warn('Attempted redirect to an external or malformed URL. Redirecting to default path.');\r\n          router.push(redirectPath);\r\n          return;\r\n        }\r\n\r\n        // If the user is a new user going to choose-role, pass the redirect parameter\r\n        if (redirectPath === '/choose-role') {\r\n          const params = new URLSearchParams();\r\n          params.set('redirect', redirectSlug);\r\n          if (message) {\r\n            params.set('message', message);\r\n          }\r\n          router.push(`${redirectPath}?${params.toString()}`);\r\n          return;\r\n        }\r\n\r\n        // If it's a relative path for existing users, we can proceed.\r\n        // The application's routing will handle the validity of the path (e.g., 404 for invalid slugs).\r\n        const cleanRedirectSlug = redirectSlug.startsWith('/') ? redirectSlug : `/${redirectSlug}`;\r\n        router.push(`${cleanRedirectSlug}${message ? `?message=${encodeURIComponent(message)}` : \"\"}`);\r\n      } else {\r\n        router.push(redirectPath);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error determining redirect path:\", error);\r\n      router.push(\"/?view=home\");\r\n    }\r\n  }\r\n\r\n  function onEmailSubmit(values: { email: string }) {\r\n    startTransition(async () => {\r\n      try {\r\n        const result = await sendOTP(values);\r\n\r\n        if (!result.success) {\r\n          // Check if this is a configuration error (email rate limit)\r\n          if ('isConfigurationError' in result && result.isConfigurationError) {\r\n            toast.error(\"Configuration Error\", {\r\n              description: result.error,\r\n              duration: 10000, // Show longer for configuration errors\r\n            });\r\n            // Don't proceed to OTP step for configuration errors\r\n            return;\r\n          }\r\n\r\n          toast.error(\"Failed to send OTP\", {\r\n            description: result.error,\r\n          });\r\n          return;\r\n        }\r\n\r\n        toast.success(\"OTP sent!\", {\r\n          description: result.message,\r\n        });\r\n\r\n        setEmail(values.email);\r\n        setStep('otp');\r\n        setCountdown(60); // 60 second countdown (Supabase rate limit)\r\n      } catch (_error) {\r\n        toast.error(\"Failed to send OTP\", {\r\n          description: \"An unexpected error occurred. Please try again.\",\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  function onOTPSubmit(values: { email: string; otp: string }) {\r\n    startTransition(async () => {\r\n      try {\r\n        // Clear any previous errors\r\n        clearError();\r\n\r\n        const result = await verifyOTP({\r\n          email: values.email,\r\n          otp: values.otp,\r\n        });\r\n\r\n        if (!result.success) {\r\n          toast.error(\"OTP verification failed\", {\r\n            description: result.error,\r\n          });\r\n          return;\r\n        }\r\n\r\n        // Extract user info from access token\r\n        const payload = result.data.accessToken ? decodeJWTPayload(result.data.accessToken) : null;\r\n        const user = {\r\n          id: payload?.user_id || '',\r\n          email: values.email,\r\n        };\r\n\r\n        const tokens = {\r\n          accessToken: result.data.accessToken,\r\n          refreshToken: result.data.refreshToken,\r\n          deviceId: result.data.deviceId,\r\n          deviceSecret: result.data.deviceSecret,\r\n          hmacKey: result.data.hmacKey,\r\n        };\r\n\r\n        // Update auth store with new tokens and user info\r\n        const authStore = useAuthStore.getState();\r\n        authStore.setTokens(tokens);\r\n        authStore.setUser(user);\r\n\r\n        toast.success(\"Sign in successful!\", {\r\n          description: \"Redirecting to your dashboard...\",\r\n        });\r\n\r\n        // Use proper post-login redirect logic\r\n        await handlePostLoginRedirect();\r\n      } catch (_error) {\r\n        toast.error(\"OTP verification failed\", {\r\n          description: \"An unexpected error occurred. Please try again.\",\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  function handleResendOTP() {\r\n    if (countdown > 0) return;\r\n\r\n    // Use the same logic as onEmailSubmit for resending\r\n    startTransition(async () => {\r\n      try {\r\n        const result = await sendOTP({ email });\r\n\r\n        if (!result.success) {\r\n          // Check if this is a configuration error (email rate limit)\r\n          if ('isConfigurationError' in result && result.isConfigurationError) {\r\n            toast.error(\"Configuration Error\", {\r\n              description: result.error,\r\n              duration: 10000, // Show longer for configuration errors\r\n            });\r\n            return;\r\n          }\r\n\r\n          toast.error(\"Failed to resend OTP\", {\r\n            description: result.error,\r\n          });\r\n          return;\r\n        }\r\n\r\n        toast.success(\"OTP resent!\", {\r\n          description: result.message,\r\n        });\r\n\r\n        setCountdown(60); // Reset countdown\r\n      } catch (_error) {\r\n        toast.error(\"Failed to resend OTP\", {\r\n          description: \"An unexpected error occurred. Please try again.\",\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  function handleBackToEmail() {\r\n    setStep('email');\r\n    setEmail('');\r\n  }\r\n\r\n  function onMobilePasswordSubmit(values: { mobile: string; password: string }) {\r\n    startTransition(async () => {\r\n      try {\r\n        // Clear any previous errors\r\n        clearError();\r\n\r\n        // Format mobile number with +91 prefix as email for our API\r\n        const phoneNumber = `+91${values.mobile}`;\r\n        \r\n        const deviceInfo = {\r\n          deviceName: `Web Browser - ${new Date().toISOString().slice(0, 10)}`,\r\n          platform: 'web' as const,\r\n        };\r\n\r\n        // Use auth store login method\r\n        const result = await login(phoneNumber, values.password, deviceInfo);\r\n\r\n        if (!result.success) {\r\n          toast.error(\"Login failed\", {\r\n            description: result.error,\r\n          });\r\n          return;\r\n        }\r\n\r\n        toast.success(\"Sign in successful!\", {\r\n          description: \"Redirecting to your dashboard...\",\r\n        });\r\n\r\n        // Use proper post-login redirect logic with new token-based system\r\n        await handlePostLoginRedirect();\r\n      } catch (_error) {\r\n        toast.error(\"Login failed\", {\r\n          description: \"An unexpected error occurred. Please try again.\",\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  function handleAuthMethodChange(method: 'email-otp' | 'mobile-password') {\r\n    if (method !== authMethod) {\r\n      setAuthMethod(method);\r\n      setStep('email');\r\n      // Don't reset forms - let each component manage its own state\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"w-full max-w-[90%] sm:max-w-md md:max-w-lg\">\r\n        <motion.div\r\n          initial={{ opacity: 0, scale: 0.95 }}\r\n          animate={{ opacity: 1, scale: 1 }}\r\n          transition={{ duration: 0.6, delay: 0.2 }}\r\n        >\r\n          <Card className=\"bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-4 sm:p-6 md:p-8 rounded-xl sm:rounded-2xl shadow-lg dark:shadow-[var(--brand-gold)]/10\">\r\n            <div className=\"text-center mb-6 sm:mb-8\">\r\n              <h1 className=\"text-xl sm:text-2xl font-bold text-foreground mb-1 sm:mb-2\">\r\n                {authMethod === 'email-otp' && step === 'otp' ? 'Enter Verification Code' : 'Welcome to Dukancard'}\r\n              </h1>\r\n              <p className=\"text-sm sm:text-base text-muted-foreground\">\r\n                {authMethod === 'email-otp' && step === 'otp'\r\n                  ? 'Check your email for the 6-digit code'\r\n                  : authMethod === 'email-otp'\r\n                  ? 'Sign in or create your account with email'\r\n                  : 'Sign in with your mobile number and password'\r\n                }\r\n              </p>\r\n\r\n              {message && (\r\n                <div className={`mt-4 p-2 sm:p-3 rounded-lg ${\r\n                  message.toLowerCase().includes(\"error\") || message.toLowerCase().includes(\"failed\")\r\n                    ? \"bg-destructive/10 text-destructive\"\r\n                    : \"bg-green-500/10 text-green-600 dark:text-green-400\"\r\n                }`}>\r\n                  <p className=\"text-xs sm:text-sm\">{message}</p>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Authentication Method Toggle */}\r\n            <AuthMethodToggle\r\n              authMethod={authMethod}\r\n              step={step}\r\n              onMethodChange={handleAuthMethodChange}\r\n            />\r\n\r\n            {/* Social Login Button */}\r\n            <SocialLoginButton\r\n              redirectSlug={redirectSlug}\r\n              message={message}\r\n              disabled={isPending}\r\n            />\r\n\r\n            <div className=\"relative mb-5 sm:mb-6\">\r\n              <div className=\"absolute inset-0 flex items-center\">\r\n                <div className=\"w-full border-t border-border\" />\r\n              </div>\r\n              <div className=\"relative flex justify-center text-xs uppercase\">\r\n                <span className=\"bg-card px-2 text-muted-foreground\">\r\n                  {authMethod === 'email-otp' && step === 'email' ? 'Or continue with email' :\r\n                   authMethod === 'mobile-password' ? 'Or continue with mobile' :\r\n                   'Or use Google instead'}\r\n                </span>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Form Components */}\r\n            {authMethod === 'email-otp' ? (\r\n              <EmailOTPForm\r\n                step={step}\r\n                email={email}\r\n                countdown={countdown}\r\n                isPending={isPending}\r\n                onEmailSubmit={onEmailSubmit}\r\n                onOTPSubmit={onOTPSubmit}\r\n                onResendOTP={handleResendOTP}\r\n                onBackToEmail={handleBackToEmail}\r\n              />\r\n            ) : (\r\n              <MobilePasswordForm\r\n                isPending={isPending || authLoading}\r\n                onSubmit={onMobilePasswordSubmit}\r\n              />\r\n            )}\r\n          </Card>\r\n        </motion.div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;;;;;;AAgBA,6EAA6E;AAC7E,SAAS,iBAAiB,KAAyB;IACjD,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI;QACF,MAAM,QAAQ,MAAM,KAAK,CAAC;QAC1B,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;QAE/B,MAAM,UAAU,KAAK,CAAC,EAAE;QACxB,4CAA4C;QAC5C,MAAM,gBAAgB,UAAU,IAAI,MAAM,CAAC,CAAC,IAAI,QAAQ,MAAM,GAAG,CAAC,IAAI;QACtE,MAAM,UAAU,KAAK,cAAc,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM;QACpE,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;IACT;AACF;AAEO,SAAS;IACd,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,WAAW,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;IAC9E,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAClD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEnD,mBAAmB;IACnB,MAAM,EAAE,KAAK,EAAE,WAAW,WAAW,EAAE,OAAO,SAAS,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IAEnF,uDAAuD;IACvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,IAAI,UAAU;YACZ,gBAAgB;QAClB;QAEA,MAAM,eAAe,aAAa,GAAG,CAAC;QACtC,IAAI,cAAc;YAChB,WAAW;QACb;IACF,GAAG;QAAC;KAAa;IAEjB,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,GAAG;YACjB,MAAM,QAAQ,WAAW,IAAM,aAAa,YAAY,IAAI;YAC5D,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;KAAU;IAEd,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,wBAAwB;gBAClC,aAAa;YACf;YACA;QACF;IACF,GAAG;QAAC;QAAW;KAAW;IAE1B,wEAAwE;IACxE,eAAe;QACb,IAAI;YACF,MAAM,YAAY,0HAAA,CAAA,eAAY,CAAC,QAAQ;YAEvC,IAAI,CAAC,UAAU,IAAI,EAAE;gBACnB,QAAQ,KAAK,CAAC;gBACd,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,qFAAqF;YACrF,kDAAkD;YAClD,MAAM,WAAW,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;YAC5B,MAAM,eAAe,MAAM,CAAA,GAAA,oIAAA,CAAA,2BAAwB,AAAD,EAAE,UAAU,UAAU,IAAI,CAAC,EAAE;YAE/E,wBAAwB;YACxB,IAAI,cAAc;gBAChB,oDAAoD;gBACpD,sEAAsE;gBACtE,IAAI,aAAa,QAAQ,CAAC,UAAU,aAAa,UAAU,CAAC,OAAO;oBACjE,QAAQ,IAAI,CAAC;oBACb,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,8EAA8E;gBAC9E,IAAI,iBAAiB,gBAAgB;oBACnC,MAAM,SAAS,IAAI;oBACnB,OAAO,GAAG,CAAC,YAAY;oBACvB,IAAI,SAAS;wBACX,OAAO,GAAG,CAAC,WAAW;oBACxB;oBACA,OAAO,IAAI,CAAC,GAAG,aAAa,CAAC,EAAE,OAAO,QAAQ,IAAI;oBAClD;gBACF;gBAEA,8DAA8D;gBAC9D,gGAAgG;gBAChG,MAAM,oBAAoB,aAAa,UAAU,CAAC,OAAO,eAAe,CAAC,CAAC,EAAE,cAAc;gBAC1F,OAAO,IAAI,CAAC,GAAG,oBAAoB,UAAU,CAAC,SAAS,EAAE,mBAAmB,UAAU,GAAG,IAAI;YAC/F,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO,IAAI,CAAC;QACd;IACF;IAEA,SAAS,cAAc,MAAyB;QAC9C,gBAAgB;YACd,IAAI;gBACF,MAAM,SAAS,MAAM,CAAA,GAAA,gKAAA,CAAA,UAAO,AAAD,EAAE;gBAE7B,IAAI,CAAC,OAAO,OAAO,EAAE;oBACnB,4DAA4D;oBAC5D,IAAI,0BAA0B,UAAU,OAAO,oBAAoB,EAAE;wBACnE,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,uBAAuB;4BACjC,aAAa,OAAO,KAAK;4BACzB,UAAU;wBACZ;wBACA,qDAAqD;wBACrD;oBACF;oBAEA,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,sBAAsB;wBAChC,aAAa,OAAO,KAAK;oBAC3B;oBACA;gBACF;gBAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,aAAa;oBACzB,aAAa,OAAO,OAAO;gBAC7B;gBAEA,SAAS,OAAO,KAAK;gBACrB,QAAQ;gBACR,aAAa,KAAK,4CAA4C;YAChE,EAAE,OAAO,QAAQ;gBACf,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,sBAAsB;oBAChC,aAAa;gBACf;YACF;QACF;IACF;IAEA,SAAS,YAAY,MAAsC;QACzD,gBAAgB;YACd,IAAI;gBACF,4BAA4B;gBAC5B;gBAEA,MAAM,SAAS,MAAM,CAAA,GAAA,gKAAA,CAAA,YAAS,AAAD,EAAE;oBAC7B,OAAO,OAAO,KAAK;oBACnB,KAAK,OAAO,GAAG;gBACjB;gBAEA,IAAI,CAAC,OAAO,OAAO,EAAE;oBACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,2BAA2B;wBACrC,aAAa,OAAO,KAAK;oBAC3B;oBACA;gBACF;gBAEA,sCAAsC;gBACtC,MAAM,UAAU,OAAO,IAAI,CAAC,WAAW,GAAG,iBAAiB,OAAO,IAAI,CAAC,WAAW,IAAI;gBACtF,MAAM,OAAO;oBACX,IAAI,SAAS,WAAW;oBACxB,OAAO,OAAO,KAAK;gBACrB;gBAEA,MAAM,SAAS;oBACb,aAAa,OAAO,IAAI,CAAC,WAAW;oBACpC,cAAc,OAAO,IAAI,CAAC,YAAY;oBACtC,UAAU,OAAO,IAAI,CAAC,QAAQ;oBAC9B,cAAc,OAAO,IAAI,CAAC,YAAY;oBACtC,SAAS,OAAO,IAAI,CAAC,OAAO;gBAC9B;gBAEA,kDAAkD;gBAClD,MAAM,YAAY,0HAAA,CAAA,eAAY,CAAC,QAAQ;gBACvC,UAAU,SAAS,CAAC;gBACpB,UAAU,OAAO,CAAC;gBAElB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,uBAAuB;oBACnC,aAAa;gBACf;gBAEA,uCAAuC;gBACvC,MAAM;YACR,EAAE,OAAO,QAAQ;gBACf,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,2BAA2B;oBACrC,aAAa;gBACf;YACF;QACF;IACF;IAEA,SAAS;QACP,IAAI,YAAY,GAAG;QAEnB,oDAAoD;QACpD,gBAAgB;YACd,IAAI;gBACF,MAAM,SAAS,MAAM,CAAA,GAAA,gKAAA,CAAA,UAAO,AAAD,EAAE;oBAAE;gBAAM;gBAErC,IAAI,CAAC,OAAO,OAAO,EAAE;oBACnB,4DAA4D;oBAC5D,IAAI,0BAA0B,UAAU,OAAO,oBAAoB,EAAE;wBACnE,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,uBAAuB;4BACjC,aAAa,OAAO,KAAK;4BACzB,UAAU;wBACZ;wBACA;oBACF;oBAEA,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,wBAAwB;wBAClC,aAAa,OAAO,KAAK;oBAC3B;oBACA;gBACF;gBAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,eAAe;oBAC3B,aAAa,OAAO,OAAO;gBAC7B;gBAEA,aAAa,KAAK,kBAAkB;YACtC,EAAE,OAAO,QAAQ;gBACf,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,wBAAwB;oBAClC,aAAa;gBACf;YACF;QACF;IACF;IAEA,SAAS;QACP,QAAQ;QACR,SAAS;IACX;IAEA,SAAS,uBAAuB,MAA4C;QAC1E,gBAAgB;YACd,IAAI;gBACF,4BAA4B;gBAC5B;gBAEA,4DAA4D;gBAC5D,MAAM,cAAc,CAAC,GAAG,EAAE,OAAO,MAAM,EAAE;gBAEzC,MAAM,aAAa;oBACjB,YAAY,CAAC,cAAc,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,GAAG,KAAK;oBACpE,UAAU;gBACZ;gBAEA,8BAA8B;gBAC9B,MAAM,SAAS,MAAM,MAAM,aAAa,OAAO,QAAQ,EAAE;gBAEzD,IAAI,CAAC,OAAO,OAAO,EAAE;oBACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gBAAgB;wBAC1B,aAAa,OAAO,KAAK;oBAC3B;oBACA;gBACF;gBAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,uBAAuB;oBACnC,aAAa;gBACf;gBAEA,mEAAmE;gBACnE,MAAM;YACR,EAAE,OAAO,QAAQ;gBACf,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gBAAgB;oBAC1B,aAAa;gBACf;YACF;QACF;IACF;IAEA,SAAS,uBAAuB,MAAuC;QACrE,IAAI,WAAW,YAAY;YACzB,cAAc;YACd,QAAQ;QACR,8DAA8D;QAChE;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACX,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAK;YACnC,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,YAAY;gBAAE,UAAU;gBAAK,OAAO;YAAI;sBAExC,cAAA,8OAAC,yHAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,eAAe,eAAe,SAAS,QAAQ,4BAA4B;;;;;;0CAE9E,8OAAC;gCAAE,WAAU;0CACV,eAAe,eAAe,SAAS,QACpC,0CACA,eAAe,cACf,8CACA;;;;;;4BAIL,yBACC,8OAAC;gCAAI,WAAW,CAAC,2BAA2B,EAC1C,QAAQ,WAAW,GAAG,QAAQ,CAAC,YAAY,QAAQ,WAAW,GAAG,QAAQ,CAAC,YACtE,uCACA,sDACJ;0CACA,cAAA,8OAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;;;;;;;kCAMzC,8OAAC,2JAAA,CAAA,mBAAgB;wBACf,YAAY;wBACZ,MAAM;wBACN,gBAAgB;;;;;;kCAIlB,8OAAC,4JAAA,CAAA,oBAAiB;wBAChB,cAAc;wBACd,SAAS;wBACT,UAAU;;;;;;kCAGZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;;;;;;;;;;0CAEjB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CACb,eAAe,eAAe,SAAS,UAAU,2BACjD,eAAe,oBAAoB,4BACnC;;;;;;;;;;;;;;;;;oBAMN,eAAe,4BACd,8OAAC,uJAAA,CAAA,eAAY;wBACX,MAAM;wBACN,OAAO;wBACP,WAAW;wBACX,WAAW;wBACX,eAAe;wBACf,aAAa;wBACb,aAAa;wBACb,eAAe;;;;;6CAGjB,8OAAC,6JAAA,CAAA,qBAAkB;wBACjB,WAAW,aAAa;wBACxB,UAAU;;;;;;;;;;;;;;;;;;;;;;AAO1B", "debugId": null}}, {"offset": {"line": 2208, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/auth/AuthPageBackground.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport { motion, useAnimation } from \"framer-motion\";\r\n\r\nexport default function AuthPageBackground() {\r\n  const [isClient, setIsClient] = useState(false);\r\n  const [isMobile, setIsMobile] = useState(false);\r\n\r\n  // Animation controls\r\n  const gradientControls = useAnimation();\r\n  const blob1Controls = useAnimation();\r\n  const blob2Controls = useAnimation();\r\n  const blob3Controls = useAnimation();\r\n  const lineControls = useAnimation();\r\n  const nodeControls = useAnimation();\r\n\r\n  // Only render on client side and detect mobile\r\n  useEffect(() => {\r\n    setIsClient(true);\r\n    setIsMobile(window.innerWidth < 768);\r\n\r\n    const handleResize = () => {\r\n      setIsMobile(window.innerWidth < 768);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  // Setup animations\r\n  useEffect(() => {\r\n    if (isClient) {\r\n      // Gradient animation\r\n      gradientControls.start({\r\n        scale: 1.1,\r\n        transition: {\r\n          duration: 8,\r\n          repeat: Infinity,\r\n          repeatType: \"reverse\",\r\n          ease: \"easeInOut\",\r\n        },\r\n      });\r\n\r\n      // Blob animations\r\n      blob1Controls.start({\r\n        scale: 1.2,\r\n        x: 20,\r\n        transition: {\r\n          duration: 10,\r\n          repeat: Infinity,\r\n          repeatType: \"reverse\",\r\n          ease: \"easeInOut\",\r\n        },\r\n      });\r\n\r\n      blob2Controls.start({\r\n        scale: 1.15,\r\n        x: -15,\r\n        transition: {\r\n          duration: 12,\r\n          repeat: Infinity,\r\n          repeatType: \"reverse\",\r\n          ease: \"easeInOut\",\r\n        },\r\n      });\r\n\r\n      blob3Controls.start({\r\n        scale: 1.25,\r\n        y: 25,\r\n        transition: {\r\n          duration: 14,\r\n          repeat: Infinity,\r\n          repeatType: \"reverse\",\r\n          ease: \"easeInOut\",\r\n        },\r\n      });\r\n\r\n      // Line animation\r\n      lineControls.start({\r\n        scale: 1.1,\r\n        transition: {\r\n          duration: 6,\r\n          repeat: Infinity,\r\n          repeatType: \"reverse\",\r\n          ease: \"easeInOut\",\r\n        },\r\n      });\r\n\r\n      // Node animation\r\n      nodeControls.start({\r\n        scale: 1.3,\r\n        transition: {\r\n          duration: 4,\r\n          repeat: Infinity,\r\n          repeatType: \"reverse\",\r\n          ease: \"easeInOut\",\r\n        },\r\n      });\r\n    }\r\n  }, [isClient, gradientControls, blob1Controls, blob2Controls, blob3Controls, lineControls, nodeControls]);\r\n\r\n  return (\r\n    <div className=\"absolute inset-0 -z-10 overflow-hidden\">\r\n      {isClient && (\r\n        <>\r\n          {/* Main gradient background */}\r\n          <motion.div\r\n            animate={gradientControls}\r\n            className=\"absolute inset-0 opacity-40 dark:opacity-30\"\r\n            style={{\r\n              background: `radial-gradient(circle at 50% 50%,\r\n                var(--brand-gold) 0%,\r\n                rgba(var(--brand-gold-rgb), 0.3) 25%,\r\n                rgba(var(--brand-gold-rgb), 0.1) 50%,\r\n                rgba(0, 0, 255, 0.1) 75%,\r\n                rgba(0, 0, 255, 0.05) 100%)`,\r\n              filter: isMobile ? \"blur(60px)\" : \"blur(80px)\",\r\n            }}\r\n          />\r\n\r\n          {/* Top right blob */}\r\n          <motion.div\r\n            animate={blob1Controls}\r\n            className=\"absolute top-0 right-0 w-[500px] h-[500px] rounded-full bg-[var(--brand-gold-rgb)]/5 blur-3xl dark:bg-[var(--brand-gold-rgb)]/10 opacity-70\"\r\n          />\r\n\r\n          {/* Top left blob */}\r\n          <motion.div\r\n            animate={blob2Controls}\r\n            className=\"absolute -top-20 -left-20 w-[300px] h-[300px] rounded-full bg-blue-500/5 blur-3xl dark:bg-blue-500/10 opacity-70\"\r\n          />\r\n\r\n          {/* Bottom blob */}\r\n          <motion.div\r\n            animate={blob3Controls}\r\n            className=\"absolute bottom-0 left-1/4 w-[400px] h-[400px] rounded-full bg-purple-500/5 blur-3xl dark:bg-purple-500/10 opacity-60\"\r\n          />\r\n\r\n          {/* Circuit-like elements */}\r\n          <svg\r\n            className=\"absolute inset-0 w-full h-full opacity-10 dark:opacity-20 pointer-events-none\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n          >\r\n            <defs>\r\n              <filter id=\"glow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\r\n                <feGaussianBlur stdDeviation=\"2\" result=\"blur\" />\r\n                <feComposite\r\n                  in=\"SourceGraphic\"\r\n                  in2=\"blur\"\r\n                  operator=\"over\"\r\n                  result=\"glow\"\r\n                />\r\n              </filter>\r\n            </defs>\r\n\r\n            {/* Single circuit line */}\r\n            <motion.line\r\n              animate={lineControls}\r\n              x1=\"20%\"\r\n              y1=\"20%\"\r\n              x2=\"80%\"\r\n              y2=\"80%\"\r\n              stroke=\"var(--brand-gold)\"\r\n              strokeWidth=\"0.5\"\r\n              strokeOpacity=\"0.3\"\r\n              filter=\"url(#glow)\"\r\n            />\r\n\r\n            {/* Single circuit node */}\r\n            <motion.circle\r\n              animate={nodeControls}\r\n              cx=\"50%\"\r\n              cy=\"50%\"\r\n              r=\"2\"\r\n              fill=\"var(--brand-gold)\"\r\n              filter=\"url(#glow)\"\r\n            />\r\n          </svg>\r\n        </>\r\n      )}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,qBAAqB;IACrB,MAAM,mBAAmB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD;IACpC,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD;IACjC,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD;IACjC,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD;IACjC,MAAM,eAAe,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD;IAChC,MAAM,eAAe,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD;IAEhC,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;QACZ,YAAY,OAAO,UAAU,GAAG;QAEhC,MAAM,eAAe;YACnB,YAAY,OAAO,UAAU,GAAG;QAClC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,mBAAmB;IACnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,qBAAqB;YACrB,iBAAiB,KAAK,CAAC;gBACrB,OAAO;gBACP,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,YAAY;oBACZ,MAAM;gBACR;YACF;YAEA,kBAAkB;YAClB,cAAc,KAAK,CAAC;gBAClB,OAAO;gBACP,GAAG;gBACH,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,YAAY;oBACZ,MAAM;gBACR;YACF;YAEA,cAAc,KAAK,CAAC;gBAClB,OAAO;gBACP,GAAG,CAAC;gBACJ,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,YAAY;oBACZ,MAAM;gBACR;YACF;YAEA,cAAc,KAAK,CAAC;gBAClB,OAAO;gBACP,GAAG;gBACH,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,YAAY;oBACZ,MAAM;gBACR;YACF;YAEA,iBAAiB;YACjB,aAAa,KAAK,CAAC;gBACjB,OAAO;gBACP,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,YAAY;oBACZ,MAAM;gBACR;YACF;YAEA,iBAAiB;YACjB,aAAa,KAAK,CAAC;gBACjB,OAAO;gBACP,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,YAAY;oBACZ,MAAM;gBACR;YACF;QACF;IACF,GAAG;QAAC;QAAU;QAAkB;QAAe;QAAe;QAAe;QAAc;KAAa;IAExG,qBACE,8OAAC;QAAI,WAAU;kBACZ,0BACC;;8BAEE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;oBACT,WAAU;oBACV,OAAO;wBACL,YAAY,CAAC;;;;;2CAKgB,CAAC;wBAC9B,QAAQ,WAAW,eAAe;oBACpC;;;;;;8BAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;oBACT,WAAU;;;;;;8BAIZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;oBACT,WAAU;;;;;;8BAIZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;oBACT,WAAU;;;;;;8BAIZ,8OAAC;oBACC,WAAU;oBACV,OAAM;;sCAEN,8OAAC;sCACC,cAAA,8OAAC;gCAAO,IAAG;gCAAO,GAAE;gCAAO,GAAE;gCAAO,OAAM;gCAAO,QAAO;;kDACtD,8OAAC;wCAAe,cAAa;wCAAI,QAAO;;;;;;kDACxC,8OAAC;wCACC,IAAG;wCACH,KAAI;wCACJ,UAAS;wCACT,QAAO;;;;;;;;;;;;;;;;;sCAMb,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4BACV,SAAS;4BACT,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,QAAO;4BACP,aAAY;4BACZ,eAAc;4BACd,QAAO;;;;;;sCAIT,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,SAAS;4BACT,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,MAAK;4BACL,QAAO;;;;;;;;;;;;;;;;;;;AAOrB", "debugId": null}}]}