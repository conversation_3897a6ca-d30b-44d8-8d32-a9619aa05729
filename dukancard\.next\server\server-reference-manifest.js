self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"00a8cc48c10fab6f16e252026656352c7c5fad9033\": {\n      \"workers\": {\n        \"app/(main)/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(main)/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/(main)/actions/getHomepageBusinessCard.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(main)/page\": \"rsc\"\n      }\n    },\n    \"00fb28f2104d350da9091aac6cd03550c4bf0d5e04\": {\n      \"workers\": {\n        \"app/(main)/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(main)/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/(main)/actions/getHomepageBusinessCard.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(main)/page\": \"rsc\"\n      }\n    },\n    \"408078c315b7b0094b642ab831ed7eb03d4fd98823\": {\n      \"workers\": {\n        \"app/(main)/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(main)/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/(main)/actions/getHomepageBusinessCard.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(main)/page\": \"action-browser\"\n      }\n    },\n    \"40c76fcf00cbeff9df8975c5b6c7b4d802b3ba9578\": {\n      \"workers\": {\n        \"app/(main)/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(main)/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/(main)/actions/getHomepageBusinessCard.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(main)/page\": \"action-browser\"\n      }\n    },\n    \"40d6a9ae93402e0d28dbc39a1da61f9a25a9080013\": {\n      \"workers\": {\n        \"app/(main)/login/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(main)/login/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/(main)/login/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(main)/login/page\": \"action-browser\"\n      }\n    },\n    \"40618e9fcbfa39ef9ffb9cb228c1ace1d6627d146c\": {\n      \"workers\": {\n        \"app/(main)/login/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(main)/login/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/(main)/login/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(main)/login/page\": \"action-browser\"\n      }\n    },\n    \"40233faece1b387c15157ff9feaa5d282a77135da8\": {\n      \"workers\": {\n        \"app/(main)/discover/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(main)/discover/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/(main)/discover/actions/combinedActions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/actions/location.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(main)/discover/page\": \"action-browser\"\n      }\n    },\n    \"401d1fd9f2f483c3d17b0c79ea53a43a19793352e1\": {\n      \"workers\": {\n        \"app/(main)/discover/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(main)/discover/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/(main)/discover/actions/combinedActions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/actions/location.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(main)/discover/page\": \"action-browser\"\n      }\n    },\n    \"4059dac56ec2d4fa83cae240560bfebb118fd5640f\": {\n      \"workers\": {\n        \"app/(main)/discover/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(main)/discover/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/(main)/discover/actions/combinedActions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/actions/location.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(main)/discover/page\": \"action-browser\"\n      }\n    },\n    \"40cf076a0ff37f204cc8708187f4777e00ee8b9608\": {\n      \"workers\": {\n        \"app/(main)/discover/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(main)/discover/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/(main)/discover/actions/combinedActions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/actions/location.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(main)/discover/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"ENoSsqhUZznD9fxor6afRENMBt9QqLrvjNeiFMwX4Wk=\"\n}"