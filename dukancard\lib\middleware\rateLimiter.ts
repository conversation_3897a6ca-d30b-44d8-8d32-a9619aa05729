import { NextRequest, NextResponse } from 'next/server';
import { Redis } from '@upstash/redis';

// Initialize Redis client
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
});

export interface RateLimitConfig {
  /** Maximum requests allowed */
  maxRequests: number;
  /** Time window in seconds */
  windowSeconds: number;
  /** Identifier for this rate limit (e.g., 'ip', 'device', 'user') */
  identifier: string;
}

export interface RateLimitResult {
  success: boolean;
  error?: string;
  status?: number;
  limit?: number;
  remaining?: number;
  reset?: number;
  retryAfter?: number;
}

/**
 * Get client IP address from request
 */
function getClientIP(req: NextRequest): string {
  const forwarded = req.headers.get('x-forwarded-for');
  const realIP = req.headers.get('x-real-ip');
  const remoteAddress = req.headers.get('x-remote-address');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  return realIP || remoteAddress || 'unknown';
}

/**
 * Generate Redis key for rate limiting
 */
function generateRateLimitKey(identifier: string, value: string, windowStart: number): string {
  return `ratelimit:${identifier}:${value}:${windowStart}`;
}

/**
 * Get the start of the current time window
 */
function getWindowStart(windowSeconds: number): number {
  const now = Math.floor(Date.now() / 1000);
  return Math.floor(now / windowSeconds) * windowSeconds;
}

/**
 * Check rate limit for a specific identifier and value
 */
async function checkRateLimit(
  identifier: string,
  value: string,
  config: RateLimitConfig
): Promise<RateLimitResult> {
  try {
    const windowStart = getWindowStart(config.windowSeconds);
    const key = generateRateLimitKey(identifier, value, windowStart);
    const windowEnd = windowStart + config.windowSeconds;

    // Get current count
    const currentCount = await redis.get<number>(key) || 0;

    if (currentCount >= config.maxRequests) {
      // Rate limit exceeded
      const retryAfter = windowEnd - Math.floor(Date.now() / 1000);
      return {
        success: false,
        error: 'Rate limit exceeded',
        status: 429,
        limit: config.maxRequests,
        remaining: 0,
        reset: windowEnd,
        retryAfter: Math.max(retryAfter, 0),
      };
    }

    // Increment counter
    const newCount = await redis.incr(key);
    
    // Set expiration if this is the first request in the window
    if (newCount === 1) {
      await redis.expire(key, config.windowSeconds);
    }

    // Calculate remaining requests
    const remaining = Math.max(config.maxRequests - newCount, 0);

    return {
      success: true,
      limit: config.maxRequests,
      remaining,
      reset: windowEnd,
    };
  } catch (error) {
    console.error(`Rate limit check failed for ${identifier}:${value}:`, error);
    
    // In case of Redis error, allow the request but log the error
    return {
      success: true,
      limit: config.maxRequests,
      remaining: config.maxRequests,
      reset: Math.floor(Date.now() / 1000) + config.windowSeconds,
    };
  }
}

/**
 * Rate limiting middleware with multiple strategies
 */
export async function rateLimitMiddleware(
  req: NextRequest,
  strategies: Array<{ 
    identifier: string; 
    getValue: (req: NextRequest) => string | null; 
    config: RateLimitConfig 
  }> = []
): Promise<RateLimitResult> {
  // Default strategies if none provided
  if (strategies.length === 0) {
    const defaultMaxRequests = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10);
    const defaultWindowSeconds = parseInt(process.env.RATE_LIMIT_WINDOW_SECONDS || '60', 10);

    strategies = [
      {
        identifier: 'ip',
        getValue: (req) => getClientIP(req),
        config: {
          maxRequests: defaultMaxRequests,
          windowSeconds: defaultWindowSeconds,
          identifier: 'ip',
        },
      },
    ];
  }

  let finalResult: RateLimitResult = {
    success: true,
    limit: strategies[0]?.config.maxRequests || 100,
    remaining: strategies[0]?.config.maxRequests || 100,
    reset: Math.floor(Date.now() / 1000) + (strategies[0]?.config.windowSeconds || 60),
  };

  // Apply strategies in order
  for (const strategy of strategies) {
    const value = strategy.getValue(req);
    
    // Skip strategy if value cannot be determined
    if (!value || value === 'unknown') {
      continue;
    }

    const result = await checkRateLimit(strategy.identifier, value, strategy.config);
    
    if (!result.success) {
      return result;
    }

    // Update final result with the most restrictive limits
    if (result.remaining !== undefined && result.remaining < (finalResult.remaining || Infinity)) {
      finalResult = {
        ...finalResult,
        limit: result.limit,
        remaining: result.remaining,
        reset: result.reset,
      };
    }
  }

  return finalResult;
}

/**
 * Apply rate limiting strategies with common configurations
 */
export async function applyRateLimiting(
  req: NextRequest,
  options: {
    deviceId?: string;
    userId?: string;
    byIP?: boolean;
    byDevice?: boolean;
    byUser?: boolean;
    customLimits?: {
      ip?: RateLimitConfig;
      device?: RateLimitConfig;
      user?: RateLimitConfig;
    };
  } = {}
): Promise<RateLimitResult> {
  const strategies: Array<{
    identifier: string;
    getValue: (req: NextRequest) => string | null;
    config: RateLimitConfig;
  }> = [];

  // Default configurations
  const defaultIPLimit: RateLimitConfig = {
    maxRequests: parseInt(process.env.RATE_LIMIT_IP_MAX_REQUESTS || '100', 10),
    windowSeconds: parseInt(process.env.RATE_LIMIT_IP_WINDOW_SECONDS || '60', 10),
    identifier: 'ip',
  };

  const defaultDeviceLimit: RateLimitConfig = {
    maxRequests: parseInt(process.env.RATE_LIMIT_DEVICE_MAX_REQUESTS || '200', 10),
    windowSeconds: parseInt(process.env.RATE_LIMIT_DEVICE_WINDOW_SECONDS || '60', 10),
    identifier: 'device',
  };

  const defaultUserLimit: RateLimitConfig = {
    maxRequests: parseInt(process.env.RATE_LIMIT_USER_MAX_REQUESTS || '500', 10),
    windowSeconds: parseInt(process.env.RATE_LIMIT_USER_WINDOW_SECONDS || '60', 10),
    identifier: 'user',
  };

  // Add IP rate limiting (enabled by default)
  if (options.byIP !== false) {
    strategies.push({
      identifier: 'ip',
      getValue: (req) => getClientIP(req),
      config: options.customLimits?.ip || defaultIPLimit,
    });
  }

  // Add device rate limiting
  if (options.byDevice && options.deviceId) {
    strategies.push({
      identifier: 'device',
      getValue: () => options.deviceId!,
      config: options.customLimits?.device || defaultDeviceLimit,
    });
  }

  // Add user rate limiting
  if (options.byUser && options.userId) {
    strategies.push({
      identifier: 'user',
      getValue: () => options.userId!,
      config: options.customLimits?.user || defaultUserLimit,
    });
  }

  return rateLimitMiddleware(req, strategies);
}

/**
 * Next.js middleware wrapper for rate limiting
 */
export async function rateLimitingMiddleware(
  req: NextRequest,
  options: Parameters<typeof applyRateLimiting>[1] = {}
): Promise<NextResponse | null> {
  const result = await applyRateLimiting(req, options);

  if (!result.success) {
    const response = new NextResponse(JSON.stringify({ error: result.error }), {
      status: result.status || 429,
      headers: {
        'Content-Type': 'application/json',
        'X-RateLimit-Limit': result.limit?.toString() || '',
        'X-RateLimit-Remaining': result.remaining?.toString() || '0',
        'X-RateLimit-Reset': result.reset?.toString() || '',
        'Retry-After': result.retryAfter?.toString() || '',
      },
    });

    return response;
  }

  // Add rate limit headers to successful responses
  // Note: This will be handled by the calling code since we're returning null to continue
  return null;
}

/**
 * Add rate limit headers to response
 */
export function addRateLimitHeaders(
  response: NextResponse,
  result: RateLimitResult
): NextResponse {
  if (result.limit !== undefined) {
    response.headers.set('X-RateLimit-Limit', result.limit.toString());
  }
  if (result.remaining !== undefined) {
    response.headers.set('X-RateLimit-Remaining', result.remaining.toString());
  }
  if (result.reset !== undefined) {
    response.headers.set('X-RateLimit-Reset', result.reset.toString());
  }
  
  return response;
}