"use server";

/**
 * Check if the current user has access to a business profile
 * This is used to verify ownership before allowing certain operations
 */
export async function checkBusinessProfileAccess(
  businessProfileId: string
): Promise<{
  hasAccess: boolean;
  error?: string;
}> {
  try {
    // Use the business access API endpoint
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business/access`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Note: In a real implementation, this would need proper authentication headers
        // For now, this is a placeholder - the actual implementation would need
        // to pass through the user's JWT token and HMAC signature
      },
      body: JSON.stringify({
        business_profile_id: businessProfileId,
      }),
    });

    const result = await response.json();

    if (!response.ok) {
      console.error("API Fetch Error:", result);
      return {
        hasAccess: false,
        error: result.error || "Failed to check business profile access",
      };
    }

    return { hasAccess: result.has_access };
  } catch (e) {
    console.error("Exception in checkBusinessProfileAccess:", e);
    return { hasAccess: false, error: "An unexpected error occurred" };
  }
}

/**
 * Get the current user's business profile ID
 * This is useful for operations that need to know the user's business profile
 */
export async function getCurrentUserBusinessProfileId(): Promise<{
  profileId?: string;
  error?: string;
}> {
  try {
    // Use the business access API endpoint
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business/access/me`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Note: In a real implementation, this would need proper authentication headers
        // For now, this is a placeholder - the actual implementation would need
        // to pass through the user's JWT token and HMAC signature
      },
    });

    const result = await response.json();

    if (!response.ok) {
      console.error("API Fetch Error:", result);
      return {
        error: result.error || "Failed to get current user business profile ID",
      };
    }

    return { profileId: result.profile_id };
  } catch (e) {
    console.error("Exception in getCurrentUserBusinessProfileId:", e);
    return { error: "An unexpected error occurred" };
  }
}
