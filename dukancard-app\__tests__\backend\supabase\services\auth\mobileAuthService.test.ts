/**
 * @jest-environment node
 */
import { MobileAuthService } from '@/backend/supabase/services/auth/mobileAuthService';
import { supabase } from '@/lib/supabase';

// Mock dependencies
jest.mock('@/lib/supabase');

const mockSupabase = supabase as jest.Mocked<typeof supabase>;

describe('MobileAuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('signInWithMobilePassword', () => {
    it('should successfully sign in with mobile and password', async () => {
      const mobile = '9876543210';
      const password = 'password123';
      const mockResult = {
        data: {
          user: {
            id: 'user123',
            phone: '+919876543210',
            email: null,
          },
          session: {
            access_token: 'mock_access_token',
            refresh_token: 'mock_refresh_token',
          },
        },
        error: null,
      };

      mockSupabase.auth.signInWithPassword.mockResolvedValue(mockResult);

      const result = await MobileAuthService.signInWithMobilePassword(mobile, password);

      expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
        phone: '+919876543210',
        password: 'password123',
      });
      expect(result).toEqual(mockResult);
    });

    it('should add +91 country code to mobile number', async () => {
      const mobile = '9876543210';
      const password = 'password123';
      
      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: null, session: null },
        error: null,
      });

      await MobileAuthService.signInWithMobilePassword(mobile, password);

      expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
        phone: '+919876543210',
        password: 'password123',
      });
    });

    it('should handle invalid credentials error', async () => {
      const mobile = '9876543210';
      const password = 'wrongpassword';
      const error = {
        message: 'Invalid login credentials',
        status: 400,
      };
      const mockResult = {
        data: { user: null, session: null },
        error,
      };

      mockSupabase.auth.signInWithPassword.mockResolvedValue(mockResult);

      const result = await MobileAuthService.signInWithMobilePassword(mobile, password);

      expect(result).toEqual(mockResult);
      expect(result.data.user).toBeNull();
      expect(result.error).toEqual(error);
    });

    it('should handle user not found error', async () => {
      const mobile = '1234567890';
      const password = 'password123';
      const error = {
        message: 'User not found',
        status: 404,
      };
      const mockResult = {
        data: { user: null, session: null },
        error,
      };

      mockSupabase.auth.signInWithPassword.mockResolvedValue(mockResult);

      const result = await MobileAuthService.signInWithMobilePassword(mobile, password);

      expect(result).toEqual(mockResult);
      expect(result.error.message).toBe('User not found');
    });

    it('should handle network errors', async () => {
      const mobile = '9876543210';
      const password = 'password123';
      const networkError = new Error('Network timeout');

      mockSupabase.auth.signInWithPassword.mockRejectedValue(networkError);

      await expect(
        MobileAuthService.signInWithMobilePassword(mobile, password)
      ).rejects.toThrow('Network timeout');
    });

    it('should handle rate limiting error', async () => {
      const mobile = '9876543210';
      const password = 'password123';
      const error = {
        message: 'Too many requests',
        status: 429,
      };
      const mockResult = {
        data: { user: null, session: null },
        error,
      };

      mockSupabase.auth.signInWithPassword.mockResolvedValue(mockResult);

      const result = await MobileAuthService.signInWithMobilePassword(mobile, password);

      expect(result.error.message).toBe('Too many requests');
    });

    it('should handle empty mobile number', async () => {
      const mobile = '';
      const password = 'password123';
      
      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: null, session: null },
        error: null,
      });

      await MobileAuthService.signInWithMobilePassword(mobile, password);

      expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
        phone: '+91',
        password: 'password123',
      });
    });

    it('should handle empty password', async () => {
      const mobile = '9876543210';
      const password = '';
      
      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: null, session: null },
        error: null,
      });

      await MobileAuthService.signInWithMobilePassword(mobile, password);

      expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
        phone: '+919876543210',
        password: '',
      });
    });

    it('should handle mobile number with formatting', async () => {
      const mobile = '************'; // With dashes
      const password = 'password123';
      
      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: null, session: null },
        error: null,
      });

      await MobileAuthService.signInWithMobilePassword(mobile, password);

      expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
        phone: '+91************',
        password: 'password123',
      });
    });

    it('should handle successful sign in with complete user data', async () => {
      const mobile = '9876543210';
      const password = 'password123';
      const mockUser = {
        id: 'user123',
        phone: '+919876543210',
        email: '<EMAIL>',
        created_at: '2024-01-01T00:00:00.000Z',
        updated_at: '2024-01-01T00:00:00.000Z',
        user_metadata: {},
        app_metadata: {},
      };
      const mockSession = {
        access_token: 'mock_access_token',
        refresh_token: 'mock_refresh_token',
        expires_in: 3600,
        token_type: 'bearer',
        user: mockUser,
      };
      const mockResult = {
        data: {
          user: mockUser,
          session: mockSession,
        },
        error: null,
      };

      mockSupabase.auth.signInWithPassword.mockResolvedValue(mockResult);

      const result = await MobileAuthService.signInWithMobilePassword(mobile, password);

      expect(result.data.user).toEqual(mockUser);
      expect(result.data.session).toEqual(mockSession);
      expect(result.error).toBeNull();
    });

    it('should handle authentication with custom error codes', async () => {
      const mobile = '9876543210';
      const password = 'password123';
      const error = {
        message: 'Phone not confirmed',
        status: 422,
        code: 'phone_not_confirmed',
      };
      const mockResult = {
        data: { user: null, session: null },
        error,
      };

      mockSupabase.auth.signInWithPassword.mockResolvedValue(mockResult);

      const result = await MobileAuthService.signInWithMobilePassword(mobile, password);

      expect(result.error.code).toBe('phone_not_confirmed');
      expect(result.error.message).toBe('Phone not confirmed');
    });

    it('should always add +91 prefix regardless of input format', async () => {
      const testCases = [
        { input: '9876543210', expected: '+919876543210' },
        { input: '************', expected: '+91************' },
        { input: '************', expected: '+91************' },
        { input: '0987654321', expected: '+910987654321' }, // Leading zero
      ];

      for (const testCase of testCases) {
        jest.clearAllMocks();
        mockSupabase.auth.signInWithPassword.mockResolvedValue({
          data: { user: null, session: null },
          error: null,
        });

        await MobileAuthService.signInWithMobilePassword(testCase.input, 'password123');

        expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
          phone: testCase.expected,
          password: 'password123',
        });
      }
    });

    it('should handle concurrent sign-in requests', async () => {
      const mobile = '9876543210';
      const password = 'password123';
      
      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: { id: 'user123' }, session: {} },
        error: null,
      });

      // Simulate concurrent requests
      const promises = [
        MobileAuthService.signInWithMobilePassword(mobile, password),
        MobileAuthService.signInWithMobilePassword(mobile, password),
        MobileAuthService.signInWithMobilePassword(mobile, password),
      ];

      const results = await Promise.all(promises);

      expect(results).toHaveLength(3);
      expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledTimes(3);
      results.forEach(result => {
        expect(result.data.user.id).toBe('user123');
      });
    });
  });
});
