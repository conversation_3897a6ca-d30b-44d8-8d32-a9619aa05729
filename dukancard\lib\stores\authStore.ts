import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// Types for authentication state
interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  deviceId: string;
  deviceSecret: string;
  hmacKey: string;
}

interface AuthUser {
  id: string;
  email?: string;
  phone?: string;
  // Add other user properties as needed
}

interface AuthState {
  // Authentication state
  isAuthenticated: boolean;
  isLoading: boolean;
  user: AuthUser | null;
  tokens: AuthTokens | null;
  
  // Error states
  error: string | null;
  isRefreshing: boolean;
  
  // Actions
  login: (email: string, password: string, deviceInfo: any) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  refreshTokens: () => Promise<boolean>;
  setTokens: (tokens: AuthTokens) => void;
  setUser: (user: AuthUser | null) => void;
  clearAuth: () => void;
  clearError: () => void;
  
  // Utility functions
  isTokenValid: () => boolean;
  shouldRefreshToken: () => boolean;
  getAuthHeaders: () => Record<string, string>;
  getHMACHeaders: (method: string, path: string, body?: string) => Record<string, string>;
}

// API wrapper for authenticated requests
export class AuthenticatedApiClient {
  private static instance: AuthenticatedApiClient;
  private refreshPromise: Promise<boolean> | null = null;
  
  static getInstance(): AuthenticatedApiClient {
    if (!AuthenticatedApiClient.instance) {
      AuthenticatedApiClient.instance = new AuthenticatedApiClient();
    }
    return AuthenticatedApiClient.instance;
  }
  
  async fetch(url: string, options: RequestInit = {}): Promise<Response> {
    const store = useAuthStore.getState();

    // Proactively refresh token if needed (expires within 5 minutes)
    if (store.shouldRefreshToken() && !store.isRefreshing && !this.refreshPromise) {
      this.refreshPromise = store.refreshTokens();
      await this.refreshPromise;
      this.refreshPromise = null;
    }

    // Get updated store state after potential refresh
    const updatedStore = useAuthStore.getState();

    // Extract method and path for HMAC signing
    const method = options.method || 'GET';
    const urlObj = new URL(url);
    const path = urlObj.pathname;
    const body = options.body ? (typeof options.body === 'string' ? options.body : JSON.stringify(options.body)) : '';

    // Add authorization and HMAC headers
    const headers = {
      ...options.headers,
      ...updatedStore.getAuthHeaders(),
      ...updatedStore.getHMACHeaders(method, path, body),
    };

    let response = await fetch(url, {
      ...options,
      headers,
    });
    
    // If unauthorized and we have tokens, try to refresh
    if (response.status === 401 && updatedStore.tokens) {
      // If there's already a refresh in progress, wait for it
      if (this.refreshPromise) {
        await this.refreshPromise;
      } else if (!updatedStore.isRefreshing) {
        // Start a new refresh
        this.refreshPromise = updatedStore.refreshTokens();
        const refreshSuccess = await this.refreshPromise;
        this.refreshPromise = null;
        
        if (!refreshSuccess) {
          // Refresh failed, return the 401 response
          return response;
        }
      }
      
      // Get the updated store state and retry the request
      const finalStore = useAuthStore.getState();
      if (finalStore.tokens) {
        const newHeaders = {
          ...options.headers,
          ...finalStore.getAuthHeaders(),
        };
        
        response = await fetch(url, {
          ...options,
          headers: newHeaders,
        });
      }
    }
    
    return response;
  }
}

// Helper function to decode JWT payload (basic decode, not for verification)
function decodeJWTPayload(token: string): any {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) return null;
    
    const payload = parts[1];
    // Add padding if needed for base64 decoding
    const paddedPayload = payload + '='.repeat((4 - payload.length % 4) % 4);
    const decoded = atob(paddedPayload.replace(/-/g, '+').replace(/_/g, '/'));
    return JSON.parse(decoded);
  } catch (error) {
    console.error('Error decoding JWT:', error);
    return null;
  }
}

export const useAuthStore = create<AuthState>()(
  devtools(
    persist(
      immer((set: (fn: (draft: AuthState) => void) => void, get: () => AuthState) => ({
        // Initial state
        isAuthenticated: false,
        isLoading: false,
        user: null,
        tokens: null,
        error: null,
        isRefreshing: false,

        // Login action
        login: async (email: string, password: string, deviceInfo: any) => {
          set((state: AuthState) => {
            state.isLoading = true;
            state.error = null;
          });

          try {
            const response = await fetch('/api/auth/login', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                email,
                password,
                deviceInfo,
              }),
            });

            const data = await response.json();

            if (!response.ok) {
              set((state: AuthState) => {
                state.isLoading = false;
                state.error = data.error || 'Login failed';
              });
              
              return { success: false, error: data.error || 'Login failed' };
            }

            // Extract user info from access token
            const payload = decodeJWTPayload(data.accessToken);
            const user: AuthUser = {
              id: payload?.user_id || '',
              email: email,
              // Add phone if it's a phone-based login
              phone: email.startsWith('+91') ? email : undefined,
            };

            const tokens: AuthTokens = {
              accessToken: data.accessToken,
              refreshToken: data.refreshToken,
              deviceId: data.deviceId,
              deviceSecret: data.deviceSecret,
              hmacKey: data.hmacKey,
            };

            set((state: AuthState) => {
              state.isAuthenticated = true;
              state.isLoading = false;
              state.user = user;
              state.tokens = tokens;
              state.error = null;
            });

            return { success: true };
          } catch (error) {
            console.error('Login error:', error);
            set((state: AuthState) => {
              state.isLoading = false;
              state.error = 'An unexpected error occurred';
            });
            
            return { success: false, error: 'An unexpected error occurred' };
          }
        },

        // Logout action
        logout: async () => {
          const currentTokens = get().tokens;
          
          if (currentTokens) {
            try {
              // Call logout API
              const response = await fetch('/api/auth/logout', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': `Bearer ${currentTokens.accessToken}`,
                },
                body: JSON.stringify({
                  refreshToken: currentTokens.refreshToken,
                }),
              });

              // Continue with logout even if API call fails
              if (!response.ok) {
                console.warn('Logout API call failed, but continuing with local logout');
              }
            } catch (error) {
              console.warn('Error calling logout API:', error);
            }
          }

          set((state: AuthState) => {
            state.isAuthenticated = false;
            state.user = null;
            state.tokens = null;
            state.error = null;
            state.isRefreshing = false;
          });

          // Clear access token cookie
          if (typeof document !== 'undefined') {
            document.cookie = 'accessToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; secure; samesite=strict';
          }
        },

        // Refresh tokens action
        refreshTokens: async () => {
          const currentTokens = get().tokens;
          
          if (!currentTokens || get().isRefreshing) {
            return false;
          }

          set((state: AuthState) => {
            state.isRefreshing = true;
          });

          try {
            const response = await fetch('/api/auth/refresh', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                refreshToken: currentTokens.refreshToken,
                deviceId: currentTokens.deviceId,
              }),
            });

            const data = await response.json();

            if (!response.ok) {
              // Refresh failed, clear authentication
              set((state: AuthState) => {
                state.isAuthenticated = false;
                state.user = null;
                state.tokens = null;
                state.isRefreshing = false;
                state.error = 'Session expired. Please log in again.';
              });
              
              return false;
            }

            // Update tokens
            set((state: AuthState) => {
              if (state.tokens) {
                state.tokens.accessToken = data.accessToken;
                state.tokens.refreshToken = data.refreshToken;
              }
              state.isRefreshing = false;
            });

            // Update access token cookie
            if (typeof document !== 'undefined') {
              document.cookie = `accessToken=${data.accessToken}; path=/; max-age=${15 * 60}; secure; samesite=strict`;
            }

            return true;
          } catch (error) {
            console.error('Token refresh error:', error);
            set((state: AuthState) => {
              state.isAuthenticated = false;
              state.user = null;
              state.tokens = null;
              state.isRefreshing = false;
              state.error = 'Session expired. Please log in again.';
            });
            
            return false;
          }
        },

        // Set tokens (for external use)
        setTokens: (tokens: AuthTokens) => {
          set((state: AuthState) => {
            state.tokens = tokens;
            state.isAuthenticated = true;
          });
          
          // For web client session persistence, store access token in cookie
          // This allows the middleware to validate the session
          if (typeof document !== 'undefined') {
            document.cookie = `accessToken=${tokens.accessToken}; path=/; max-age=${15 * 60}; secure; samesite=strict`;
          }
        },

        // Set user (for external use)
        setUser: (user: AuthUser | null) => {
          set((state: AuthState) => {
            state.user = user;
          });
        },

        // Clear authentication
        clearAuth: () => {
          set((state: AuthState) => {
            state.isAuthenticated = false;
            state.user = null;
            state.tokens = null;
            state.error = null;
            state.isRefreshing = false;
          });

          // Clear access token cookie
          if (typeof document !== 'undefined') {
            document.cookie = 'accessToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; secure; samesite=strict';
          }
        },

        // Clear error
        clearError: () => {
          set((state: AuthState) => {
            state.error = null;
          });
        },

        // Check if current token is valid (not expired)
        isTokenValid: () => {
          const tokens = get().tokens;
          if (!tokens?.accessToken) return false;

          const payload = decodeJWTPayload(tokens.accessToken);
          if (!payload || !payload.exp) return false;

          const now = Math.floor(Date.now() / 1000);
          return payload.exp > now;
        },

        // Check if token needs refresh (expires within 5 minutes)
        shouldRefreshToken: () => {
          const tokens = get().tokens;
          if (!tokens?.accessToken) return false;

          const payload = decodeJWTPayload(tokens.accessToken);
          if (!payload || !payload.exp) return false;

          const now = Math.floor(Date.now() / 1000);
          const fiveMinutesFromNow = now + (5 * 60); // 5 minutes in seconds
          return payload.exp < fiveMinutesFromNow;
        },

        // Get auth headers for API requests
        getAuthHeaders: (): Record<string, string> => {
          const tokens = get().tokens;
          if (!tokens?.accessToken) return {};

          return {
            'Authorization': `Bearer ${tokens.accessToken}`,
          };
        },

        // Get HMAC headers for API requests
        getHMACHeaders: (method: string, path: string, body: string = ''): Record<string, string> => {
          const tokens = get().tokens;
          if (!tokens?.deviceId || !tokens?.hmacKey) return {};

          const timestamp = Date.now().toString();

          // Import crypto for browser environment
          if (typeof window !== 'undefined') {
            // For browser environment, we'll need to use Web Crypto API or a polyfill
            // For now, return empty headers and we'll implement this properly
            console.warn('HMAC signing not yet implemented for browser environment');
            return {
              'X-Device-Id': tokens.deviceId,
              'X-Timestamp': timestamp,
              'X-Signature': 'browser-not-implemented', // TODO: Implement browser-compatible HMAC signing
            };
          }

          // For server-side rendering, we can use Node.js crypto
          try {
            const crypto = require('crypto');

            // Create SHA256 hash of the request body
            const bodyHash = crypto
              .createHash('sha256')
              .update(body || '')
              .digest('hex');

            // Create the string to be signed: method + path + timestamp + bodyHash
            const stringToSign = `${method.toUpperCase()}${path}${timestamp}${bodyHash}`;

            // Generate HMAC-SHA256 signature using HMAC key
            const signature = crypto
              .createHmac('sha256', tokens.hmacKey)
              .update(stringToSign)
              .digest('base64');

            return {
              'X-Device-Id': tokens.deviceId,
              'X-Timestamp': timestamp,
              'X-Signature': signature,
            };
          } catch (error) {
            console.error('Error generating HMAC signature:', error);
            return {};
          }
        },
      })),
      {
        name: 'auth-store',
        partialize: (state: AuthState) => ({
          // Persist authentication state but not loading states
          isAuthenticated: state.isAuthenticated,
          user: state.user,
          tokens: state.tokens,
          // Don't persist error, isLoading, or isRefreshing
        }),
      }
    ),
    {
      name: 'auth-store',
    }
  )
);

// Helper hook for authenticated API calls
export const useAuthenticatedApi = () => {
  return AuthenticatedApiClient.getInstance();
};