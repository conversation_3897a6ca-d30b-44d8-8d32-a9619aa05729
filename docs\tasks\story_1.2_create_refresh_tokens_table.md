### User Story 1.2: Create `refresh_tokens` Table Schema

**Description:** This story creates the `refresh_tokens` table, which is essential for the security feature of token rotation and persistent, long-lived user sessions.

**Acceptance Criteria:**
- A new, reversible SQL migration for the `refresh_tokens` table is created.
- The table is correctly linked to the `devices` table.
- The migration successfully applies to a local development database.

---

### Development Tasks

-   [ ] **1. Create a new migration file**
    *   In the `dukancard` project directory, run the following Supabase CLI command:
    *   **Command:** `supabase migration new create_refresh_tokens_table`

-   [ ] **2. Define the 'Up' Migration (Create Table)**
    *   Open the newly generated SQL file in `supabase/migrations/`.
    *   Add the following SQL script to define the `refresh_tokens` table and its indexes.

    ```sql
    -- supabase/migrations/{timestamp}_create_refresh_tokens_table.sql

    CREATE TABLE public.refresh_tokens (
        id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
        device_id uuid NOT NULL REFERENCES public.devices(device_id) ON DELETE CASCADE,
        refresh_token_hash text NOT NULL,
        issued_at timestamptz NOT NULL DEFAULT now(),
        expires_at timestamptz,
        rotated_from uuid, -- Stores the ID of the token this one was rotated from, for reuse detection
        revoked boolean NOT NULL DEFAULT false
    );

    -- Add index for faster lookups on device_id
    CREATE INDEX idx_refresh_tokens_device_id ON public.refresh_tokens(device_id);

    -- Add comments for clarity
    COMMENT ON TABLE public.refresh_tokens IS 'Stores rotated refresh tokens for user sessions.';
    COMMENT ON COLUMN public.refresh_tokens.rotated_from IS 'For detecting refresh token reuse.';
    ```

-   [ ] **3. Document the 'Down' Migration (Drop Table)**
    *   Add the corresponding `DROP TABLE` statement as a comment in the migration file for documentation purposes.
    ```sql
    -- Down Migration
    -- DROP TABLE public.refresh_tokens;
    ```

-   [ ] **4. Unit Test the Migration**
    *   **Task:** Apply the migration to your local development database.
    *   **Command:** `supabase db reset`
    *   **Verification:**
        *   After the command completes, connect to your local database.
        *   Verify that the `public.refresh_tokens` table exists and that all columns, constraints, and indexes were created correctly.
        *   Confirm the foreign key to `public.devices` is in place.