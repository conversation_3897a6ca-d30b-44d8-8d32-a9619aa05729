import { POST } from '@/app/api/auth/refresh/route';

// Mock the dependencies
jest.mock('@/lib/auth/jwt', () => ({
  generateAccessToken: jest.fn().mockReturnValue('new-access-token'),
  generateRefreshToken: jest.fn().mockReturnValue('new-refresh-token'),
}));

jest.mock('@/lib/security/hashing', () => ({
  hashSecret: jest.fn().mockResolvedValue('new-hashed-token'),
  compareSecret: jest.fn(),
}));

jest.mock('@/utils/supabase/service-role', () => ({
  createServiceRoleClient: jest.fn(),
}));

import { generateAccessToken, generateRefreshToken } from '@/lib/auth/jwt';
import { hashSecret, compareSecret } from '@/lib/security/hashing';
import { createServiceRoleClient } from '@/utils/supabase/service-role';

describe('/api/auth/refresh', () => {
  let mockSupabase: any;
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Create a mock Supabase client with simplified structure
    mockSupabase = {
      from: jest.fn(),
    };
    
    (createServiceRoleClient as jest.Mock).mockReturnValue(mockSupabase);
  });

  describe('POST', () => {
    it('should successfully refresh tokens for valid request', async () => {
      const deviceId = '550e8400-e29b-41d4-a716-************'; // Valid UUID
      const userId = '550e8400-e29b-41d4-a716-************';
      
      const mockToken = {
        token_id: 'token-1',
        token_hash: 'hashed-token',
        revoked: false,
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 1 day from now
      };
      
      // Mock the from() method to return different query chains based on table
      mockSupabase.from.mockImplementation((table: string) => {
        if (table === 'devices') {
          return {
            select: () => ({
              eq: () => ({
                single: jest.fn().mockResolvedValue({
                  data: { user_id: userId, device_id: deviceId },
                  error: null,
                })
              })
            }),
            update: () => ({
              eq: jest.fn().mockResolvedValue({ error: null })
            })
          };
        } else if (table === 'refresh_tokens') {
          return {
            select: () => ({
              eq: () => ({
                eq: jest.fn().mockResolvedValue({
                  data: [mockToken],
                  error: null,
                })
              }),
              single: jest.fn().mockResolvedValue({
                data: { token_hash: 'revoked-token-hash' },
                error: null,
              })
            }),
            update: () => ({
              eq: jest.fn().mockResolvedValue({ error: null })
            }),
            insert: jest.fn().mockResolvedValue({ error: null })
          };
        }
      });
      
      // Mock successful token comparison
      (compareSecret as jest.Mock).mockResolvedValue(true);

      const request = {
        json: jest.fn().mockResolvedValue({
          refreshToken: 'valid-refresh-token',
          deviceId: deviceId,
        }),
      } as any;

      const response = await POST(request);
      const responseData = await response.json();

      // Debug logging
      if (response.status !== 200) {
        console.log('Response status:', response.status);
        console.log('Response data:', responseData);
      }

      expect(response.status).toBe(200);
      expect(responseData.accessToken).toBe('new-access-token');
      expect(responseData.refreshToken).toBe('new-refresh-token');
      
      // Verify token comparison was called
      expect(compareSecret).toHaveBeenCalledWith('valid-refresh-token', 'hashed-token');
      
      // Verify new tokens were generated
      expect(generateAccessToken).toHaveBeenCalledWith(userId, []);
      expect(generateRefreshToken).toHaveBeenCalled();
      
      // Verify Supabase methods were called
      expect(mockSupabase.from).toHaveBeenCalledWith('devices');
      expect(mockSupabase.from).toHaveBeenCalledWith('refresh_tokens');
    });

    it('should return 401 for invalid device ID', async () => {
      // Mock device lookup failure
      mockSupabase.from.mockImplementation((table: string) => {
        if (table === 'devices') {
          return {
            select: () => ({
              eq: () => ({
                single: jest.fn().mockResolvedValue({
                  data: null,
                  error: { message: 'Device not found' },
                })
              })
            })
          };
        }
      });

      const request = {
        json: jest.fn().mockResolvedValue({
          refreshToken: 'valid-refresh-token',
          deviceId: '550e8400-e29b-41d4-a716-************',
        }),
      } as any;

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(401);
      expect(responseData.error).toBe('Invalid device ID');
    });

    it('should return 400 for invalid request body', async () => {
      const request = {
        json: jest.fn().mockResolvedValue({
          refreshToken: '', // Empty token
          deviceId: 'not-a-uuid', // Invalid UUID
        }),
      } as any;

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(400);
      expect(responseData.error).toBe('Validation failed');
      expect(responseData.details).toBeInstanceOf(Array);
    });

    it('should return 401 for expired refresh token', async () => {
      const deviceId = '550e8400-e29b-41d4-a716-************';
      const userId = '550e8400-e29b-41d4-a716-************';
      
      // Mock device lookup
      mockSupabase.mockDeviceSelect.mockResolvedValue({
        data: { user_id: userId, device_id: deviceId },
        error: null,
      });
      
      // Mock expired token
      const expiredToken = {
        token_id: 'token-1',
        token_hash: 'hashed-token',
        revoked: false,
        expires_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
      };
      mockSupabase.mockRefreshTokenSelect.mockResolvedValue({
        data: [expiredToken],
        error: null,
      });
      
      // Mock token comparison success
      (compareSecret as jest.Mock).mockResolvedValue(true);

      const request = {
        json: jest.fn().mockResolvedValue({
          refreshToken: 'expired-refresh-token',
          deviceId: deviceId,
        }),
      } as any;

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(401);
      expect(responseData.error).toBe('Refresh token expired');
    });

    it('should return 401 for invalid refresh token', async () => {
      const deviceId = '550e8400-e29b-41d4-a716-************';
      const userId = '550e8400-e29b-41d4-a716-************';
      
      // Mock device lookup
      mockSupabase.mockDeviceSelect.mockResolvedValue({
        data: { user_id: userId, device_id: deviceId },
        error: null,
      });
      
      // Mock active tokens
      mockSupabase.mockRefreshTokenSelect.mockResolvedValue({
        data: [
          {
            token_id: 'token-1',
            token_hash: 'different-hash',
            revoked: false,
            expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          }
        ],
        error: null,
      });
      
      // Mock token comparison failure
      (compareSecret as jest.Mock).mockResolvedValue(false);

      const request = {
        json: jest.fn().mockResolvedValue({
          refreshToken: 'invalid-refresh-token',
          deviceId: deviceId,
        }),
      } as any;

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(401);
      expect(responseData.error).toBe('Invalid refresh token');
    });

    it('should handle token reuse detection and revoke all device tokens', async () => {
      const deviceId = '550e8400-e29b-41d4-a716-************';
      const userId = '550e8400-e29b-41d4-a716-************';
      
      // Mock device lookup
      mockSupabase.mockDeviceSelect.mockResolvedValue({
        data: { user_id: userId, device_id: deviceId },
        error: null,
      });
      
      // Mock no active tokens found
      mockSupabase.mockRefreshTokenSelect
        .mockResolvedValueOnce({
          data: [], // No active tokens
          error: null,
        })
        .mockResolvedValueOnce({
          data: [{ token_id: 'revoked-token-1' }], // Revoked tokens found
          error: null,
        })
        .mockResolvedValueOnce({
          data: { token_hash: 'revoked-token-hash' }, // Single revoked token
          error: null,
        });
      
      // Mock token comparison - matches revoked token
      (compareSecret as jest.Mock).mockResolvedValue(true);
      
      // Mock revocation operations
      mockSupabase.mockRefreshTokenUpdate.mockResolvedValue({ error: null });
      mockSupabase.mockDeviceUpdate.mockResolvedValue({ error: null });

      const request = {
        json: jest.fn().mockResolvedValue({
          refreshToken: 'reused-refresh-token',
          deviceId: deviceId,
        }),
      } as any;

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(409);
      expect(responseData.error).toBe('Token reuse detected - all device tokens have been revoked');
      
      // Verify all tokens were revoked
      expect(mockSupabase.mockRefreshTokenUpdate).toHaveBeenCalledWith({
        revoked: true,
        revoked_at: expect.any(String),
      });
      
      // Verify device was revoked
      expect(mockSupabase.mockDeviceUpdate).toHaveBeenCalledWith({
        revoked: true,
        revoked_at: expect.any(String),
      });
    });

    it('should return 500 for database errors', async () => {
      const deviceId = '550e8400-e29b-41d4-a716-************';
      const userId = '550e8400-e29b-41d4-a716-************';
      
      // Mock successful device lookup
      mockSupabase.mockDeviceSelect.mockResolvedValue({
        data: { user_id: userId, device_id: deviceId },
        error: null,
      });
      
      // But mock refresh token fetch error
      mockSupabase.mockRefreshTokenSelect.mockResolvedValue({
        data: null,
        error: { message: 'Database error' },
      });

      const request = {
        json: jest.fn().mockResolvedValue({
          refreshToken: 'valid-refresh-token',
          deviceId: deviceId,
        }),
      } as any;

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(500);
      expect(responseData.error).toBe('Internal Server Error');
    });
  });
});