### User Story 5.4: Refactor Mobile Product & Posts Features

*   **User Story:** As a mobile user, I want to interact with products and view posts, with all interactions being securely handled by the backend API.
*   **Acceptance Criteria:**
    *   All product and post-related features in the mobile app work as expected.
    *   These features now exclusively use the new secure API for data operations.
    *   All direct Supabase SDK calls for these features have been removed.

---

### Development Tasks

-   [ ] **1. Identify Relevant Mobile Modules**
    *   **Developer's Task:** Locate all mobile screens, components, and data services within the `dukancard-app` codebase that currently interact with `products_services`, `business_posts`, and `customer_posts`.

-   [ ] **2. Refactor Data Fetching for Products and Posts**
    *   **Developer's Task:** Replace all direct Supabase client calls used for **fetching** product and post data with calls to the new centralized API client (from Story 5.2). Ensure that the data received from the API is correctly parsed and displayed in the UI.

-   [ ] **3. Refactor Data Mutation for Products and Posts**
    *   **Developer's Task:** Replace all direct Supabase client calls used for **creating, updating, or deleting** product and post data with calls to the new centralized API client. Ensure that forms and user interactions correctly send data to the API and handle responses (success, error, loading states).

-   [ ] **4. Update UI to Reflect API Responses**
    *   **Developer's Task:** Verify that the mobile UI correctly displays data received from the new API. This includes handling potential differences in data structure, loading indicators, and error messages returned by the API.