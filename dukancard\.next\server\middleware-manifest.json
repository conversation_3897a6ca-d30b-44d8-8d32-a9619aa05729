{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_9dd07395._.js", "server/edge/chunks/node_modules_@supabase_auth-js_dist_module_17bbb6b5._.js", "server/edge/chunks/node_modules_@upstash_redis_b3b75fae._.js", "server/edge/chunks/node_modules_b0831d12._.js", "server/edge/chunks/[root-of-the-server]__0ee7c35c._.js", "server/edge/chunks/edge-wrapper_36e49a54.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ENoSsqhUZznD9fxor6afRENMBt9QqLrvjNeiFMwX4Wk=", "__NEXT_PREVIEW_MODE_ID": "a67489c21ac697e2704435caa7fee467", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e6df0784dc004a26dcae46538de285aa31f622c744e71fbf49fe778db4106864", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "28c3c032546bfc2f41327708f5d79b6cf55961240c8c98d98e685b83b0b6f502"}}}, "sortedMiddleware": ["/"], "functions": {}}