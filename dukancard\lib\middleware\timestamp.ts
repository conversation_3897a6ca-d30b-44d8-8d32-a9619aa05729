import { NextRequest, NextResponse } from 'next/server';

export interface TimestampValidationResult {
  success: boolean;
  error?: string;
  status?: number;
}

/**
 * Configuration for timestamp validation
 */
export interface TimestampConfig {
  /** Time window in seconds (default: 120) */
  windowSeconds?: number;
  /** Whether timestamp validation is required (default: true) */
  required?: boolean;
}

/**
 * Validates request timestamp to prevent replay attacks
 * @param timestamp - Unix timestamp in milliseconds as string
 * @param config - Validation configuration
 * @returns Validation result
 */
export function validateRequestTimestamp(
  timestamp: string | null,
  config: TimestampConfig = {}
): TimestampValidationResult {
  const { windowSeconds = 120, required = true } = config;

  // Skip validation if not required
  if (!required) {
    return { success: true };
  }

  // Check if timestamp is provided
  if (timestamp === null || timestamp === undefined) {
    return {
      success: false,
      error: 'Missing X-Timestamp header',
      status: 400,
    };
  }

  // Parse timestamp
  const requestTime = parseInt(timestamp, 10);
  if (isNaN(requestTime) || timestamp.trim() === '') {
    return {
      success: false,
      error: 'Invalid timestamp format',
      status: 400,
    };
  }

  // Get current server time (UTC)
  const currentTime = Date.now();
  const windowMs = windowSeconds * 1000;

  // Check if request is within the allowed time window
  const timeDiff = Math.abs(currentTime - requestTime);
  
  if (timeDiff > windowMs) {
    return {
      success: false,
      error: 'Request has expired',
      status: 408,
    };
  }

  return { success: true };
}

/**
 * Middleware function for timestamp validation
 * @param req - Next.js request object
 * @param config - Validation configuration
 * @returns Validation result
 */
export async function timestampMiddleware(
  req: NextRequest,
  config: TimestampConfig = {}
): Promise<TimestampValidationResult> {
  try {
    // Extract timestamp from X-Timestamp header
    const timestamp = req.headers.get('x-timestamp');
    
    // Validate the timestamp
    return validateRequestTimestamp(timestamp, config);
    
  } catch (error) {
    console.error('Unexpected error in timestamp validation:', error);
    return {
      success: false,
      error: 'Internal Server Error',
      status: 500,
    };
  }
}

/**
 * Next.js middleware wrapper for timestamp validation
 * @param req - Next.js request object
 * @param config - Validation configuration
 * @returns NextResponse or null to continue
 */
export async function timestampValidationMiddleware(
  req: NextRequest,
  config: TimestampConfig = {}
): Promise<NextResponse | null> {
  const result = await timestampMiddleware(req, config);
  
  if (!result.success) {
    return new NextResponse(JSON.stringify({ error: result.error }), {
      status: result.status || 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
  
  return null; // Continue to next middleware/handler
}

/**
 * Get the current server timestamp in milliseconds
 * Useful for clients to synchronize their timestamps
 * @returns Current UTC timestamp in milliseconds
 */
export function getServerTimestamp(): number {
  return Date.now();
}

/**
 * Check if a timestamp is within the allowed window
 * @param timestamp - Timestamp to check (in milliseconds)
 * @param windowSeconds - Allowed window in seconds
 * @returns true if within window, false otherwise
 */
export function isTimestampFresh(
  timestamp: number,
  windowSeconds: number = 120
): boolean {
  const currentTime = Date.now();
  const windowMs = windowSeconds * 1000;
  const timeDiff = Math.abs(currentTime - timestamp);
  
  return timeDiff <= windowMs;
}